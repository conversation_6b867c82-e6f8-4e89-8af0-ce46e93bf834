#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

// 包含 RVV 优化头文件
#include "../ggml/src/ggml-cpu/arch/riscv/rvv-simd.h"

// 外部测试函数声明
extern void run_rvv_tests(void);

// ================================================================================================
// RVV 优化测试主程序
// ================================================================================================

static void print_system_info(void) {
    printf("=== 系统信息 ===\n");
    
    // 检查 RISC-V 架构
    #ifdef __riscv
    printf("架构: RISC-V\n");
    
    #ifdef __riscv_xlen
    printf("XLEN: %d 位\n", __riscv_xlen);
    #endif
    
    #ifdef __riscv_v
    printf("向量扩展: 支持\n");
    if (RVV_AVAILABLE()) {
        printf("向量长度: %zu 字节 (%zu 个 float32 元素)\n", 
               RVV_VLENB(), RVV_VL_F32());
    } else {
        printf("向量长度: 运行时不可用\n");
    }
    #else
    printf("向量扩展: 不支持\n");
    #endif
    
    #else
    printf("架构: 非 RISC-V (模拟测试)\n");
    #endif
    
    printf("CPU 核心数: %ld\n", sysconf(_SC_NPROCESSORS_ONLN));
    printf("\n");
}

static void print_optimization_features(void) {
    printf("=== RVV 优化特性 ===\n");
    
    printf("✓ 激活函数优化:\n");
    printf("  - ReLU (修正线性单元)\n");
    printf("  - GELU (高斯误差线性单元)\n");
    printf("  - SiLU (Sigmoid 线性单元)\n");
    printf("  - Leaky ReLU\n");
    
    printf("✓ 矩阵运算优化:\n");
    printf("  - GEMM (通用矩阵乘法)\n");
    printf("  - GEMV (矩阵向量乘法)\n");
    printf("  - 分块优化和循环展开\n");
    
    printf("✓ 数学运算优化:\n");
    printf("  - Softmax\n");
    printf("  - Layer Normalization\n");
    printf("  - RMS Normalization\n");
    
    printf("✓ 注意力机制优化:\n");
    printf("  - Scaled Dot-Product Attention\n");
    printf("  - Multi-Head Attention\n");
    printf("  - 位置编码\n");
    printf("  - 嵌入层查找\n");
    
    printf("✓ 多核并行优化:\n");
    printf("  - 多线程 GEMM\n");
    printf("  - 并行向量操作\n");
    printf("  - 负载均衡\n");
    printf("  - 流水线并行\n");
    
    printf("\n");
}

static void benchmark_memory_bandwidth(void) {
    printf("=== 内存带宽基准测试 ===\n");
    
    const int sizes[] = {1024, 4096, 16384, 65536, 262144};
    const int num_sizes = sizeof(sizes) / sizeof(sizes[0]);
    const int iterations = 1000;
    
    printf("%-10s %-15s %-15s %-15s\n", "大小", "标量 (GB/s)", "RVV (GB/s)", "加速比");
    printf("--------------------------------------------------------\n");
    
    for (int i = 0; i < num_sizes; i++) {
        int n = sizes[i];
        float* src = malloc(n * sizeof(float));
        float* dst = malloc(n * sizeof(float));
        
        if (!src || !dst) {
            printf("内存分配失败\n");
            continue;
        }
        
        // 初始化数据
        for (int j = 0; j < n; j++) {
            src[j] = (float)j;
        }
        
        // 标量复制基准
        struct timespec start, end;
        clock_gettime(CLOCK_MONOTONIC, &start);
        
        for (int iter = 0; iter < iterations; iter++) {
            for (int j = 0; j < n; j++) {
                dst[j] = src[j];
            }
        }
        
        clock_gettime(CLOCK_MONOTONIC, &end);
        double scalar_time = (end.tv_sec - start.tv_sec) + 
                           (end.tv_nsec - start.tv_nsec) * 1e-9;
        
        // RVV 复制基准
        rvv_config_t config = rvv_get_default_config();
        
        clock_gettime(CLOCK_MONOTONIC, &start);
        
        for (int iter = 0; iter < iterations; iter++) {
            rvv_vec_add_f32(n, dst, src, src, &config); // 使用加法模拟复制
        }
        
        clock_gettime(CLOCK_MONOTONIC, &end);
        double rvv_time = (end.tv_sec - start.tv_sec) + 
                         (end.tv_nsec - start.tv_nsec) * 1e-9;
        
        // 计算带宽 (读取 + 写入)
        double bytes_transferred = (double)n * sizeof(float) * 2 * iterations;
        double scalar_bandwidth = bytes_transferred / scalar_time / 1e9;
        double rvv_bandwidth = bytes_transferred / rvv_time / 1e9;
        double speedup = rvv_bandwidth / scalar_bandwidth;
        
        printf("%-10d %-15.2f %-15.2f %-15.2fx\n", 
               n, scalar_bandwidth, rvv_bandwidth, speedup);
        
        free(src);
        free(dst);
    }
    
    printf("\n");
}

static void test_multicore_scaling(void) {
    printf("=== 多核扩展性测试 ===\n");
    
    const int matrix_size = 512;
    const int max_threads = sysconf(_SC_NPROCESSORS_ONLN);
    
    float* A = malloc(matrix_size * matrix_size * sizeof(float));
    float* B = malloc(matrix_size * matrix_size * sizeof(float));
    float* C = malloc(matrix_size * matrix_size * sizeof(float));
    
    if (!A || !B || !C) {
        printf("内存分配失败\n");
        return;
    }
    
    // 初始化矩阵
    for (int i = 0; i < matrix_size * matrix_size; i++) {
        A[i] = (float)rand() / RAND_MAX;
        B[i] = (float)rand() / RAND_MAX;
        C[i] = 0.0f;
    }
    
    printf("矩阵大小: %dx%d\n", matrix_size, matrix_size);
    printf("%-8s %-15s %-15s %-15s\n", "线程数", "时间 (ms)", "GFLOPS", "效率");
    printf("--------------------------------------------------------\n");
    
    double single_thread_time = 0;
    
    for (int num_threads = 1; num_threads <= max_threads; num_threads++) {
        rvv_config_t config = rvv_get_default_config();
        config.num_threads = num_threads;
        
        struct timespec start, end;
        clock_gettime(CLOCK_MONOTONIC, &start);
        
        rvv_gemm_f32_multicore(matrix_size, matrix_size, matrix_size, 
                              C, A, B, 1.0f, 0.0f, &config);
        
        clock_gettime(CLOCK_MONOTONIC, &end);
        double time = (end.tv_sec - start.tv_sec) + 
                     (end.tv_nsec - start.tv_nsec) * 1e-9;
        
        if (num_threads == 1) {
            single_thread_time = time;
        }
        
        // 计算 GFLOPS (2 * m * n * k 次浮点运算)
        double flops = 2.0 * matrix_size * matrix_size * matrix_size;
        double gflops = flops / time / 1e9;
        double efficiency = single_thread_time / (time * num_threads) * 100;
        
        printf("%-8d %-15.3f %-15.2f %-15.1f%%\n", 
               num_threads, time * 1000, gflops, efficiency);
    }
    
    free(A);
    free(B);
    free(C);
    printf("\n");
}

static void print_usage(const char* program_name) {
    printf("用法: %s [选项]\n", program_name);
    printf("选项:\n");
    printf("  -h, --help     显示此帮助信息\n");
    printf("  -i, --info     显示系统信息\n");
    printf("  -f, --features 显示优化特性\n");
    printf("  -t, --test     运行功能测试\n");
    printf("  -b, --bench    运行性能基准测试\n");
    printf("  -m, --multicore 运行多核扩展性测试\n");
    printf("  -a, --all      运行所有测试\n");
}

int main(int argc, char* argv[]) {
    printf("RISC-V Vector Extension (RVV) 优化测试程序\n");
    printf("llama.cpp RVV 优化实现验证\n");
    printf("========================================\n\n");
    
    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }
    
    bool run_info = false;
    bool run_features = false;
    bool run_tests = false;
    bool run_bench = false;
    bool run_multicore = false;
    
    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
            print_usage(argv[0]);
            return 0;
        } else if (strcmp(argv[i], "-i") == 0 || strcmp(argv[i], "--info") == 0) {
            run_info = true;
        } else if (strcmp(argv[i], "-f") == 0 || strcmp(argv[i], "--features") == 0) {
            run_features = true;
        } else if (strcmp(argv[i], "-t") == 0 || strcmp(argv[i], "--test") == 0) {
            run_tests = true;
        } else if (strcmp(argv[i], "-b") == 0 || strcmp(argv[i], "--bench") == 0) {
            run_bench = true;
        } else if (strcmp(argv[i], "-m") == 0 || strcmp(argv[i], "--multicore") == 0) {
            run_multicore = true;
        } else if (strcmp(argv[i], "-a") == 0 || strcmp(argv[i], "--all") == 0) {
            run_info = run_features = run_tests = run_bench = run_multicore = true;
        } else {
            printf("未知选项: %s\n", argv[i]);
            print_usage(argv[0]);
            return 1;
        }
    }
    
    if (run_info) {
        print_system_info();
    }
    
    if (run_features) {
        print_optimization_features();
    }
    
    if (run_tests) {
        run_rvv_tests();
        printf("\n");
    }
    
    if (run_bench) {
        benchmark_memory_bandwidth();
    }
    
    if (run_multicore) {
        test_multicore_scaling();
    }
    
    printf("测试完成！\n");
    return 0;
}
