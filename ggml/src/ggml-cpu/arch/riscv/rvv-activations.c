#include "rvv-simd.h"
#include <math.h>
#include <string.h>

#ifdef __riscv_v

// ================================================================================================
// ReLU 激活函数 - RISC-V Vector Extension 优化实现
// ================================================================================================

void rvv_relu_f32(int n, float* dst, const float* src, const rvv_config_t* config) {
    if (!RVV_AVAILABLE() || n < RVV_MIN_VEC_SIZE) {
        // 回退到标量实现
        for (int i = 0; i < n; i++) {
            dst[i] = fmaxf(0.0f, src[i]);
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    int i = 0;

    // 向量化主循环
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        
        // 加载输入向量
        vfloat32m1_t v_src = __riscv_vle32_v_f32m1(src + i, vl);
        
        // 创建零向量
        vfloat32m1_t v_zero = __riscv_vfmv_v_f_f32m1(0.0f, vl);
        
        // 执行 ReLU: max(0, x)
        vfloat32m1_t v_result = __riscv_vfmax_vv_f32m1(v_src, v_zero, vl);
        
        // 存储结果
        __riscv_vse32_v_f32m1(dst + i, v_result, vl);
    }

    // 处理剩余元素
    for (; i < n; i++) {
        dst[i] = fmaxf(0.0f, src[i]);
    }
}

// ================================================================================================
// GELU 激活函数 - RISC-V Vector Extension 优化实现
// ================================================================================================

void rvv_gelu_f32(int n, float* dst, const float* src, const rvv_config_t* config) {
    if (!RVV_AVAILABLE() || n < RVV_MIN_VEC_SIZE) {
        // 回退到标量实现
        const float GELU_COEF_A = 0.044715f;
        const float SQRT_2_OVER_PI = 0.79788456080286535587989211986876f;
        
        for (int i = 0; i < n; i++) {
            float x = src[i];
            dst[i] = 0.5f * x * (1.0f + tanhf(SQRT_2_OVER_PI * x * (1.0f + GELU_COEF_A * x * x)));
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    const float GELU_COEF_A = 0.044715f;
    const float SQRT_2_OVER_PI = 0.79788456080286535587989211986876f;
    const float HALF = 0.5f;
    const float ONE = 1.0f;
    
    int i = 0;

    // 向量化主循环
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        
        // 加载输入向量
        vfloat32m1_t v_x = __riscv_vle32_v_f32m1(src + i, vl);
        
        // 创建常数向量
        vfloat32m1_t v_coef_a = __riscv_vfmv_v_f_f32m1(GELU_COEF_A, vl);
        vfloat32m1_t v_sqrt_2_pi = __riscv_vfmv_v_f_f32m1(SQRT_2_OVER_PI, vl);
        vfloat32m1_t v_half = __riscv_vfmv_v_f_f32m1(HALF, vl);
        vfloat32m1_t v_one = __riscv_vfmv_v_f_f32m1(ONE, vl);
        
        // 计算 x^2
        vfloat32m1_t v_x2 = __riscv_vfmul_vv_f32m1(v_x, v_x, vl);
        
        // 计算 GELU_COEF_A * x^2
        vfloat32m1_t v_coef_x2 = __riscv_vfmul_vv_f32m1(v_coef_a, v_x2, vl);
        
        // 计算 1 + GELU_COEF_A * x^2
        vfloat32m1_t v_inner = __riscv_vfadd_vv_f32m1(v_one, v_coef_x2, vl);
        
        // 计算 x * (1 + GELU_COEF_A * x^2)
        vfloat32m1_t v_x_inner = __riscv_vfmul_vv_f32m1(v_x, v_inner, vl);
        
        // 计算 SQRT_2_OVER_PI * x * (1 + GELU_COEF_A * x^2)
        vfloat32m1_t v_tanh_arg = __riscv_vfmul_vv_f32m1(v_sqrt_2_pi, v_x_inner, vl);
        
        // 计算 tanh (需要标量回退，因为 RVV 没有直接的 tanh 指令)
        float tanh_results[vl_max];
        __riscv_vse32_v_f32m1(tanh_results, v_tanh_arg, vl);
        for (size_t j = 0; j < vl; j++) {
            tanh_results[j] = tanhf(tanh_results[j]);
        }
        vfloat32m1_t v_tanh = __riscv_vle32_v_f32m1(tanh_results, vl);
        
        // 计算 1 + tanh(...)
        vfloat32m1_t v_one_plus_tanh = __riscv_vfadd_vv_f32m1(v_one, v_tanh, vl);
        
        // 计算 x * (1 + tanh(...))
        vfloat32m1_t v_x_tanh = __riscv_vfmul_vv_f32m1(v_x, v_one_plus_tanh, vl);
        
        // 计算最终结果: 0.5 * x * (1 + tanh(...))
        vfloat32m1_t v_result = __riscv_vfmul_vv_f32m1(v_half, v_x_tanh, vl);
        
        // 存储结果
        __riscv_vse32_v_f32m1(dst + i, v_result, vl);
    }

    // 处理剩余元素
    const float GELU_COEF_A_SCALAR = 0.044715f;
    const float SQRT_2_OVER_PI_SCALAR = 0.79788456080286535587989211986876f;
    
    for (; i < n; i++) {
        float x = src[i];
        dst[i] = 0.5f * x * (1.0f + tanhf(SQRT_2_OVER_PI_SCALAR * x * (1.0f + GELU_COEF_A_SCALAR * x * x)));
    }
}

// ================================================================================================
// SiLU (Sigmoid Linear Unit) 激活函数 - RISC-V Vector Extension 优化实现
// ================================================================================================

void rvv_silu_f32(int n, float* dst, const float* src, const rvv_config_t* config) {
    if (!RVV_AVAILABLE() || n < RVV_MIN_VEC_SIZE) {
        // 回退到标量实现
        for (int i = 0; i < n; i++) {
            float x = src[i];
            dst[i] = x / (1.0f + expf(-x));
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    const float ONE = 1.0f;
    
    int i = 0;

    // 向量化主循环
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        
        // 加载输入向量
        vfloat32m1_t v_x = __riscv_vle32_v_f32m1(src + i, vl);
        
        // 创建常数向量
        vfloat32m1_t v_one = __riscv_vfmv_v_f_f32m1(ONE, vl);
        
        // 计算 -x
        vfloat32m1_t v_neg_x = __riscv_vfneg_v_f32m1(v_x, vl);
        
        // 计算 exp(-x) (需要标量回退，因为 RVV 没有直接的 exp 指令)
        float exp_results[vl_max];
        __riscv_vse32_v_f32m1(exp_results, v_neg_x, vl);
        for (size_t j = 0; j < vl; j++) {
            exp_results[j] = expf(exp_results[j]);
        }
        vfloat32m1_t v_exp = __riscv_vle32_v_f32m1(exp_results, vl);
        
        // 计算 1 + exp(-x)
        vfloat32m1_t v_one_plus_exp = __riscv_vfadd_vv_f32m1(v_one, v_exp, vl);
        
        // 计算 x / (1 + exp(-x))
        vfloat32m1_t v_result = __riscv_vfdiv_vv_f32m1(v_x, v_one_plus_exp, vl);
        
        // 存储结果
        __riscv_vse32_v_f32m1(dst + i, v_result, vl);
    }

    // 处理剩余元素
    for (; i < n; i++) {
        float x = src[i];
        dst[i] = x / (1.0f + expf(-x));
    }
}

// ================================================================================================
// Leaky ReLU 激活函数 - RISC-V Vector Extension 优化实现
// ================================================================================================

void rvv_leaky_relu_f32(int n, float* dst, const float* src, float negative_slope, const rvv_config_t* config) {
    if (!RVV_AVAILABLE() || n < RVV_MIN_VEC_SIZE) {
        // 回退到标量实现
        for (int i = 0; i < n; i++) {
            float x = src[i];
            dst[i] = x > 0.0f ? x : negative_slope * x;
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    int i = 0;

    // 向量化主循环
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        
        // 加载输入向量
        vfloat32m1_t v_src = __riscv_vle32_v_f32m1(src + i, vl);
        
        // 创建零向量和负斜率向量
        vfloat32m1_t v_zero = __riscv_vfmv_v_f_f32m1(0.0f, vl);
        vfloat32m1_t v_neg_slope = __riscv_vfmv_v_f_f32m1(negative_slope, vl);
        
        // 创建掩码：x > 0
        vbool32_t mask_pos = __riscv_vmfgt_vv_f32m1_b32(v_src, v_zero, vl);
        
        // 计算负值部分：negative_slope * x
        vfloat32m1_t v_neg_part = __riscv_vfmul_vv_f32m1(v_neg_slope, v_src, vl);
        
        // 根据掩码选择正值或负值部分
        vfloat32m1_t v_result = __riscv_vmerge_vvm_f32m1(v_neg_part, v_src, mask_pos, vl);
        
        // 存储结果
        __riscv_vse32_v_f32m1(dst + i, v_result, vl);
    }

    // 处理剩余元素
    for (; i < n; i++) {
        float x = src[i];
        dst[i] = x > 0.0f ? x : negative_slope * x;
    }
}

#else

// 非 RVV 环境下的回退实现
void rvv_relu_f32(int n, float* dst, const float* src, const rvv_config_t* config) {
    for (int i = 0; i < n; i++) {
        dst[i] = fmaxf(0.0f, src[i]);
    }
}

void rvv_gelu_f32(int n, float* dst, const float* src, const rvv_config_t* config) {
    const float GELU_COEF_A = 0.044715f;
    const float SQRT_2_OVER_PI = 0.79788456080286535587989211986876f;
    
    for (int i = 0; i < n; i++) {
        float x = src[i];
        dst[i] = 0.5f * x * (1.0f + tanhf(SQRT_2_OVER_PI * x * (1.0f + GELU_COEF_A * x * x)));
    }
}

void rvv_silu_f32(int n, float* dst, const float* src, const rvv_config_t* config) {
    for (int i = 0; i < n; i++) {
        float x = src[i];
        dst[i] = x / (1.0f + expf(-x));
    }
}

void rvv_leaky_relu_f32(int n, float* dst, const float* src, float negative_slope, const rvv_config_t* config) {
    for (int i = 0; i < n; i++) {
        float x = src[i];
        dst[i] = x > 0.0f ? x : negative_slope * x;
    }
}

#endif
