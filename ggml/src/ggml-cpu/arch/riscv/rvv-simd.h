#pragma once

#include <stdint.h>
#include <string.h>
#include <stdbool.h>

#ifdef __riscv_v
#include <riscv_vector.h>
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 前向声明
typedef uint16_t ggml_fp16_t;

// RISC-V Vector Extension (RVV) 优化架构
// 支持动态向量长度和多种数据类型的高效向量化操作

// ================================================================================================
// RVV 基础配置和宏定义
// ================================================================================================

#ifdef __riscv_v

// 获取向量长度（字节）
#define RVV_VLENB() __riscv_vlenb()

// 获取向量长度（元素数量）
#define RVV_VL_F32() (RVV_VLENB() / sizeof(float))
#define RVV_VL_F16() (RVV_VLENB() / sizeof(ggml_fp16_t))
#define RVV_VL_I32() (RVV_VLENB() / sizeof(int32_t))
#define RVV_VL_I16() (RVV_VLENB() / sizeof(int16_t))
#define RVV_VL_I8()  (RVV_VLENB() / sizeof(int8_t))

// 向量化阈值 - 只有当数据量大于此阈值时才使用向量化
#define RVV_MIN_VEC_SIZE 16

// 检查是否支持 RVV
#define RVV_AVAILABLE() (RVV_VLENB() >= 16)

// 检查向量长度是否足够处理特定数据类型
#define RVV_CHECK_VL_F32(n) (RVV_AVAILABLE() && (n) >= RVV_VL_F32())
#define RVV_CHECK_VL_F16(n) (RVV_AVAILABLE() && (n) >= RVV_VL_F16())

#else

#define RVV_VLENB() 0
#define RVV_VL_F32() 0
#define RVV_VL_F16() 0
#define RVV_VL_I32() 0
#define RVV_VL_I16() 0
#define RVV_VL_I8() 0
#define RVV_MIN_VEC_SIZE 0
#define RVV_AVAILABLE() false
#define RVV_CHECK_VL_F32(n) false
#define RVV_CHECK_VL_F16(n) false

#endif

// ================================================================================================
// RVV 向量化策略枚举
// ================================================================================================

typedef enum {
    RVV_STRATEGY_NONE = 0,      // 不使用向量化
    RVV_STRATEGY_BASIC,         // 基础向量化
    RVV_STRATEGY_UNROLL_2X,     // 2倍循环展开
    RVV_STRATEGY_UNROLL_4X,     // 4倍循环展开
    RVV_STRATEGY_UNROLL_8X,     // 8倍循环展开
    RVV_STRATEGY_PIPELINE,      // 流水线优化
    RVV_STRATEGY_MULTICORE,     // 多核并行
} rvv_strategy_t;

// ================================================================================================
// RVV 内存访问模式
// ================================================================================================

typedef enum {
    RVV_MEM_SEQUENTIAL = 0,     // 顺序访问
    RVV_MEM_STRIDED,           // 跨步访问
    RVV_MEM_INDEXED,           // 索引访问
    RVV_MEM_GATHER_SCATTER,    // 聚集/分散访问
} rvv_memory_pattern_t;

// ================================================================================================
// RVV 优化配置结构
// ================================================================================================

typedef struct {
    rvv_strategy_t strategy;           // 向量化策略
    rvv_memory_pattern_t mem_pattern;  // 内存访问模式
    int unroll_factor;                 // 循环展开因子
    int block_size;                    // 数据块大小
    int num_threads;                   // 线程数量
    bool use_fma;                      // 是否使用融合乘加
    bool prefetch_enabled;             // 是否启用预取
    int cache_line_size;               // 缓存行大小
} rvv_config_t;

// 默认 RVV 配置
static inline rvv_config_t rvv_get_default_config(void) {
    rvv_config_t config = {
        .strategy = RVV_STRATEGY_BASIC,
        .mem_pattern = RVV_MEM_SEQUENTIAL,
        .unroll_factor = 4,
        .block_size = 1024,
        .num_threads = 1,
        .use_fma = true,
        .prefetch_enabled = false,
        .cache_line_size = 64
    };
    return config;
}

// ================================================================================================
// RVV 性能计数器和分析
// ================================================================================================

typedef struct {
    uint64_t total_ops;           // 总操作数
    uint64_t vectorized_ops;      // 向量化操作数
    uint64_t scalar_ops;          // 标量操作数
    double vectorization_ratio;   // 向量化比率
    double execution_time;        // 执行时间
    double throughput;            // 吞吐量 (ops/sec)
} rvv_perf_stats_t;

// 初始化性能统计
static inline void rvv_perf_init(rvv_perf_stats_t* stats) {
    memset(stats, 0, sizeof(rvv_perf_stats_t));
}

// 更新性能统计
static inline void rvv_perf_update(rvv_perf_stats_t* stats, uint64_t vec_ops, uint64_t scalar_ops, double time) {
    stats->vectorized_ops += vec_ops;
    stats->scalar_ops += scalar_ops;
    stats->total_ops = stats->vectorized_ops + stats->scalar_ops;
    stats->execution_time += time;
    
    if (stats->total_ops > 0) {
        stats->vectorization_ratio = (double)stats->vectorized_ops / stats->total_ops;
    }
    
    if (stats->execution_time > 0) {
        stats->throughput = stats->total_ops / stats->execution_time;
    }
}

// ================================================================================================
// RVV 多核并行支持
// ================================================================================================

typedef struct {
    int thread_id;                // 线程ID
    int num_threads;              // 总线程数
    int start_idx;                // 起始索引
    int end_idx;                  // 结束索引
    void* shared_data;            // 共享数据指针
    rvv_config_t* config;         // RVV配置
    rvv_perf_stats_t* stats;      // 性能统计
} rvv_thread_context_t;

// 计算线程工作范围
static inline void rvv_calc_thread_range(int total_size, int thread_id, int num_threads, 
                                         int* start, int* end) {
    int chunk_size = total_size / num_threads;
    int remainder = total_size % num_threads;
    
    *start = thread_id * chunk_size + (thread_id < remainder ? thread_id : remainder);
    *end = *start + chunk_size + (thread_id < remainder ? 1 : 0);
}

// ================================================================================================
// RVV 向量化辅助函数声明
// ================================================================================================

// 基础向量操作
void rvv_vec_add_f32(int n, float* dst, const float* src0, const float* src1, const rvv_config_t* config);
void rvv_vec_mul_f32(int n, float* dst, const float* src0, const float* src1, const rvv_config_t* config);
void rvv_vec_fma_f32(int n, float* dst, const float* src0, const float* src1, const float* src2, const rvv_config_t* config);

// 激活函数
void rvv_gelu_f32(int n, float* dst, const float* src, const rvv_config_t* config);
void rvv_relu_f32(int n, float* dst, const float* src, const rvv_config_t* config);
void rvv_silu_f32(int n, float* dst, const float* src, const rvv_config_t* config);

// 数学运算
void rvv_softmax_f32(int n, float* dst, const float* src, const rvv_config_t* config);
void rvv_layer_norm_f32(int n, float* dst, const float* src, const float* weight, const float* bias, 
                       float eps, const rvv_config_t* config);

// 矩阵运算
void rvv_gemm_f32(int m, int n, int k, float* C, const float* A, const float* B, 
                  float alpha, float beta, const rvv_config_t* config);
void rvv_gemv_f32(int m, int n, float* y, const float* A, const float* x, 
                  float alpha, float beta, const rvv_config_t* config);

// 量化操作
void rvv_quantize_f32_to_q8_0(int n, const float* src, void* dst, const rvv_config_t* config);
void rvv_dequantize_q8_0_to_f32(int n, const void* src, float* dst, const rvv_config_t* config);

// 额外激活函数
void rvv_leaky_relu_f32(int n, float* dst, const float* src, float negative_slope, const rvv_config_t* config);

// RMS Normalization
void rvv_rms_norm_f32(int n, float* dst, const float* src, const float* weight, float eps, const rvv_config_t* config);

// 多核版本函数声明
void rvv_gemm_f32_multicore(int m, int n, int k, float* C, const float* A, const float* B,
                            float alpha, float beta, const rvv_config_t* config);
void rvv_vec_add_f32_multicore(int n, float* dst, const float* src0, const float* src1,
                               const rvv_config_t* config);
void rvv_relu_f32_multicore(int n, float* dst, const float* src, const rvv_config_t* config);
void rvv_gelu_f32_multicore(int n, float* dst, const float* src, const rvv_config_t* config);
void rvv_silu_f32_multicore(int n, float* dst, const float* src, const rvv_config_t* config);

// 注意力机制函数声明
void rvv_scaled_dot_product_attention_f32(
    int batch_size, int seq_len, int head_dim,
    float* output, const float* query, const float* key, const float* value,
    const float* mask, float scale, const rvv_config_t* config);

void rvv_multi_head_attention_f32(
    int batch_size, int seq_len, int num_heads, int head_dim,
    float* output, const float* query, const float* key, const float* value,
    const float* mask, const rvv_config_t* config);

// 位置编码和嵌入函数声明
void rvv_sinusoidal_position_encoding_f32(
    int seq_len, int d_model, float* pos_encoding, const rvv_config_t* config);

void rvv_embedding_lookup_f32(
    int batch_size, int seq_len, int vocab_size, int embed_dim,
    float* output, const int* input_ids, const float* embedding_table,
    const rvv_config_t* config);

void rvv_add_position_encoding_f32(
    int batch_size, int seq_len, int embed_dim,
    float* embeddings, const float* pos_encoding, const rvv_config_t* config);

#ifdef __cplusplus
}
#endif
