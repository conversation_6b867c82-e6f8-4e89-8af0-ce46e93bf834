# RISC-V Vector Extension (RVV) 优化模块 CMake 配置

cmake_minimum_required(VERSION 3.12)

# 检查是否为 RISC-V 架构
if(NOT CMAKE_SYSTEM_PROCESSOR MATCHES "riscv")
    message(WARNING "RVV 优化模块仅支持 RISC-V 架构")
    return()
endif()

# RVV 优化源文件
set(RVV_SOURCES
    rvv-activations.c
    rvv-gemm.c
    rvv-math.c
    rvv-multicore.c
    rvv-attention.c
    rvv-test.c
)

# 创建 RVV 优化静态库
add_library(ggml-rvv-opt STATIC ${RVV_SOURCES})

# 设置编译选项
target_compile_options(ggml-rvv-opt PRIVATE
    -march=rv64gcv          # 启用 RISC-V Vector Extension
    -mabi=lp64d            # 使用 LP64D ABI
    -O3                    # 最高优化级别
    -ffast-math            # 启用快速数学运算
    -funroll-loops         # 循环展开
    -fomit-frame-pointer   # 省略帧指针
)

# 检查编译器是否支持特定的 RVV 特性
include(CheckCCompilerFlag)

check_c_compiler_flag("-march=rv64gcv_zfhmin" COMPILER_SUPPORTS_ZFHMIN)
if(COMPILER_SUPPORTS_ZFHMIN)
    target_compile_definitions(ggml-rvv-opt PRIVATE RVV_ZFHMIN_SUPPORT)
    target_compile_options(ggml-rvv-opt PRIVATE -march=rv64gcv_zfhmin)
endif()

check_c_compiler_flag("-march=rv64gc_xtheadvector" COMPILER_SUPPORTS_XTHEADVECTOR)
if(COMPILER_SUPPORTS_XTHEADVECTOR)
    target_compile_definitions(ggml-rvv-opt PRIVATE RVV_XTHEADVECTOR_SUPPORT)
endif()

# 设置包含目录
target_include_directories(ggml-rvv-opt PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../../..
)

# 链接数学库
target_link_libraries(ggml-rvv-opt PRIVATE m)

# 如果支持多线程，链接 pthread
find_package(Threads)
if(Threads_FOUND)
    target_link_libraries(ggml-rvv-opt PRIVATE Threads::Threads)
    target_compile_definitions(ggml-rvv-opt PRIVATE RVV_MULTITHREAD_SUPPORT)
endif()

# 设置编译定义
target_compile_definitions(ggml-rvv-opt PRIVATE
    RVV_OPTIMIZATION_ENABLED
    $<$<CONFIG:Debug>:RVV_DEBUG_MODE>
    $<$<CONFIG:Release>:RVV_RELEASE_MODE>
)

# 创建测试可执行文件
if(BUILD_TESTING)
    add_executable(test-rvv-optimization
        ${CMAKE_SOURCE_DIR}/tests/test-rvv-optimization.c
    )
    
    target_link_libraries(test-rvv-optimization PRIVATE
        ggml-rvv-opt
        m
    )
    
    if(Threads_FOUND)
        target_link_libraries(test-rvv-optimization PRIVATE Threads::Threads)
    endif()
    
    target_compile_options(test-rvv-optimization PRIVATE
        -march=rv64gcv
        -mabi=lp64d
        -O3
    )
    
    # 添加测试
    add_test(NAME rvv_functionality_test 
             COMMAND test-rvv-optimization --test)
    add_test(NAME rvv_benchmark_test 
             COMMAND test-rvv-optimization --bench)
    add_test(NAME rvv_multicore_test 
             COMMAND test-rvv-optimization --multicore)
endif()

# 安装规则
install(TARGETS ggml-rvv-opt
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES rvv-simd.h
    DESTINATION include/ggml/cpu/arch/riscv
)

# 性能分析选项
option(RVV_ENABLE_PROFILING "启用 RVV 性能分析" OFF)
if(RVV_ENABLE_PROFILING)
    target_compile_definitions(ggml-rvv-opt PRIVATE RVV_PROFILING_ENABLED)
    target_compile_options(ggml-rvv-opt PRIVATE -pg)
    target_link_options(ggml-rvv-opt PRIVATE -pg)
endif()

# 向量化报告选项
option(RVV_VECTORIZATION_REPORT "生成向量化报告" OFF)
if(RVV_VECTORIZATION_REPORT)
    if(CMAKE_C_COMPILER_ID STREQUAL "GNU")
        target_compile_options(ggml-rvv-opt PRIVATE 
            -fopt-info-vec-optimized
            -fopt-info-vec-missed
        )
    elseif(CMAKE_C_COMPILER_ID STREQUAL "Clang")
        target_compile_options(ggml-rvv-opt PRIVATE 
            -Rpass=loop-vectorize
            -Rpass-missed=loop-vectorize
            -Rpass-analysis=loop-vectorize
        )
    endif()
endif()

# 调试选项
option(RVV_DEBUG_VERBOSE "启用详细调试输出" OFF)
if(RVV_DEBUG_VERBOSE)
    target_compile_definitions(ggml-rvv-opt PRIVATE RVV_DEBUG_VERBOSE)
endif()

# 创建性能基准可执行文件
add_executable(rvv-benchmark
    rvv-test.c
    ${CMAKE_SOURCE_DIR}/tests/test-rvv-optimization.c
)

target_link_libraries(rvv-benchmark PRIVATE
    ggml-rvv-opt
    m
)

if(Threads_FOUND)
    target_link_libraries(rvv-benchmark PRIVATE Threads::Threads)
endif()

target_compile_options(rvv-benchmark PRIVATE
    -march=rv64gcv
    -mabi=lp64d
    -O3
)

# 打印配置信息
message(STATUS "RVV 优化配置:")
message(STATUS "  架构: ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "  编译器: ${CMAKE_C_COMPILER_ID}")
message(STATUS "  构建类型: ${CMAKE_BUILD_TYPE}")
message(STATUS "  多线程支持: ${Threads_FOUND}")
message(STATUS "  性能分析: ${RVV_ENABLE_PROFILING}")
message(STATUS "  向量化报告: ${RVV_VECTORIZATION_REPORT}")
message(STATUS "  详细调试: ${RVV_DEBUG_VERBOSE}")

# 创建文档目标
find_program(DOXYGEN_EXECUTABLE doxygen)
if(DOXYGEN_EXECUTABLE)
    configure_file(${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in 
                   ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile @ONLY)
    
    add_custom_target(rvv-docs
        COMMAND ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "生成 RVV 优化 API 文档"
        VERBATIM
    )
endif()

# 代码覆盖率选项
option(RVV_ENABLE_COVERAGE "启用代码覆盖率分析" OFF)
if(RVV_ENABLE_COVERAGE AND CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(ggml-rvv-opt PRIVATE --coverage)
    target_link_options(ggml-rvv-opt PRIVATE --coverage)
endif()

# 静态分析选项
option(RVV_ENABLE_STATIC_ANALYSIS "启用静态分析" OFF)
if(RVV_ENABLE_STATIC_ANALYSIS)
    find_program(CLANG_TIDY_EXECUTABLE clang-tidy)
    if(CLANG_TIDY_EXECUTABLE)
        set_target_properties(ggml-rvv-opt PROPERTIES
            C_CLANG_TIDY "${CLANG_TIDY_EXECUTABLE};-checks=*"
        )
    endif()
endif()

# 内存检查选项
option(RVV_ENABLE_SANITIZERS "启用内存和地址检查器" OFF)
if(RVV_ENABLE_SANITIZERS AND CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(ggml-rvv-opt PRIVATE 
        -fsanitize=address
        -fsanitize=undefined
        -fno-omit-frame-pointer
    )
    target_link_options(ggml-rvv-opt PRIVATE 
        -fsanitize=address
        -fsanitize=undefined
    )
endif()
