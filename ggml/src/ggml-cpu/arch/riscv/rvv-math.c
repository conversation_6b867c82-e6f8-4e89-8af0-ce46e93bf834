#include "rvv-simd.h"
#include <math.h>
#include <float.h>
#include <string.h>

#ifdef __riscv_v

// ================================================================================================
// Softmax 函数 - RISC-V Vector Extension 优化实现
// ================================================================================================

void rvv_softmax_f32(int n, float* dst, const float* src, const rvv_config_t* config) {
    if (!RVV_AVAILABLE() || n < RVV_MIN_VEC_SIZE) {
        // 回退到标量实现
        float max_val = -FLT_MAX;
        for (int i = 0; i < n; i++) {
            if (src[i] > max_val) max_val = src[i];
        }
        
        float sum = 0.0f;
        for (int i = 0; i < n; i++) {
            dst[i] = expf(src[i] - max_val);
            sum += dst[i];
        }
        
        float inv_sum = 1.0f / sum;
        for (int i = 0; i < n; i++) {
            dst[i] *= inv_sum;
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    
    // 第一步：找到最大值
    vfloat32m1_t v_max = __riscv_vfmv_v_f_f32m1(-FLT_MAX, vl_max);
    
    int i = 0;
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        vfloat32m1_t v_src = __riscv_vle32_v_f32m1(src + i, vl);
        v_max = __riscv_vfmax_vv_f32m1(v_max, v_src, vl);
    }
    
    // 归约找到全局最大值
    vfloat32m1_t v_max_scalar = __riscv_vfredmax_vs_f32m1_f32m1(v_max, v_max, vl_max);
    float max_val = __riscv_vfmv_f_s_f32m1_f32(v_max_scalar);
    
    // 处理剩余元素
    for (; i < n; i++) {
        if (src[i] > max_val) max_val = src[i];
    }
    
    // 第二步：计算 exp(x - max) 并求和
    vfloat32m1_t v_sum = __riscv_vfmv_v_f_f32m1(0.0f, vl_max);
    vfloat32m1_t v_max_broadcast = __riscv_vfmv_v_f_f32m1(max_val, vl_max);
    
    i = 0;
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        
        // 加载输入
        vfloat32m1_t v_src = __riscv_vle32_v_f32m1(src + i, vl);
        
        // 计算 x - max
        vfloat32m1_t v_diff = __riscv_vfsub_vv_f32m1(v_src, v_max_broadcast, vl);
        
        // 计算 exp(x - max) - 需要标量回退
        float exp_results[vl_max];
        __riscv_vse32_v_f32m1(exp_results, v_diff, vl);
        for (size_t j = 0; j < vl; j++) {
            exp_results[j] = expf(exp_results[j]);
        }
        vfloat32m1_t v_exp = __riscv_vle32_v_f32m1(exp_results, vl);
        
        // 存储 exp 结果
        __riscv_vse32_v_f32m1(dst + i, v_exp, vl);
        
        // 累加到总和
        v_sum = __riscv_vfadd_vv_f32m1(v_sum, v_exp, vl);
    }
    
    // 归约求和
    vfloat32m1_t v_sum_scalar = __riscv_vfredosum_vs_f32m1_f32m1(v_sum, __riscv_vfmv_v_f_f32m1(0.0f, vl_max), vl_max);
    float sum = __riscv_vfmv_f_s_f32m1_f32(v_sum_scalar);
    
    // 处理剩余元素
    for (; i < n; i++) {
        dst[i] = expf(src[i] - max_val);
        sum += dst[i];
    }
    
    // 第三步：归一化
    float inv_sum = 1.0f / sum;
    vfloat32m1_t v_inv_sum = __riscv_vfmv_v_f_f32m1(inv_sum, vl_max);
    
    i = 0;
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        
        vfloat32m1_t v_dst = __riscv_vle32_v_f32m1(dst + i, vl);
        v_dst = __riscv_vfmul_vv_f32m1(v_dst, v_inv_sum, vl);
        __riscv_vse32_v_f32m1(dst + i, v_dst, vl);
    }
    
    // 处理剩余元素
    for (; i < n; i++) {
        dst[i] *= inv_sum;
    }
}

// ================================================================================================
// Layer Normalization - RISC-V Vector Extension 优化实现
// ================================================================================================

void rvv_layer_norm_f32(int n, float* dst, const float* src, const float* weight, const float* bias, 
                        float eps, const rvv_config_t* config) {
    if (!RVV_AVAILABLE() || n < RVV_MIN_VEC_SIZE) {
        // 回退到标量实现
        float mean = 0.0f;
        for (int i = 0; i < n; i++) {
            mean += src[i];
        }
        mean /= n;
        
        float var = 0.0f;
        for (int i = 0; i < n; i++) {
            float diff = src[i] - mean;
            var += diff * diff;
        }
        var /= n;
        
        float inv_std = 1.0f / sqrtf(var + eps);
        
        for (int i = 0; i < n; i++) {
            float normalized = (src[i] - mean) * inv_std;
            dst[i] = normalized * weight[i] + bias[i];
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    const float n_inv = 1.0f / n;
    
    // 第一步：计算均值
    vfloat32m1_t v_sum = __riscv_vfmv_v_f_f32m1(0.0f, vl_max);
    
    int i = 0;
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        vfloat32m1_t v_src = __riscv_vle32_v_f32m1(src + i, vl);
        v_sum = __riscv_vfadd_vv_f32m1(v_sum, v_src, vl);
    }
    
    // 归约求和
    vfloat32m1_t v_mean_scalar = __riscv_vfredosum_vs_f32m1_f32m1(v_sum, __riscv_vfmv_v_f_f32m1(0.0f, vl_max), vl_max);
    float mean = __riscv_vfmv_f_s_f32m1_f32(v_mean_scalar);
    
    // 处理剩余元素
    for (; i < n; i++) {
        mean += src[i];
    }
    mean *= n_inv;
    
    // 第二步：计算方差
    vfloat32m1_t v_var_sum = __riscv_vfmv_v_f_f32m1(0.0f, vl_max);
    vfloat32m1_t v_mean_broadcast = __riscv_vfmv_v_f_f32m1(mean, vl_max);
    
    i = 0;
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        
        vfloat32m1_t v_src = __riscv_vle32_v_f32m1(src + i, vl);
        vfloat32m1_t v_diff = __riscv_vfsub_vv_f32m1(v_src, v_mean_broadcast, vl);
        vfloat32m1_t v_diff_sq = __riscv_vfmul_vv_f32m1(v_diff, v_diff, vl);
        v_var_sum = __riscv_vfadd_vv_f32m1(v_var_sum, v_diff_sq, vl);
    }
    
    // 归约求和
    vfloat32m1_t v_var_scalar = __riscv_vfredosum_vs_f32m1_f32m1(v_var_sum, __riscv_vfmv_v_f_f32m1(0.0f, vl_max), vl_max);
    float var = __riscv_vfmv_f_s_f32m1_f32(v_var_scalar);
    
    // 处理剩余元素
    for (; i < n; i++) {
        float diff = src[i] - mean;
        var += diff * diff;
    }
    var *= n_inv;
    
    // 第三步：归一化并应用权重和偏置
    float inv_std = 1.0f / sqrtf(var + eps);
    vfloat32m1_t v_mean_norm = __riscv_vfmv_v_f_f32m1(mean, vl_max);
    vfloat32m1_t v_inv_std = __riscv_vfmv_v_f_f32m1(inv_std, vl_max);
    
    i = 0;
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        
        // 加载输入、权重和偏置
        vfloat32m1_t v_src = __riscv_vle32_v_f32m1(src + i, vl);
        vfloat32m1_t v_weight = __riscv_vle32_v_f32m1(weight + i, vl);
        vfloat32m1_t v_bias = __riscv_vle32_v_f32m1(bias + i, vl);
        
        // 计算 (x - mean) * inv_std
        vfloat32m1_t v_diff = __riscv_vfsub_vv_f32m1(v_src, v_mean_norm, vl);
        vfloat32m1_t v_normalized = __riscv_vfmul_vv_f32m1(v_diff, v_inv_std, vl);
        
        // 应用权重和偏置: normalized * weight + bias
        vfloat32m1_t v_result = __riscv_vfmacc_vv_f32m1(v_bias, v_normalized, v_weight, vl);
        
        // 存储结果
        __riscv_vse32_v_f32m1(dst + i, v_result, vl);
    }
    
    // 处理剩余元素
    for (; i < n; i++) {
        float normalized = (src[i] - mean) * inv_std;
        dst[i] = normalized * weight[i] + bias[i];
    }
}

// ================================================================================================
// RMS Normalization - RISC-V Vector Extension 优化实现
// ================================================================================================

void rvv_rms_norm_f32(int n, float* dst, const float* src, const float* weight, float eps, const rvv_config_t* config) {
    if (!RVV_AVAILABLE() || n < RVV_MIN_VEC_SIZE) {
        // 回退到标量实现
        float sum_sq = 0.0f;
        for (int i = 0; i < n; i++) {
            sum_sq += src[i] * src[i];
        }
        
        float rms = sqrtf(sum_sq / n + eps);
        float inv_rms = 1.0f / rms;
        
        for (int i = 0; i < n; i++) {
            dst[i] = src[i] * inv_rms * weight[i];
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    const float n_inv = 1.0f / n;
    
    // 第一步：计算平方和
    vfloat32m1_t v_sum_sq = __riscv_vfmv_v_f_f32m1(0.0f, vl_max);
    
    int i = 0;
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        vfloat32m1_t v_src = __riscv_vle32_v_f32m1(src + i, vl);
        vfloat32m1_t v_sq = __riscv_vfmul_vv_f32m1(v_src, v_src, vl);
        v_sum_sq = __riscv_vfadd_vv_f32m1(v_sum_sq, v_sq, vl);
    }
    
    // 归约求和
    vfloat32m1_t v_sum_scalar = __riscv_vfredosum_vs_f32m1_f32m1(v_sum_sq, __riscv_vfmv_v_f_f32m1(0.0f, vl_max), vl_max);
    float sum_sq = __riscv_vfmv_f_s_f32m1_f32(v_sum_scalar);
    
    // 处理剩余元素
    for (; i < n; i++) {
        sum_sq += src[i] * src[i];
    }
    
    // 第二步：计算 RMS 和逆 RMS
    float rms = sqrtf(sum_sq * n_inv + eps);
    float inv_rms = 1.0f / rms;
    vfloat32m1_t v_inv_rms = __riscv_vfmv_v_f_f32m1(inv_rms, vl_max);
    
    // 第三步：归一化并应用权重
    i = 0;
    for (; i + vl_max <= n; i += vl_max) {
        size_t vl = __riscv_vsetvl_e32m1(vl_max);
        
        vfloat32m1_t v_src = __riscv_vle32_v_f32m1(src + i, vl);
        vfloat32m1_t v_weight = __riscv_vle32_v_f32m1(weight + i, vl);
        
        // 计算 src * inv_rms * weight
        vfloat32m1_t v_normalized = __riscv_vfmul_vv_f32m1(v_src, v_inv_rms, vl);
        vfloat32m1_t v_result = __riscv_vfmul_vv_f32m1(v_normalized, v_weight, vl);
        
        __riscv_vse32_v_f32m1(dst + i, v_result, vl);
    }
    
    // 处理剩余元素
    for (; i < n; i++) {
        dst[i] = src[i] * inv_rms * weight[i];
    }
}

#else

// 非 RVV 环境下的回退实现
void rvv_softmax_f32(int n, float* dst, const float* src, const rvv_config_t* config) {
    float max_val = -FLT_MAX;
    for (int i = 0; i < n; i++) {
        if (src[i] > max_val) max_val = src[i];
    }
    
    float sum = 0.0f;
    for (int i = 0; i < n; i++) {
        dst[i] = expf(src[i] - max_val);
        sum += dst[i];
    }
    
    float inv_sum = 1.0f / sum;
    for (int i = 0; i < n; i++) {
        dst[i] *= inv_sum;
    }
}

void rvv_layer_norm_f32(int n, float* dst, const float* src, const float* weight, const float* bias, 
                        float eps, const rvv_config_t* config) {
    float mean = 0.0f;
    for (int i = 0; i < n; i++) {
        mean += src[i];
    }
    mean /= n;
    
    float var = 0.0f;
    for (int i = 0; i < n; i++) {
        float diff = src[i] - mean;
        var += diff * diff;
    }
    var /= n;
    
    float inv_std = 1.0f / sqrtf(var + eps);
    
    for (int i = 0; i < n; i++) {
        float normalized = (src[i] - mean) * inv_std;
        dst[i] = normalized * weight[i] + bias[i];
    }
}

void rvv_rms_norm_f32(int n, float* dst, const float* src, const float* weight, float eps, const rvv_config_t* config) {
    float sum_sq = 0.0f;
    for (int i = 0; i < n; i++) {
        sum_sq += src[i] * src[i];
    }
    
    float rms = sqrtf(sum_sq / n + eps);
    float inv_rms = 1.0f / rms;
    
    for (int i = 0; i < n; i++) {
        dst[i] = src[i] * inv_rms * weight[i];
    }
}

#endif
