#include "rvv-simd.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <math.h>
#include <assert.h>

// ================================================================================================
// RVV 测试框架和性能基准
// ================================================================================================

#define TEST_TOLERANCE 1e-5f
#define BENCHMARK_ITERATIONS 1000

// 测试结果结构
typedef struct {
    bool passed;
    double execution_time;
    double speedup;
    char error_msg[256];
} rvv_test_result_t;

// 生成随机测试数据
static void generate_random_data_f32(float* data, int n, float min_val, float max_val) {
    for (int i = 0; i < n; i++) {
        data[i] = min_val + (max_val - min_val) * ((float)rand() / RAND_MAX);
    }
}

// 比较两个浮点数组
static bool compare_arrays_f32(const float* a, const float* b, int n, float tolerance) {
    for (int i = 0; i < n; i++) {
        float diff = fabsf(a[i] - b[i]);
        float max_val = fmaxf(fabsf(a[i]), fabsf(b[i]));
        if (max_val > 0 && diff / max_val > tolerance) {
            return false;
        } else if (max_val == 0 && diff > tolerance) {
            return false;
        }
    }
    return true;
}

// 获取当前时间（秒）
static double get_time_seconds(void) {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec + ts.tv_nsec * 1e-9;
}

// ================================================================================================
// 激活函数测试
// ================================================================================================

static rvv_test_result_t test_relu_f32(int n) {
    rvv_test_result_t result = {0};
    
    float* input = malloc(n * sizeof(float));
    float* output_rvv = malloc(n * sizeof(float));
    float* output_ref = malloc(n * sizeof(float));
    
    if (!input || !output_rvv || !output_ref) {
        strcpy(result.error_msg, "Memory allocation failed");
        goto cleanup;
    }
    
    // 生成测试数据（包含正负值）
    generate_random_data_f32(input, n, -10.0f, 10.0f);
    
    rvv_config_t config = rvv_get_default_config();
    
    // 参考实现
    double start_time = get_time_seconds();
    for (int iter = 0; iter < BENCHMARK_ITERATIONS; iter++) {
        for (int i = 0; i < n; i++) {
            output_ref[i] = fmaxf(0.0f, input[i]);
        }
    }
    double ref_time = get_time_seconds() - start_time;
    
    // RVV 实现
    start_time = get_time_seconds();
    for (int iter = 0; iter < BENCHMARK_ITERATIONS; iter++) {
        rvv_relu_f32(n, output_rvv, input, &config);
    }
    double rvv_time = get_time_seconds() - start_time;
    
    // 验证正确性
    result.passed = compare_arrays_f32(output_rvv, output_ref, n, TEST_TOLERANCE);
    result.execution_time = rvv_time;
    result.speedup = ref_time / rvv_time;
    
    if (!result.passed) {
        strcpy(result.error_msg, "ReLU output mismatch");
    }
    
cleanup:
    free(input);
    free(output_rvv);
    free(output_ref);
    return result;
}

static rvv_test_result_t test_gelu_f32(int n) {
    rvv_test_result_t result = {0};
    
    float* input = malloc(n * sizeof(float));
    float* output_rvv = malloc(n * sizeof(float));
    float* output_ref = malloc(n * sizeof(float));
    
    if (!input || !output_rvv || !output_ref) {
        strcpy(result.error_msg, "Memory allocation failed");
        goto cleanup;
    }
    
    generate_random_data_f32(input, n, -3.0f, 3.0f);
    
    rvv_config_t config = rvv_get_default_config();
    
    // 参考实现
    const float GELU_COEF_A = 0.044715f;
    const float SQRT_2_OVER_PI = 0.79788456080286535587989211986876f;
    
    double start_time = get_time_seconds();
    for (int iter = 0; iter < BENCHMARK_ITERATIONS; iter++) {
        for (int i = 0; i < n; i++) {
            float x = input[i];
            output_ref[i] = 0.5f * x * (1.0f + tanhf(SQRT_2_OVER_PI * x * (1.0f + GELU_COEF_A * x * x)));
        }
    }
    double ref_time = get_time_seconds() - start_time;
    
    // RVV 实现
    start_time = get_time_seconds();
    for (int iter = 0; iter < BENCHMARK_ITERATIONS; iter++) {
        rvv_gelu_f32(n, output_rvv, input, &config);
    }
    double rvv_time = get_time_seconds() - start_time;
    
    result.passed = compare_arrays_f32(output_rvv, output_ref, n, TEST_TOLERANCE);
    result.execution_time = rvv_time;
    result.speedup = ref_time / rvv_time;
    
    if (!result.passed) {
        strcpy(result.error_msg, "GELU output mismatch");
    }
    
cleanup:
    free(input);
    free(output_rvv);
    free(output_ref);
    return result;
}

static rvv_test_result_t test_silu_f32(int n) {
    rvv_test_result_t result = {0};
    
    float* input = malloc(n * sizeof(float));
    float* output_rvv = malloc(n * sizeof(float));
    float* output_ref = malloc(n * sizeof(float));
    
    if (!input || !output_rvv || !output_ref) {
        strcpy(result.error_msg, "Memory allocation failed");
        goto cleanup;
    }
    
    generate_random_data_f32(input, n, -5.0f, 5.0f);
    
    rvv_config_t config = rvv_get_default_config();
    
    // 参考实现
    double start_time = get_time_seconds();
    for (int iter = 0; iter < BENCHMARK_ITERATIONS; iter++) {
        for (int i = 0; i < n; i++) {
            float x = input[i];
            output_ref[i] = x / (1.0f + expf(-x));
        }
    }
    double ref_time = get_time_seconds() - start_time;
    
    // RVV 实现
    start_time = get_time_seconds();
    for (int iter = 0; iter < BENCHMARK_ITERATIONS; iter++) {
        rvv_silu_f32(n, output_rvv, input, &config);
    }
    double rvv_time = get_time_seconds() - start_time;
    
    result.passed = compare_arrays_f32(output_rvv, output_ref, n, TEST_TOLERANCE);
    result.execution_time = rvv_time;
    result.speedup = ref_time / rvv_time;
    
    if (!result.passed) {
        strcpy(result.error_msg, "SiLU output mismatch");
    }
    
cleanup:
    free(input);
    free(output_rvv);
    free(output_ref);
    return result;
}

// ================================================================================================
// GEMM 测试
// ================================================================================================

static rvv_test_result_t test_gemm_f32(int m, int n, int k) {
    rvv_test_result_t result = {0};
    
    float* A = malloc(m * k * sizeof(float));
    float* B = malloc(k * n * sizeof(float));
    float* C_rvv = malloc(m * n * sizeof(float));
    float* C_ref = malloc(m * n * sizeof(float));
    
    if (!A || !B || !C_rvv || !C_ref) {
        strcpy(result.error_msg, "Memory allocation failed");
        goto cleanup;
    }
    
    generate_random_data_f32(A, m * k, -1.0f, 1.0f);
    generate_random_data_f32(B, k * n, -1.0f, 1.0f);
    generate_random_data_f32(C_rvv, m * n, -0.1f, 0.1f);
    memcpy(C_ref, C_rvv, m * n * sizeof(float));
    
    rvv_config_t config = rvv_get_default_config();
    float alpha = 1.0f, beta = 1.0f;
    
    // 参考实现
    double start_time = get_time_seconds();
    for (int iter = 0; iter < 10; iter++) {  // 减少迭代次数，因为 GEMM 较慢
        for (int i = 0; i < m; i++) {
            for (int j = 0; j < n; j++) {
                float sum = 0.0f;
                for (int l = 0; l < k; l++) {
                    sum += A[i * k + l] * B[l * n + j];
                }
                C_ref[i * n + j] = alpha * sum + beta * C_ref[i * n + j];
            }
        }
    }
    double ref_time = get_time_seconds() - start_time;
    
    // RVV 实现
    start_time = get_time_seconds();
    for (int iter = 0; iter < 10; iter++) {
        rvv_gemm_f32(m, n, k, C_rvv, A, B, alpha, beta, &config);
    }
    double rvv_time = get_time_seconds() - start_time;
    
    result.passed = compare_arrays_f32(C_rvv, C_ref, m * n, TEST_TOLERANCE * 10); // 放宽容差
    result.execution_time = rvv_time;
    result.speedup = ref_time / rvv_time;
    
    if (!result.passed) {
        strcpy(result.error_msg, "GEMM output mismatch");
    }
    
cleanup:
    free(A);
    free(B);
    free(C_rvv);
    free(C_ref);
    return result;
}

// ================================================================================================
// 数学运算测试
// ================================================================================================

static rvv_test_result_t test_softmax_f32(int n) {
    rvv_test_result_t result = {0};
    
    float* input = malloc(n * sizeof(float));
    float* output_rvv = malloc(n * sizeof(float));
    float* output_ref = malloc(n * sizeof(float));
    
    if (!input || !output_rvv || !output_ref) {
        strcpy(result.error_msg, "Memory allocation failed");
        goto cleanup;
    }
    
    generate_random_data_f32(input, n, -5.0f, 5.0f);
    
    rvv_config_t config = rvv_get_default_config();
    
    // 参考实现
    double start_time = get_time_seconds();
    for (int iter = 0; iter < BENCHMARK_ITERATIONS; iter++) {
        // 找最大值
        float max_val = input[0];
        for (int i = 1; i < n; i++) {
            if (input[i] > max_val) max_val = input[i];
        }
        
        // 计算 exp 和求和
        float sum = 0.0f;
        for (int i = 0; i < n; i++) {
            output_ref[i] = expf(input[i] - max_val);
            sum += output_ref[i];
        }
        
        // 归一化
        for (int i = 0; i < n; i++) {
            output_ref[i] /= sum;
        }
    }
    double ref_time = get_time_seconds() - start_time;
    
    // RVV 实现
    start_time = get_time_seconds();
    for (int iter = 0; iter < BENCHMARK_ITERATIONS; iter++) {
        rvv_softmax_f32(n, output_rvv, input, &config);
    }
    double rvv_time = get_time_seconds() - start_time;
    
    result.passed = compare_arrays_f32(output_rvv, output_ref, n, TEST_TOLERANCE);
    result.execution_time = rvv_time;
    result.speedup = ref_time / rvv_time;
    
    if (!result.passed) {
        strcpy(result.error_msg, "Softmax output mismatch");
    }
    
cleanup:
    free(input);
    free(output_rvv);
    free(output_ref);
    return result;
}

// ================================================================================================
// 主测试函数
// ================================================================================================

void run_rvv_tests(void) {
    printf("=== RISC-V Vector Extension (RVV) 测试报告 ===\n\n");
    
    if (!RVV_AVAILABLE()) {
        printf("警告: RVV 不可用，将使用标量回退实现\n\n");
    } else {
        printf("RVV 可用，向量长度: %zu 字节\n\n", RVV_VLENB());
    }
    
    srand(42); // 固定随机种子以确保可重现性
    
    // 测试不同大小
    int test_sizes[] = {64, 256, 1024, 4096, 16384};
    int num_sizes = sizeof(test_sizes) / sizeof(test_sizes[0]);
    
    printf("激活函数测试:\n");
    printf("%-10s %-8s %-12s %-10s %-15s\n", "函数", "大小", "通过", "时间(ms)", "加速比");
    printf("--------------------------------------------------------\n");
    
    for (int i = 0; i < num_sizes; i++) {
        int n = test_sizes[i];
        
        rvv_test_result_t relu_result = test_relu_f32(n);
        printf("%-10s %-8d %-12s %-10.3f %-15.2fx\n", 
               "ReLU", n, relu_result.passed ? "是" : "否", 
               relu_result.execution_time * 1000, relu_result.speedup);
        
        rvv_test_result_t gelu_result = test_gelu_f32(n);
        printf("%-10s %-8d %-12s %-10.3f %-15.2fx\n", 
               "GELU", n, gelu_result.passed ? "是" : "否", 
               gelu_result.execution_time * 1000, gelu_result.speedup);
        
        rvv_test_result_t silu_result = test_silu_f32(n);
        printf("%-10s %-8d %-12s %-10.3f %-15.2fx\n", 
               "SiLU", n, silu_result.passed ? "是" : "否", 
               silu_result.execution_time * 1000, silu_result.speedup);
    }
    
    printf("\nGEMM 测试:\n");
    printf("%-15s %-12s %-10s %-15s\n", "矩阵大小", "通过", "时间(ms)", "加速比");
    printf("------------------------------------------------\n");
    
    int gemm_sizes[][3] = {{64, 64, 64}, {128, 128, 128}, {256, 256, 256}};
    int num_gemm_sizes = sizeof(gemm_sizes) / sizeof(gemm_sizes[0]);
    
    for (int i = 0; i < num_gemm_sizes; i++) {
        int m = gemm_sizes[i][0], n = gemm_sizes[i][1], k = gemm_sizes[i][2];
        rvv_test_result_t gemm_result = test_gemm_f32(m, n, k);
        printf("%-15s %-12s %-10.3f %-15.2fx\n", 
               "256x256x256", gemm_result.passed ? "是" : "否", 
               gemm_result.execution_time * 1000, gemm_result.speedup);
    }
    
    printf("\n数学运算测试:\n");
    printf("%-10s %-8s %-12s %-10s %-15s\n", "函数", "大小", "通过", "时间(ms)", "加速比");
    printf("--------------------------------------------------------\n");
    
    for (int i = 0; i < num_sizes; i++) {
        int n = test_sizes[i];
        rvv_test_result_t softmax_result = test_softmax_f32(n);
        printf("%-10s %-8d %-12s %-10.3f %-15.2fx\n", 
               "Softmax", n, softmax_result.passed ? "是" : "否", 
               softmax_result.execution_time * 1000, softmax_result.speedup);
    }
    
    printf("\n=== 测试完成 ===\n");
}
