#include "rvv-simd.h"
#include <pthread.h>
#include <unistd.h>
#include <sys/sysinfo.h>
#include <errno.h>

// ================================================================================================
// RISC-V 多核并行优化实现
// 支持多核协同计算、流水线并行和负载均衡
// ================================================================================================

// 获取系统 CPU 核心数
static int rvv_get_num_cores(void) {
    int num_cores = sysconf(_SC_NPROCESSORS_ONLN);
    return num_cores > 0 ? num_cores : 1;
}

// ================================================================================================
// 多核 GEMM 实现
// ================================================================================================

typedef struct {
    int thread_id;
    int num_threads;
    int m, n, k;
    float* C;
    const float* A;
    const float* B;
    float alpha, beta;
    int start_row, end_row;
    const rvv_config_t* config;
    rvv_perf_stats_t* stats;
} rvv_gemm_thread_data_t;

static void* rvv_gemm_thread_worker(void* arg) {
    rvv_gemm_thread_data_t* data = (rvv_gemm_thread_data_t*)arg;
    
    // 计算该线程负责的行范围
    int rows_per_thread = data->m / data->num_threads;
    int remainder = data->m % data->num_threads;
    
    data->start_row = data->thread_id * rows_per_thread + (data->thread_id < remainder ? data->thread_id : remainder);
    data->end_row = data->start_row + rows_per_thread + (data->thread_id < remainder ? 1 : 0);
    
    int local_m = data->end_row - data->start_row;
    if (local_m <= 0) return NULL;
    
    // 调用单线程 GEMM 处理该线程的行范围
    rvv_gemm_f32(local_m, data->n, data->k,
                 data->C + data->start_row * data->n,
                 data->A + data->start_row * data->k,
                 data->B,
                 data->alpha, data->beta, data->config);
    
    return NULL;
}

void rvv_gemm_f32_multicore(int m, int n, int k, float* C, const float* A, const float* B,
                            float alpha, float beta, const rvv_config_t* config) {
    if (!config || config->num_threads <= 1 || m < config->num_threads * 4) {
        // 回退到单线程实现
        rvv_gemm_f32(m, n, k, C, A, B, alpha, beta, config);
        return;
    }
    
    int num_threads = config->num_threads;
    if (num_threads > rvv_get_num_cores()) {
        num_threads = rvv_get_num_cores();
    }
    
    pthread_t threads[num_threads];
    rvv_gemm_thread_data_t thread_data[num_threads];
    
    // 创建线程
    for (int i = 0; i < num_threads; i++) {
        thread_data[i] = (rvv_gemm_thread_data_t){
            .thread_id = i,
            .num_threads = num_threads,
            .m = m, .n = n, .k = k,
            .C = C, .A = A, .B = B,
            .alpha = alpha, .beta = beta,
            .config = config,
            .stats = NULL
        };
        
        if (pthread_create(&threads[i], NULL, rvv_gemm_thread_worker, &thread_data[i]) != 0) {
            // 如果线程创建失败，回退到单线程
            for (int j = 0; j < i; j++) {
                pthread_join(threads[j], NULL);
            }
            rvv_gemm_f32(m, n, k, C, A, B, alpha, beta, config);
            return;
        }
    }
    
    // 等待所有线程完成
    for (int i = 0; i < num_threads; i++) {
        pthread_join(threads[i], NULL);
    }
}

// ================================================================================================
// 多核向量操作实现
// ================================================================================================

typedef struct {
    int thread_id;
    int num_threads;
    int n;
    float* dst;
    const float* src0;
    const float* src1;
    int start_idx, end_idx;
    const rvv_config_t* config;
} rvv_vec_thread_data_t;

static void* rvv_vec_add_thread_worker(void* arg) {
    rvv_vec_thread_data_t* data = (rvv_vec_thread_data_t*)arg;
    
    rvv_calc_thread_range(data->n, data->thread_id, data->num_threads, 
                         &data->start_idx, &data->end_idx);
    
    int local_n = data->end_idx - data->start_idx;
    if (local_n <= 0) return NULL;
    
    rvv_vec_add_f32(local_n,
                    data->dst + data->start_idx,
                    data->src0 + data->start_idx,
                    data->src1 + data->start_idx,
                    data->config);
    
    return NULL;
}

void rvv_vec_add_f32_multicore(int n, float* dst, const float* src0, const float* src1, 
                               const rvv_config_t* config) {
    if (!config || config->num_threads <= 1 || n < config->num_threads * RVV_MIN_VEC_SIZE) {
        rvv_vec_add_f32(n, dst, src0, src1, config);
        return;
    }
    
    int num_threads = config->num_threads;
    if (num_threads > rvv_get_num_cores()) {
        num_threads = rvv_get_num_cores();
    }
    
    pthread_t threads[num_threads];
    rvv_vec_thread_data_t thread_data[num_threads];
    
    for (int i = 0; i < num_threads; i++) {
        thread_data[i] = (rvv_vec_thread_data_t){
            .thread_id = i,
            .num_threads = num_threads,
            .n = n,
            .dst = dst,
            .src0 = src0,
            .src1 = src1,
            .config = config
        };
        
        if (pthread_create(&threads[i], NULL, rvv_vec_add_thread_worker, &thread_data[i]) != 0) {
            for (int j = 0; j < i; j++) {
                pthread_join(threads[j], NULL);
            }
            rvv_vec_add_f32(n, dst, src0, src1, config);
            return;
        }
    }
    
    for (int i = 0; i < num_threads; i++) {
        pthread_join(threads[i], NULL);
    }
}

// ================================================================================================
// 多核激活函数实现
// ================================================================================================

typedef struct {
    int thread_id;
    int num_threads;
    int n;
    float* dst;
    const float* src;
    int start_idx, end_idx;
    const rvv_config_t* config;
    void (*activation_func)(int, float*, const float*, const rvv_config_t*);
} rvv_activation_thread_data_t;

static void* rvv_activation_thread_worker(void* arg) {
    rvv_activation_thread_data_t* data = (rvv_activation_thread_data_t*)arg;
    
    rvv_calc_thread_range(data->n, data->thread_id, data->num_threads,
                         &data->start_idx, &data->end_idx);
    
    int local_n = data->end_idx - data->start_idx;
    if (local_n <= 0) return NULL;
    
    data->activation_func(local_n,
                         data->dst + data->start_idx,
                         data->src + data->start_idx,
                         data->config);
    
    return NULL;
}

static void rvv_activation_multicore(int n, float* dst, const float* src, const rvv_config_t* config,
                                     void (*activation_func)(int, float*, const float*, const rvv_config_t*)) {
    if (!config || config->num_threads <= 1 || n < config->num_threads * RVV_MIN_VEC_SIZE) {
        activation_func(n, dst, src, config);
        return;
    }
    
    int num_threads = config->num_threads;
    if (num_threads > rvv_get_num_cores()) {
        num_threads = rvv_get_num_cores();
    }
    
    pthread_t threads[num_threads];
    rvv_activation_thread_data_t thread_data[num_threads];
    
    for (int i = 0; i < num_threads; i++) {
        thread_data[i] = (rvv_activation_thread_data_t){
            .thread_id = i,
            .num_threads = num_threads,
            .n = n,
            .dst = dst,
            .src = src,
            .config = config,
            .activation_func = activation_func
        };
        
        if (pthread_create(&threads[i], NULL, rvv_activation_thread_worker, &thread_data[i]) != 0) {
            for (int j = 0; j < i; j++) {
                pthread_join(threads[j], NULL);
            }
            activation_func(n, dst, src, config);
            return;
        }
    }
    
    for (int i = 0; i < num_threads; i++) {
        pthread_join(threads[i], NULL);
    }
}

void rvv_relu_f32_multicore(int n, float* dst, const float* src, const rvv_config_t* config) {
    rvv_activation_multicore(n, dst, src, config, rvv_relu_f32);
}

void rvv_gelu_f32_multicore(int n, float* dst, const float* src, const rvv_config_t* config) {
    rvv_activation_multicore(n, dst, src, config, rvv_gelu_f32);
}

void rvv_silu_f32_multicore(int n, float* dst, const float* src, const rvv_config_t* config) {
    rvv_activation_multicore(n, dst, src, config, rvv_silu_f32);
}

// ================================================================================================
// 流水线并行实现
// ================================================================================================

typedef struct {
    int stage_id;
    int num_stages;
    void* input_data;
    void* output_data;
    void (*stage_func)(void*, void*);
    pthread_mutex_t* input_mutex;
    pthread_mutex_t* output_mutex;
    pthread_cond_t* input_cond;
    pthread_cond_t* output_cond;
    volatile bool* done;
} rvv_pipeline_stage_t;

static void* rvv_pipeline_stage_worker(void* arg) {
    rvv_pipeline_stage_t* stage = (rvv_pipeline_stage_t*)arg;
    
    while (!*(stage->done)) {
        // 等待输入数据
        pthread_mutex_lock(stage->input_mutex);
        while (stage->input_data == NULL && !*(stage->done)) {
            pthread_cond_wait(stage->input_cond, stage->input_mutex);
        }
        
        if (*(stage->done)) {
            pthread_mutex_unlock(stage->input_mutex);
            break;
        }
        
        void* input = stage->input_data;
        stage->input_data = NULL;
        pthread_mutex_unlock(stage->input_mutex);
        
        // 处理数据
        stage->stage_func(input, stage->output_data);
        
        // 通知输出数据就绪
        pthread_mutex_lock(stage->output_mutex);
        pthread_cond_signal(stage->output_cond);
        pthread_mutex_unlock(stage->output_mutex);
    }
    
    return NULL;
}

// ================================================================================================
// 自适应负载均衡
// ================================================================================================

typedef struct {
    int num_threads;
    int* thread_loads;      // 每个线程的负载
    int* thread_priorities; // 线程优先级
    pthread_mutex_t load_mutex;
} rvv_load_balancer_t;

static rvv_load_balancer_t* rvv_create_load_balancer(int num_threads) {
    rvv_load_balancer_t* balancer = malloc(sizeof(rvv_load_balancer_t));
    if (!balancer) return NULL;
    
    balancer->num_threads = num_threads;
    balancer->thread_loads = calloc(num_threads, sizeof(int));
    balancer->thread_priorities = calloc(num_threads, sizeof(int));
    
    if (!balancer->thread_loads || !balancer->thread_priorities) {
        free(balancer->thread_loads);
        free(balancer->thread_priorities);
        free(balancer);
        return NULL;
    }
    
    pthread_mutex_init(&balancer->load_mutex, NULL);
    return balancer;
}

static void rvv_destroy_load_balancer(rvv_load_balancer_t* balancer) {
    if (balancer) {
        pthread_mutex_destroy(&balancer->load_mutex);
        free(balancer->thread_loads);
        free(balancer->thread_priorities);
        free(balancer);
    }
}

static int rvv_get_least_loaded_thread(rvv_load_balancer_t* balancer) {
    pthread_mutex_lock(&balancer->load_mutex);
    
    int min_load = balancer->thread_loads[0];
    int min_thread = 0;
    
    for (int i = 1; i < balancer->num_threads; i++) {
        if (balancer->thread_loads[i] < min_load) {
            min_load = balancer->thread_loads[i];
            min_thread = i;
        }
    }
    
    balancer->thread_loads[min_thread]++;
    pthread_mutex_unlock(&balancer->load_mutex);
    
    return min_thread;
}

static void rvv_update_thread_load(rvv_load_balancer_t* balancer, int thread_id, int load_delta) {
    pthread_mutex_lock(&balancer->load_mutex);
    balancer->thread_loads[thread_id] += load_delta;
    if (balancer->thread_loads[thread_id] < 0) {
        balancer->thread_loads[thread_id] = 0;
    }
    pthread_mutex_unlock(&balancer->load_mutex);
}
