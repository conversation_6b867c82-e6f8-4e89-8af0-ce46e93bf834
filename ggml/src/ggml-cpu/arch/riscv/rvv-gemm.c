#include "rvv-simd.h"
#include <string.h>
#include <assert.h>

#ifdef __riscv_v

// ================================================================================================
// RVV GEMM 矩阵乘法优化实现
// 支持多种数据类型和优化策略
// ================================================================================================

// GEMM 分块参数
#define RVV_GEMM_MC 256    // M 方向分块大小
#define RVV_GEMM_NC 128    // N 方向分块大小  
#define RVV_GEMM_KC 64     // K 方向分块大小

// ================================================================================================
// 单精度浮点 GEMM 内核 - 基础版本
// ================================================================================================

static void rvv_gemm_kernel_f32_basic(int m, int n, int k, 
                                      float* C, int ldc,
                                      const float* A, int lda,
                                      const float* B, int ldb,
                                      float alpha, float beta) {
    const size_t vl_max = RVV_VL_F32();
    
    for (int i = 0; i < m; i++) {
        for (int j = 0; j < n; j += vl_max) {
            size_t vl = __riscv_vsetvl_e32m1(n - j < vl_max ? n - j : vl_max);
            
            // 初始化累加器
            vfloat32m1_t v_acc = __riscv_vfmv_v_f_f32m1(0.0f, vl);
            
            // 如果 beta != 0，加载并缩放 C[i][j:j+vl]
            if (beta != 0.0f) {
                vfloat32m1_t v_c = __riscv_vle32_v_f32m1(&C[i * ldc + j], vl);
                if (beta != 1.0f) {
                    vfloat32m1_t v_beta = __riscv_vfmv_v_f_f32m1(beta, vl);
                    v_c = __riscv_vfmul_vv_f32m1(v_c, v_beta, vl);
                }
                v_acc = v_c;
            }
            
            // 内积计算
            for (int l = 0; l < k; l++) {
                // 广播 A[i][l]
                vfloat32m1_t v_a = __riscv_vfmv_v_f_f32m1(A[i * lda + l], vl);
                
                // 加载 B[l][j:j+vl]
                vfloat32m1_t v_b = __riscv_vle32_v_f32m1(&B[l * ldb + j], vl);
                
                // 融合乘加：acc += a * b
                v_acc = __riscv_vfmacc_vv_f32m1(v_acc, v_a, v_b, vl);
            }
            
            // 应用 alpha 缩放
            if (alpha != 1.0f) {
                vfloat32m1_t v_alpha = __riscv_vfmv_v_f_f32m1(alpha, vl);
                v_acc = __riscv_vfmul_vv_f32m1(v_acc, v_alpha, vl);
            }
            
            // 存储结果
            __riscv_vse32_v_f32m1(&C[i * ldc + j], v_acc, vl);
        }
    }
}

// ================================================================================================
// 单精度浮点 GEMM 内核 - 分块优化版本
// ================================================================================================

static void rvv_gemm_kernel_f32_blocked(int m, int n, int k,
                                        float* C, int ldc,
                                        const float* A, int lda,
                                        const float* B, int ldb,
                                        float alpha, float beta) {
    const size_t vl_max = RVV_VL_F32();
    const int unroll_m = 4;  // M 方向展开因子
    
    // 处理 M 方向的 4x 展开
    int i = 0;
    for (; i + unroll_m <= m; i += unroll_m) {
        for (int j = 0; j < n; j += vl_max) {
            size_t vl = __riscv_vsetvl_e32m1(n - j < vl_max ? n - j : vl_max);
            
            // 为 4 行初始化累加器
            vfloat32m1_t v_acc0 = __riscv_vfmv_v_f_f32m1(0.0f, vl);
            vfloat32m1_t v_acc1 = __riscv_vfmv_v_f_f32m1(0.0f, vl);
            vfloat32m1_t v_acc2 = __riscv_vfmv_v_f_f32m1(0.0f, vl);
            vfloat32m1_t v_acc3 = __riscv_vfmv_v_f_f32m1(0.0f, vl);
            
            // 如果 beta != 0，加载并缩放现有的 C 值
            if (beta != 0.0f) {
                vfloat32m1_t v_beta = __riscv_vfmv_v_f_f32m1(beta, vl);
                
                vfloat32m1_t v_c0 = __riscv_vle32_v_f32m1(&C[(i+0) * ldc + j], vl);
                vfloat32m1_t v_c1 = __riscv_vle32_v_f32m1(&C[(i+1) * ldc + j], vl);
                vfloat32m1_t v_c2 = __riscv_vle32_v_f32m1(&C[(i+2) * ldc + j], vl);
                vfloat32m1_t v_c3 = __riscv_vle32_v_f32m1(&C[(i+3) * ldc + j], vl);
                
                if (beta != 1.0f) {
                    v_c0 = __riscv_vfmul_vv_f32m1(v_c0, v_beta, vl);
                    v_c1 = __riscv_vfmul_vv_f32m1(v_c1, v_beta, vl);
                    v_c2 = __riscv_vfmul_vv_f32m1(v_c2, v_beta, vl);
                    v_c3 = __riscv_vfmul_vv_f32m1(v_c3, v_beta, vl);
                }
                
                v_acc0 = v_c0;
                v_acc1 = v_c1;
                v_acc2 = v_c2;
                v_acc3 = v_c3;
            }
            
            // 内积计算 - 4 行同时处理
            for (int l = 0; l < k; l++) {
                // 加载 B[l][j:j+vl]
                vfloat32m1_t v_b = __riscv_vle32_v_f32m1(&B[l * ldb + j], vl);
                
                // 广播 A 的 4 个元素并执行 FMA
                vfloat32m1_t v_a0 = __riscv_vfmv_v_f_f32m1(A[(i+0) * lda + l], vl);
                vfloat32m1_t v_a1 = __riscv_vfmv_v_f_f32m1(A[(i+1) * lda + l], vl);
                vfloat32m1_t v_a2 = __riscv_vfmv_v_f_f32m1(A[(i+2) * lda + l], vl);
                vfloat32m1_t v_a3 = __riscv_vfmv_v_f_f32m1(A[(i+3) * lda + l], vl);
                
                v_acc0 = __riscv_vfmacc_vv_f32m1(v_acc0, v_a0, v_b, vl);
                v_acc1 = __riscv_vfmacc_vv_f32m1(v_acc1, v_a1, v_b, vl);
                v_acc2 = __riscv_vfmacc_vv_f32m1(v_acc2, v_a2, v_b, vl);
                v_acc3 = __riscv_vfmacc_vv_f32m1(v_acc3, v_a3, v_b, vl);
            }
            
            // 应用 alpha 缩放
            if (alpha != 1.0f) {
                vfloat32m1_t v_alpha = __riscv_vfmv_v_f_f32m1(alpha, vl);
                v_acc0 = __riscv_vfmul_vv_f32m1(v_acc0, v_alpha, vl);
                v_acc1 = __riscv_vfmul_vv_f32m1(v_acc1, v_alpha, vl);
                v_acc2 = __riscv_vfmul_vv_f32m1(v_acc2, v_alpha, vl);
                v_acc3 = __riscv_vfmul_vv_f32m1(v_acc3, v_alpha, vl);
            }
            
            // 存储结果
            __riscv_vse32_v_f32m1(&C[(i+0) * ldc + j], v_acc0, vl);
            __riscv_vse32_v_f32m1(&C[(i+1) * ldc + j], v_acc1, vl);
            __riscv_vse32_v_f32m1(&C[(i+2) * ldc + j], v_acc2, vl);
            __riscv_vse32_v_f32m1(&C[(i+3) * ldc + j], v_acc3, vl);
        }
    }
    
    // 处理剩余的行
    for (; i < m; i++) {
        for (int j = 0; j < n; j += vl_max) {
            size_t vl = __riscv_vsetvl_e32m1(n - j < vl_max ? n - j : vl_max);
            
            vfloat32m1_t v_acc = __riscv_vfmv_v_f_f32m1(0.0f, vl);
            
            if (beta != 0.0f) {
                vfloat32m1_t v_c = __riscv_vle32_v_f32m1(&C[i * ldc + j], vl);
                if (beta != 1.0f) {
                    vfloat32m1_t v_beta = __riscv_vfmv_v_f_f32m1(beta, vl);
                    v_c = __riscv_vfmul_vv_f32m1(v_c, v_beta, vl);
                }
                v_acc = v_c;
            }
            
            for (int l = 0; l < k; l++) {
                vfloat32m1_t v_a = __riscv_vfmv_v_f_f32m1(A[i * lda + l], vl);
                vfloat32m1_t v_b = __riscv_vle32_v_f32m1(&B[l * ldb + j], vl);
                v_acc = __riscv_vfmacc_vv_f32m1(v_acc, v_a, v_b, vl);
            }
            
            if (alpha != 1.0f) {
                vfloat32m1_t v_alpha = __riscv_vfmv_v_f_f32m1(alpha, vl);
                v_acc = __riscv_vfmul_vv_f32m1(v_acc, v_alpha, vl);
            }
            
            __riscv_vse32_v_f32m1(&C[i * ldc + j], v_acc, vl);
        }
    }
}

// ================================================================================================
// 主 GEMM 函数 - 支持多种优化策略
// ================================================================================================

void rvv_gemm_f32(int m, int n, int k, float* C, const float* A, const float* B, 
                  float alpha, float beta, const rvv_config_t* config) {
    if (!RVV_AVAILABLE() || m < 4 || n < RVV_VL_F32() || k < 4) {
        // 回退到标量实现
        for (int i = 0; i < m; i++) {
            for (int j = 0; j < n; j++) {
                float sum = 0.0f;
                for (int l = 0; l < k; l++) {
                    sum += A[i * k + l] * B[l * n + j];
                }
                C[i * n + j] = alpha * sum + beta * C[i * n + j];
            }
        }
        return;
    }
    
    // 选择优化策略
    switch (config->strategy) {
        case RVV_STRATEGY_BASIC:
            rvv_gemm_kernel_f32_basic(m, n, k, C, n, A, k, B, n, alpha, beta);
            break;
            
        case RVV_STRATEGY_UNROLL_4X:
            rvv_gemm_kernel_f32_blocked(m, n, k, C, n, A, k, B, n, alpha, beta);
            break;
            
        default:
            rvv_gemm_kernel_f32_basic(m, n, k, C, n, A, k, B, n, alpha, beta);
            break;
    }
}

// ================================================================================================
// GEMV (矩阵-向量乘法) 优化实现
// ================================================================================================

void rvv_gemv_f32(int m, int n, float* y, const float* A, const float* x, 
                  float alpha, float beta, const rvv_config_t* config) {
    if (!RVV_AVAILABLE() || n < RVV_VL_F32()) {
        // 回退到标量实现
        for (int i = 0; i < m; i++) {
            float sum = 0.0f;
            for (int j = 0; j < n; j++) {
                sum += A[i * n + j] * x[j];
            }
            y[i] = alpha * sum + beta * y[i];
        }
        return;
    }
    
    const size_t vl_max = RVV_VL_F32();
    
    for (int i = 0; i < m; i++) {
        vfloat32m1_t v_sum = __riscv_vfmv_v_f_f32m1(0.0f, vl_max);
        
        int j = 0;
        // 向量化内积计算
        for (; j + vl_max <= n; j += vl_max) {
            size_t vl = __riscv_vsetvl_e32m1(vl_max);
            
            vfloat32m1_t v_a = __riscv_vle32_v_f32m1(&A[i * n + j], vl);
            vfloat32m1_t v_x = __riscv_vle32_v_f32m1(&x[j], vl);
            
            v_sum = __riscv_vfmacc_vv_f32m1(v_sum, v_a, v_x, vl);
        }
        
        // 归约求和
        vfloat32m1_t v_zero = __riscv_vfmv_v_f_f32m1(0.0f, vl_max);
        vfloat32m1_t v_result = __riscv_vfredosum_vs_f32m1_f32m1(v_sum, v_zero, vl_max);
        float sum = __riscv_vfmv_f_s_f32m1_f32(v_result);
        
        // 处理剩余元素
        for (; j < n; j++) {
            sum += A[i * n + j] * x[j];
        }
        
        // 应用 alpha 和 beta
        y[i] = alpha * sum + beta * y[i];
    }
}

#else

// 非 RVV 环境下的回退实现
void rvv_gemm_f32(int m, int n, int k, float* C, const float* A, const float* B, 
                  float alpha, float beta, const rvv_config_t* config) {
    for (int i = 0; i < m; i++) {
        for (int j = 0; j < n; j++) {
            float sum = 0.0f;
            for (int l = 0; l < k; l++) {
                sum += A[i * k + l] * B[l * n + j];
            }
            C[i * n + j] = alpha * sum + beta * C[i * n + j];
        }
    }
}

void rvv_gemv_f32(int m, int n, float* y, const float* A, const float* x, 
                  float alpha, float beta, const rvv_config_t* config) {
    for (int i = 0; i < m; i++) {
        float sum = 0.0f;
        for (int j = 0; j < n; j++) {
            sum += A[i * n + j] * x[j];
        }
        y[i] = alpha * sum + beta * y[i];
    }
}

#endif
