#include "rvv-simd.h"
#include <math.h>
#include <string.h>

#ifdef __riscv_v

// ================================================================================================
// 注意力机制优化实现 - RISC-V Vector Extension
// ================================================================================================

// Scaled Dot-Product Attention: Attention(Q,K,V) = softmax(QK^T/√d_k)V
void rvv_scaled_dot_product_attention_f32(
    int batch_size, int seq_len, int head_dim,
    float* output,           // [batch_size, seq_len, head_dim]
    const float* query,      // [batch_size, seq_len, head_dim]
    const float* key,        // [batch_size, seq_len, head_dim]
    const float* value,      // [batch_size, seq_len, head_dim]
    const float* mask,       // [batch_size, seq_len, seq_len] or NULL
    float scale,             // 1/√d_k
    const rvv_config_t* config) {
    
    if (!RVV_AVAILABLE()) {
        // 标量回退实现
        for (int b = 0; b < batch_size; b++) {
            for (int i = 0; i < seq_len; i++) {
                // 计算注意力分数
                float scores[seq_len];
                for (int j = 0; j < seq_len; j++) {
                    float score = 0.0f;
                    for (int d = 0; d < head_dim; d++) {
                        score += query[b * seq_len * head_dim + i * head_dim + d] *
                                key[b * seq_len * head_dim + j * head_dim + d];
                    }
                    scores[j] = score * scale;
                    if (mask) {
                        scores[j] += mask[b * seq_len * seq_len + i * seq_len + j];
                    }
                }
                
                // Softmax
                rvv_softmax_f32(seq_len, scores, scores, config);
                
                // 计算输出
                for (int d = 0; d < head_dim; d++) {
                    float sum = 0.0f;
                    for (int j = 0; j < seq_len; j++) {
                        sum += scores[j] * value[b * seq_len * head_dim + j * head_dim + d];
                    }
                    output[b * seq_len * head_dim + i * head_dim + d] = sum;
                }
            }
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    
    for (int b = 0; b < batch_size; b++) {
        for (int i = 0; i < seq_len; i++) {
            // 计算注意力分数 QK^T
            float scores[seq_len];
            
            for (int j = 0; j < seq_len; j++) {
                vfloat32m1_t v_sum = __riscv_vfmv_v_f_f32m1(0.0f, vl_max);
                
                int d = 0;
                // 向量化内积计算
                for (; d + vl_max <= head_dim; d += vl_max) {
                    size_t vl = __riscv_vsetvl_e32m1(vl_max);
                    
                    vfloat32m1_t v_q = __riscv_vle32_v_f32m1(
                        &query[b * seq_len * head_dim + i * head_dim + d], vl);
                    vfloat32m1_t v_k = __riscv_vle32_v_f32m1(
                        &key[b * seq_len * head_dim + j * head_dim + d], vl);
                    
                    v_sum = __riscv_vfmacc_vv_f32m1(v_sum, v_q, v_k, vl);
                }
                
                // 归约求和
                vfloat32m1_t v_zero = __riscv_vfmv_v_f_f32m1(0.0f, vl_max);
                vfloat32m1_t v_result = __riscv_vfredosum_vs_f32m1_f32m1(v_sum, v_zero, vl_max);
                float score = __riscv_vfmv_f_s_f32m1_f32(v_result);
                
                // 处理剩余元素
                for (; d < head_dim; d++) {
                    score += query[b * seq_len * head_dim + i * head_dim + d] *
                            key[b * seq_len * head_dim + j * head_dim + d];
                }
                
                scores[j] = score * scale;
                if (mask) {
                    scores[j] += mask[b * seq_len * seq_len + i * seq_len + j];
                }
            }
            
            // 应用 Softmax
            rvv_softmax_f32(seq_len, scores, scores, config);
            
            // 计算输出 = attention_weights * V
            int d = 0;
            for (; d + vl_max <= head_dim; d += vl_max) {
                size_t vl = __riscv_vsetvl_e32m1(vl_max);
                vfloat32m1_t v_output = __riscv_vfmv_v_f_f32m1(0.0f, vl);
                
                for (int j = 0; j < seq_len; j++) {
                    vfloat32m1_t v_score = __riscv_vfmv_v_f_f32m1(scores[j], vl);
                    vfloat32m1_t v_value = __riscv_vle32_v_f32m1(
                        &value[b * seq_len * head_dim + j * head_dim + d], vl);
                    
                    v_output = __riscv_vfmacc_vv_f32m1(v_output, v_score, v_value, vl);
                }
                
                __riscv_vse32_v_f32m1(&output[b * seq_len * head_dim + i * head_dim + d], v_output, vl);
            }
            
            // 处理剩余维度
            for (; d < head_dim; d++) {
                float sum = 0.0f;
                for (int j = 0; j < seq_len; j++) {
                    sum += scores[j] * value[b * seq_len * head_dim + j * head_dim + d];
                }
                output[b * seq_len * head_dim + i * head_dim + d] = sum;
            }
        }
    }
}

// ================================================================================================
// 多头注意力机制优化实现
// ================================================================================================

void rvv_multi_head_attention_f32(
    int batch_size, int seq_len, int num_heads, int head_dim,
    float* output,           // [batch_size, seq_len, num_heads * head_dim]
    const float* query,      // [batch_size, seq_len, num_heads * head_dim]
    const float* key,        // [batch_size, seq_len, num_heads * head_dim]
    const float* value,      // [batch_size, seq_len, num_heads * head_dim]
    const float* mask,       // [batch_size, seq_len, seq_len] or NULL
    const rvv_config_t* config) {
    
    float scale = 1.0f / sqrtf((float)head_dim);
    
    for (int h = 0; h < num_heads; h++) {
        // 提取每个头的 Q, K, V
        const float* q_head = query + h * head_dim;
        const float* k_head = key + h * head_dim;
        const float* v_head = value + h * head_dim;
        float* o_head = output + h * head_dim;
        
        // 对每个头应用注意力机制
        rvv_scaled_dot_product_attention_f32(
            batch_size, seq_len, head_dim,
            o_head, q_head, k_head, v_head, mask, scale, config);
    }
}

// ================================================================================================
// 位置编码优化实现
// ================================================================================================

// Sinusoidal Position Encoding
void rvv_sinusoidal_position_encoding_f32(
    int seq_len, int d_model,
    float* pos_encoding,     // [seq_len, d_model]
    const rvv_config_t* config) {
    
    if (!RVV_AVAILABLE()) {
        // 标量实现
        for (int pos = 0; pos < seq_len; pos++) {
            for (int i = 0; i < d_model; i += 2) {
                float angle = pos / powf(10000.0f, (float)i / d_model);
                pos_encoding[pos * d_model + i] = sinf(angle);
                if (i + 1 < d_model) {
                    pos_encoding[pos * d_model + i + 1] = cosf(angle);
                }
            }
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    
    for (int pos = 0; pos < seq_len; pos++) {
        vfloat32m1_t v_pos = __riscv_vfmv_v_f_f32m1((float)pos, vl_max);
        
        int i = 0;
        for (; i + vl_max <= d_model; i += vl_max) {
            size_t vl = __riscv_vsetvl_e32m1(vl_max);
            
            // 创建维度索引向量
            float dim_indices[vl_max];
            for (size_t j = 0; j < vl; j++) {
                dim_indices[j] = (float)(i + j);
            }
            vfloat32m1_t v_dim = __riscv_vle32_v_f32m1(dim_indices, vl);
            
            // 计算 angle = pos / 10000^(dim/d_model)
            vfloat32m1_t v_d_model = __riscv_vfmv_v_f_f32m1((float)d_model, vl);
            vfloat32m1_t v_ratio = __riscv_vfdiv_vv_f32m1(v_dim, v_d_model, vl);
            
            // 计算 10000^ratio (需要标量回退)
            float ratios[vl_max];
            __riscv_vse32_v_f32m1(ratios, v_ratio, vl);
            for (size_t j = 0; j < vl; j++) {
                ratios[j] = powf(10000.0f, ratios[j]);
            }
            vfloat32m1_t v_base = __riscv_vle32_v_f32m1(ratios, vl);
            
            // 计算角度
            vfloat32m1_t v_angle = __riscv_vfdiv_vv_f32m1(v_pos, v_base, vl);
            
            // 计算 sin 和 cos (需要标量回退)
            float angles[vl_max];
            __riscv_vse32_v_f32m1(angles, v_angle, vl);
            
            for (size_t j = 0; j < vl; j += 2) {
                if (i + j < d_model) {
                    pos_encoding[pos * d_model + i + j] = sinf(angles[j]);
                }
                if (i + j + 1 < d_model) {
                    pos_encoding[pos * d_model + i + j + 1] = cosf(angles[j]);
                }
            }
        }
        
        // 处理剩余元素
        for (; i < d_model; i += 2) {
            float angle = pos / powf(10000.0f, (float)i / d_model);
            pos_encoding[pos * d_model + i] = sinf(angle);
            if (i + 1 < d_model) {
                pos_encoding[pos * d_model + i + 1] = cosf(angle);
            }
        }
    }
}

// ================================================================================================
// 嵌入层优化实现
// ================================================================================================

// Token Embedding Lookup
void rvv_embedding_lookup_f32(
    int batch_size, int seq_len, int vocab_size, int embed_dim,
    float* output,           // [batch_size, seq_len, embed_dim]
    const int* input_ids,    // [batch_size, seq_len]
    const float* embedding_table, // [vocab_size, embed_dim]
    const rvv_config_t* config) {
    
    if (!RVV_AVAILABLE()) {
        // 标量实现
        for (int b = 0; b < batch_size; b++) {
            for (int s = 0; s < seq_len; s++) {
                int token_id = input_ids[b * seq_len + s];
                if (token_id >= 0 && token_id < vocab_size) {
                    memcpy(&output[b * seq_len * embed_dim + s * embed_dim],
                           &embedding_table[token_id * embed_dim],
                           embed_dim * sizeof(float));
                }
            }
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    
    for (int b = 0; b < batch_size; b++) {
        for (int s = 0; s < seq_len; s++) {
            int token_id = input_ids[b * seq_len + s];
            if (token_id >= 0 && token_id < vocab_size) {
                const float* src = &embedding_table[token_id * embed_dim];
                float* dst = &output[b * seq_len * embed_dim + s * embed_dim];
                
                int d = 0;
                // 向量化复制
                for (; d + vl_max <= embed_dim; d += vl_max) {
                    size_t vl = __riscv_vsetvl_e32m1(vl_max);
                    vfloat32m1_t v_embed = __riscv_vle32_v_f32m1(src + d, vl);
                    __riscv_vse32_v_f32m1(dst + d, v_embed, vl);
                }
                
                // 处理剩余元素
                for (; d < embed_dim; d++) {
                    dst[d] = src[d];
                }
            }
        }
    }
}

// 添加位置编码到嵌入
void rvv_add_position_encoding_f32(
    int batch_size, int seq_len, int embed_dim,
    float* embeddings,       // [batch_size, seq_len, embed_dim] (in-place)
    const float* pos_encoding, // [seq_len, embed_dim]
    const rvv_config_t* config) {
    
    if (!RVV_AVAILABLE()) {
        // 标量实现
        for (int b = 0; b < batch_size; b++) {
            for (int s = 0; s < seq_len; s++) {
                for (int d = 0; d < embed_dim; d++) {
                    embeddings[b * seq_len * embed_dim + s * embed_dim + d] += 
                        pos_encoding[s * embed_dim + d];
                }
            }
        }
        return;
    }

    const size_t vl_max = RVV_VL_F32();
    
    for (int b = 0; b < batch_size; b++) {
        for (int s = 0; s < seq_len; s++) {
            float* embed_ptr = &embeddings[b * seq_len * embed_dim + s * embed_dim];
            const float* pos_ptr = &pos_encoding[s * embed_dim];
            
            int d = 0;
            for (; d + vl_max <= embed_dim; d += vl_max) {
                size_t vl = __riscv_vsetvl_e32m1(vl_max);
                
                vfloat32m1_t v_embed = __riscv_vle32_v_f32m1(embed_ptr + d, vl);
                vfloat32m1_t v_pos = __riscv_vle32_v_f32m1(pos_ptr + d, vl);
                vfloat32m1_t v_result = __riscv_vfadd_vv_f32m1(v_embed, v_pos, vl);
                
                __riscv_vse32_v_f32m1(embed_ptr + d, v_result, vl);
            }
            
            // 处理剩余元素
            for (; d < embed_dim; d++) {
                embed_ptr[d] += pos_ptr[d];
            }
        }
    }
}

#else

// 非 RVV 环境下的回退实现
void rvv_scaled_dot_product_attention_f32(
    int batch_size, int seq_len, int head_dim,
    float* output, const float* query, const float* key, const float* value,
    const float* mask, float scale, const rvv_config_t* config) {
    // 标量实现...
}

void rvv_multi_head_attention_f32(
    int batch_size, int seq_len, int num_heads, int head_dim,
    float* output, const float* query, const float* key, const float* value,
    const float* mask, const rvv_config_t* config) {
    // 标量实现...
}

void rvv_sinusoidal_position_encoding_f32(
    int seq_len, int d_model, float* pos_encoding, const rvv_config_t* config) {
    // 标量实现...
}

void rvv_embedding_lookup_f32(
    int batch_size, int seq_len, int vocab_size, int embed_dim,
    float* output, const int* input_ids, const float* embedding_table,
    const rvv_config_t* config) {
    // 标量实现...
}

void rvv_add_position_encoding_f32(
    int batch_size, int seq_len, int embed_dim,
    float* embeddings, const float* pos_encoding, const rvv_config_t* config) {
    // 标量实现...
}

#endif
