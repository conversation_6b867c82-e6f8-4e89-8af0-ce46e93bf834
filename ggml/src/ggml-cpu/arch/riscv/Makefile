# RISC-V Vector Extension (RVV) 优化模块 Makefile
# 用于编译和测试 RVV 优化实现

# 编译器设置
CC = riscv64-unknown-linux-gnu-gcc
CXX = riscv64-unknown-linux-gnu-g++

# 如果是本地 RISC-V 系统，使用本地编译器
ifeq ($(shell uname -m), riscv64)
    CC = gcc
    CXX = g++
endif

# 编译标志
CFLAGS = -std=c11 -Wall -Wextra -O3 -march=rv64gcv -mabi=lp64d
CFLAGS += -ffast-math -funroll-loops -fomit-frame-pointer
CFLAGS += -DRVV_OPTIMIZATION_ENABLED

# 调试标志
DEBUG_CFLAGS = -std=c11 -Wall -Wextra -O0 -g -march=rv64gcv -mabi=lp64d
DEBUG_CFLAGS += -DRVV_DEBUG_MODE -DRVV_DEBUG_VERBOSE

# 包含目录
INCLUDES = -I. -I../../.. -I../../../../include

# 链接库
LIBS = -lm -lpthread

# 源文件
SOURCES = rvv-activations.c rvv-gemm.c rvv-math.c rvv-multicore.c rvv-attention.c rvv-test.c
OBJECTS = $(SOURCES:.c=.o)

# 测试程序源文件
TEST_SOURCES = ../../../../tests/test-rvv-optimization.c
TEST_OBJECTS = $(TEST_SOURCES:.c=.o)

# 目标文件
LIBRARY = libggml-rvv-opt.a
TEST_PROGRAM = test-rvv-optimization
BENCHMARK_PROGRAM = rvv-benchmark

# 默认目标
all: $(LIBRARY) $(TEST_PROGRAM) $(BENCHMARK_PROGRAM)

# 编译静态库
$(LIBRARY): $(OBJECTS)
	@echo "创建静态库 $@"
	ar rcs $@ $^

# 编译测试程序
$(TEST_PROGRAM): $(TEST_OBJECTS) $(LIBRARY)
	@echo "编译测试程序 $@"
	$(CC) $(CFLAGS) $(INCLUDES) -o $@ $^ $(LIBS)

# 编译基准测试程序
$(BENCHMARK_PROGRAM): $(OBJECTS)
	@echo "编译基准测试程序 $@"
	$(CC) $(CFLAGS) $(INCLUDES) -DRVV_BENCHMARK_MAIN -o $@ $^ $(LIBS)

# 编译目标文件
%.o: %.c
	@echo "编译 $<"
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# 调试版本
debug: CFLAGS = $(DEBUG_CFLAGS)
debug: clean $(LIBRARY) $(TEST_PROGRAM)

# 运行测试
test: $(TEST_PROGRAM)
	@echo "运行功能测试..."
	./$(TEST_PROGRAM) --test

# 运行基准测试
benchmark: $(TEST_PROGRAM)
	@echo "运行性能基准测试..."
	./$(TEST_PROGRAM) --bench

# 运行多核测试
multicore: $(TEST_PROGRAM)
	@echo "运行多核扩展性测试..."
	./$(TEST_PROGRAM) --multicore

# 运行所有测试
test-all: $(TEST_PROGRAM)
	@echo "运行所有测试..."
	./$(TEST_PROGRAM) --all

# 显示系统信息
info: $(TEST_PROGRAM)
	@echo "显示系统信息..."
	./$(TEST_PROGRAM) --info --features

# 性能分析版本
profile: CFLAGS += -pg
profile: LIBS += -pg
profile: clean $(LIBRARY) $(TEST_PROGRAM)

# 代码覆盖率版本
coverage: CFLAGS = $(DEBUG_CFLAGS) --coverage
coverage: LIBS += --coverage
coverage: clean $(LIBRARY) $(TEST_PROGRAM)
	@echo "运行覆盖率测试..."
	./$(TEST_PROGRAM) --all
	gcov $(SOURCES)

# 静态分析
static-analysis:
	@echo "运行静态分析..."
	@if command -v clang-tidy >/dev/null 2>&1; then \
		clang-tidy $(SOURCES) -- $(CFLAGS) $(INCLUDES); \
	else \
		echo "clang-tidy 未找到，跳过静态分析"; \
	fi

# 内存检查版本
sanitize: CFLAGS += -fsanitize=address -fsanitize=undefined -fno-omit-frame-pointer
sanitize: LIBS += -fsanitize=address -fsanitize=undefined
sanitize: clean $(LIBRARY) $(TEST_PROGRAM)

# 向量化报告
vectorization-report: CFLAGS += -fopt-info-vec-optimized -fopt-info-vec-missed
vectorization-report: clean $(LIBRARY)

# 交叉编译设置
cross-compile:
	$(MAKE) CC=riscv64-unknown-linux-gnu-gcc CXX=riscv64-unknown-linux-gnu-g++

# 安装
install: $(LIBRARY) $(TEST_PROGRAM)
	@echo "安装 RVV 优化库..."
	mkdir -p $(DESTDIR)/usr/local/lib
	mkdir -p $(DESTDIR)/usr/local/include/ggml/cpu/arch/riscv
	mkdir -p $(DESTDIR)/usr/local/bin
	cp $(LIBRARY) $(DESTDIR)/usr/local/lib/
	cp rvv-simd.h $(DESTDIR)/usr/local/include/ggml/cpu/arch/riscv/
	cp $(TEST_PROGRAM) $(DESTDIR)/usr/local/bin/

# 卸载
uninstall:
	@echo "卸载 RVV 优化库..."
	rm -f $(DESTDIR)/usr/local/lib/$(LIBRARY)
	rm -f $(DESTDIR)/usr/local/include/ggml/cpu/arch/riscv/rvv-simd.h
	rm -f $(DESTDIR)/usr/local/bin/$(TEST_PROGRAM)

# 清理
clean:
	@echo "清理编译文件..."
	rm -f $(OBJECTS) $(TEST_OBJECTS) $(LIBRARY) $(TEST_PROGRAM) $(BENCHMARK_PROGRAM)
	rm -f *.gcov *.gcda *.gcno
	rm -f gmon.out

# 深度清理
distclean: clean
	rm -rf build/
	rm -f *.log

# 创建发布包
dist: clean
	@echo "创建发布包..."
	tar -czf rvv-optimization-$(shell date +%Y%m%d).tar.gz \
		*.c *.h Makefile CMakeLists.txt README.md

# 帮助信息
help:
	@echo "RVV 优化模块 Makefile 使用说明:"
	@echo ""
	@echo "编译目标:"
	@echo "  all                 - 编译所有目标 (默认)"
	@echo "  debug              - 编译调试版本"
	@echo "  profile            - 编译性能分析版本"
	@echo "  coverage           - 编译代码覆盖率版本"
	@echo "  sanitize           - 编译内存检查版本"
	@echo "  cross-compile      - 交叉编译"
	@echo ""
	@echo "测试目标:"
	@echo "  test               - 运行功能测试"
	@echo "  benchmark          - 运行性能基准测试"
	@echo "  multicore          - 运行多核扩展性测试"
	@echo "  test-all           - 运行所有测试"
	@echo "  info               - 显示系统信息"
	@echo ""
	@echo "分析目标:"
	@echo "  static-analysis    - 运行静态代码分析"
	@echo "  vectorization-report - 生成向量化报告"
	@echo ""
	@echo "维护目标:"
	@echo "  clean              - 清理编译文件"
	@echo "  distclean          - 深度清理"
	@echo "  install            - 安装库和头文件"
	@echo "  uninstall          - 卸载库和头文件"
	@echo "  dist               - 创建发布包"
	@echo "  help               - 显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make               - 编译所有目标"
	@echo "  make test-all      - 编译并运行所有测试"
	@echo "  make debug test    - 编译调试版本并运行测试"
	@echo "  make cross-compile - 交叉编译到 RISC-V"

# 检查依赖
check-deps:
	@echo "检查编译依赖..."
	@command -v $(CC) >/dev/null 2>&1 || { echo "错误: $(CC) 未找到"; exit 1; }
	@echo "编译器: $(CC)"
	@$(CC) --version | head -1
	@echo "目标架构: $(shell $(CC) -dumpmachine)"

# 显示编译信息
show-config:
	@echo "编译配置:"
	@echo "  CC = $(CC)"
	@echo "  CFLAGS = $(CFLAGS)"
	@echo "  INCLUDES = $(INCLUDES)"
	@echo "  LIBS = $(LIBS)"
	@echo "  SOURCES = $(SOURCES)"

# 伪目标声明
.PHONY: all debug test benchmark multicore test-all info profile coverage \
        static-analysis sanitize vectorization-report cross-compile \
        install uninstall clean distclean dist help check-deps show-config
