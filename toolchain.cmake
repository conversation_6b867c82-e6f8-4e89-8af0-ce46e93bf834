#set(CMAKE_C_COMPILER /home/<USER>/gcc/bin/riscv64-unknown-linux-gnu-gcc)
#set(CMAKE_CXX_COMPILER /home/<USER>/gcc/bin/riscv64-unknown-linux-gnu-g++)

set(CMAKE_C_COMPILER /home/<USER>/llvm/bin/clang)
set(CMAKE_CXX_COMPILER /home/<USER>/llvm/bin/clang++)

set(CMAKE_C_FLAGS "-fno-omit-frame-pointer -mcpu=c910v3-cp -march=rv64imafdc_zicntr_zicsr_zifencei_zihpm_zfh_xtheadc -O3 -g" CACHE STRING "C Compiler flags")
set(CMAKE_CXX_FLAGS "-fno-omit-frame-pointer -mcpu=c910v3-cp -march=rv64imafdc_zicntr_zicsr_zifencei_zihpm_zfh_xtheadc -O3 -g" CACHE STRING "C++ Compiler flags")

#set(CMAKE_C_FLAGS "-O3" CACHE STRING "C Compiler flags")
#set(CMAKE_CXX_FLAGS "-O3" CACHE STRING "C++ Compiler flags")
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR riscv64)
set(CMAKE_SYSTEM_ARCH riscv64)
set(CMAKE_SYSROOT /home/<USER>/llvm/sysroot)
