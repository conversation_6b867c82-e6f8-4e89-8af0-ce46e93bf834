# Locale name alias data base.
# Copyright (C) 1996-2021 Free Software Foundation, Inc.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2, or (at your option)
# any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, see <https://www.gnu.org/licenses/>.

# The format of this file is the same as for the corresponding file of
# the X Window System, which normally can be found in
#	/usr/lib/X11/locale/locale.alias
# A single line contains two fields: an alias and a substitution value.
# All entries are case independent.

# Note: This file is obsolete and is kept around for the time being for
# backward compatibility.  Nobody should rely on the names defined here.
# Locales should always be specified by their full name.

# Note: This file used to contain the following lines:
#	bokmaal		nb_NO.ISO-8859-1
#	franc,ais	fr_FR.ISO-8859-1
# except that the "aa" was actually the byte '\0xE5' (the Latin-1
# encoding for U+00E5 LATIN SMALL LETTER A WITH RING ABOVE) and the
# "c," was actually the byte '\xE7' (the Latin-1 encoding for U+00E7
# LATIN SMALL LETTER C WITH CEDILLA).  These lines were removed
# because they caused 'locale -a' to output text encoded in Latin-1,
# which broke applications in UTF-8 locales.  See:
# https://sourceware.org/bugzilla/show_bug.cgi?id=18412

bokmal		nb_NO.ISO-8859-1
catalan		ca_ES.ISO-8859-1
croatian	hr_HR.ISO-8859-2
czech		cs_CZ.ISO-8859-2
danish          da_DK.ISO-8859-1
dansk		da_DK.ISO-8859-1
deutsch		de_DE.ISO-8859-1
dutch		nl_NL.ISO-8859-1
eesti		et_EE.ISO-8859-1
estonian	et_EE.ISO-8859-1
finnish         fi_FI.ISO-8859-1
french		fr_FR.ISO-8859-1
galego		gl_ES.ISO-8859-1
galician	gl_ES.ISO-8859-1
german		de_DE.ISO-8859-1
greek           el_GR.ISO-8859-7
hebrew          he_IL.ISO-8859-8
hrvatski	hr_HR.ISO-8859-2
hungarian       hu_HU.ISO-8859-2
icelandic       is_IS.ISO-8859-1
italian         it_IT.ISO-8859-1
japanese	ja_JP.eucJP
japanese.euc	ja_JP.eucJP
ja_JP		ja_JP.eucJP
ja_JP.ujis	ja_JP.eucJP
japanese.sjis	ja_JP.SJIS
korean		ko_KR.eucKR
korean.euc 	ko_KR.eucKR
ko_KR		ko_KR.eucKR
lithuanian      lt_LT.ISO-8859-13
no_NO		nb_NO.ISO-8859-1
no_NO.ISO-8859-1 nb_NO.ISO-8859-1
norwegian       nb_NO.ISO-8859-1
nynorsk		nn_NO.ISO-8859-1
polish          pl_PL.ISO-8859-2
portuguese      pt_PT.ISO-8859-1
romanian        ro_RO.ISO-8859-2
russian         ru_RU.ISO-8859-5
slovak          sk_SK.ISO-8859-2
slovene         sl_SI.ISO-8859-2
slovenian       sl_SI.ISO-8859-2
spanish         es_ES.ISO-8859-1
swedish         sv_SE.ISO-8859-1
thai		th_TH.TIS-620
turkish         tr_TR.ISO-8859-9
