This is libc.info, produced by makeinfo version 6.8 from libc.texinfo.

This is ‘The GNU C Library Reference Manual’, for version 2.33.

   Copyright © 1993–2021 Free Software Foundation, Inc.

   Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being “Free Software Needs Free Documentation” and
“GNU Lesser General Public License”, the Front-Cover texts being “A GNU
Manual”, and with the Back-Cover Texts as in (a) below.  A copy of the
license is included in the section entitled "GNU Free Documentation
License".

   (a) The FSF’s Back-Cover Text is: “You have the freedom to copy and
modify this GNU manual.  Buying copies from the FSF supports it in
developing GNU and promoting software freedom.”
INFO-DIR-SECTION Software libraries
START-INFO-DIR-ENTRY
* Libc: (libc).                 C library.
END-INFO-DIR-ENTRY

INFO-DIR-SECTION GNU C library functions and macros
START-INFO-DIR-ENTRY
* ALTWERASE: (libc)Local Modes.
* ARGP_ERR_UNKNOWN: (libc)Argp Parser Functions.
* ARG_MAX: (libc)General Limits.
* BC_BASE_MAX: (libc)Utility Limits.
* BC_DIM_MAX: (libc)Utility Limits.
* BC_SCALE_MAX: (libc)Utility Limits.
* BC_STRING_MAX: (libc)Utility Limits.
* BRKINT: (libc)Input Modes.
* BUFSIZ: (libc)Controlling Buffering.
* CCTS_OFLOW: (libc)Control Modes.
* CHAR_BIT: (libc)Width of Type.
* CHILD_MAX: (libc)General Limits.
* CIGNORE: (libc)Control Modes.
* CLK_TCK: (libc)Processor Time.
* CLOCAL: (libc)Control Modes.
* CLOCKS_PER_SEC: (libc)CPU Time.
* CLOCK_MONOTONIC: (libc)Getting the Time.
* CLOCK_REALTIME: (libc)Getting the Time.
* COLL_WEIGHTS_MAX: (libc)Utility Limits.
* CPU_CLR: (libc)CPU Affinity.
* CPU_FEATURE_USABLE: (libc)X86.
* CPU_ISSET: (libc)CPU Affinity.
* CPU_SET: (libc)CPU Affinity.
* CPU_SETSIZE: (libc)CPU Affinity.
* CPU_ZERO: (libc)CPU Affinity.
* CREAD: (libc)Control Modes.
* CRTS_IFLOW: (libc)Control Modes.
* CS5: (libc)Control Modes.
* CS6: (libc)Control Modes.
* CS7: (libc)Control Modes.
* CS8: (libc)Control Modes.
* CSIZE: (libc)Control Modes.
* CSTOPB: (libc)Control Modes.
* DTTOIF: (libc)Directory Entries.
* E2BIG: (libc)Error Codes.
* EACCES: (libc)Error Codes.
* EADDRINUSE: (libc)Error Codes.
* EADDRNOTAVAIL: (libc)Error Codes.
* EADV: (libc)Error Codes.
* EAFNOSUPPORT: (libc)Error Codes.
* EAGAIN: (libc)Error Codes.
* EALREADY: (libc)Error Codes.
* EAUTH: (libc)Error Codes.
* EBACKGROUND: (libc)Error Codes.
* EBADE: (libc)Error Codes.
* EBADF: (libc)Error Codes.
* EBADFD: (libc)Error Codes.
* EBADMSG: (libc)Error Codes.
* EBADR: (libc)Error Codes.
* EBADRPC: (libc)Error Codes.
* EBADRQC: (libc)Error Codes.
* EBADSLT: (libc)Error Codes.
* EBFONT: (libc)Error Codes.
* EBUSY: (libc)Error Codes.
* ECANCELED: (libc)Error Codes.
* ECHILD: (libc)Error Codes.
* ECHO: (libc)Local Modes.
* ECHOCTL: (libc)Local Modes.
* ECHOE: (libc)Local Modes.
* ECHOK: (libc)Local Modes.
* ECHOKE: (libc)Local Modes.
* ECHONL: (libc)Local Modes.
* ECHOPRT: (libc)Local Modes.
* ECHRNG: (libc)Error Codes.
* ECOMM: (libc)Error Codes.
* ECONNABORTED: (libc)Error Codes.
* ECONNREFUSED: (libc)Error Codes.
* ECONNRESET: (libc)Error Codes.
* ED: (libc)Error Codes.
* EDEADLK: (libc)Error Codes.
* EDEADLOCK: (libc)Error Codes.
* EDESTADDRREQ: (libc)Error Codes.
* EDIED: (libc)Error Codes.
* EDOM: (libc)Error Codes.
* EDOTDOT: (libc)Error Codes.
* EDQUOT: (libc)Error Codes.
* EEXIST: (libc)Error Codes.
* EFAULT: (libc)Error Codes.
* EFBIG: (libc)Error Codes.
* EFTYPE: (libc)Error Codes.
* EGRATUITOUS: (libc)Error Codes.
* EGREGIOUS: (libc)Error Codes.
* EHOSTDOWN: (libc)Error Codes.
* EHOSTUNREACH: (libc)Error Codes.
* EHWPOISON: (libc)Error Codes.
* EIDRM: (libc)Error Codes.
* EIEIO: (libc)Error Codes.
* EILSEQ: (libc)Error Codes.
* EINPROGRESS: (libc)Error Codes.
* EINTR: (libc)Error Codes.
* EINVAL: (libc)Error Codes.
* EIO: (libc)Error Codes.
* EISCONN: (libc)Error Codes.
* EISDIR: (libc)Error Codes.
* EISNAM: (libc)Error Codes.
* EKEYEXPIRED: (libc)Error Codes.
* EKEYREJECTED: (libc)Error Codes.
* EKEYREVOKED: (libc)Error Codes.
* EL2HLT: (libc)Error Codes.
* EL2NSYNC: (libc)Error Codes.
* EL3HLT: (libc)Error Codes.
* EL3RST: (libc)Error Codes.
* ELIBACC: (libc)Error Codes.
* ELIBBAD: (libc)Error Codes.
* ELIBEXEC: (libc)Error Codes.
* ELIBMAX: (libc)Error Codes.
* ELIBSCN: (libc)Error Codes.
* ELNRNG: (libc)Error Codes.
* ELOOP: (libc)Error Codes.
* EMEDIUMTYPE: (libc)Error Codes.
* EMFILE: (libc)Error Codes.
* EMLINK: (libc)Error Codes.
* EMSGSIZE: (libc)Error Codes.
* EMULTIHOP: (libc)Error Codes.
* ENAMETOOLONG: (libc)Error Codes.
* ENAVAIL: (libc)Error Codes.
* ENEEDAUTH: (libc)Error Codes.
* ENETDOWN: (libc)Error Codes.
* ENETRESET: (libc)Error Codes.
* ENETUNREACH: (libc)Error Codes.
* ENFILE: (libc)Error Codes.
* ENOANO: (libc)Error Codes.
* ENOBUFS: (libc)Error Codes.
* ENOCSI: (libc)Error Codes.
* ENODATA: (libc)Error Codes.
* ENODEV: (libc)Error Codes.
* ENOENT: (libc)Error Codes.
* ENOEXEC: (libc)Error Codes.
* ENOKEY: (libc)Error Codes.
* ENOLCK: (libc)Error Codes.
* ENOLINK: (libc)Error Codes.
* ENOMEDIUM: (libc)Error Codes.
* ENOMEM: (libc)Error Codes.
* ENOMSG: (libc)Error Codes.
* ENONET: (libc)Error Codes.
* ENOPKG: (libc)Error Codes.
* ENOPROTOOPT: (libc)Error Codes.
* ENOSPC: (libc)Error Codes.
* ENOSR: (libc)Error Codes.
* ENOSTR: (libc)Error Codes.
* ENOSYS: (libc)Error Codes.
* ENOTBLK: (libc)Error Codes.
* ENOTCONN: (libc)Error Codes.
* ENOTDIR: (libc)Error Codes.
* ENOTEMPTY: (libc)Error Codes.
* ENOTNAM: (libc)Error Codes.
* ENOTRECOVERABLE: (libc)Error Codes.
* ENOTSOCK: (libc)Error Codes.
* ENOTSUP: (libc)Error Codes.
* ENOTTY: (libc)Error Codes.
* ENOTUNIQ: (libc)Error Codes.
* ENXIO: (libc)Error Codes.
* EOF: (libc)EOF and Errors.
* EOPNOTSUPP: (libc)Error Codes.
* EOVERFLOW: (libc)Error Codes.
* EOWNERDEAD: (libc)Error Codes.
* EPERM: (libc)Error Codes.
* EPFNOSUPPORT: (libc)Error Codes.
* EPIPE: (libc)Error Codes.
* EPROCLIM: (libc)Error Codes.
* EPROCUNAVAIL: (libc)Error Codes.
* EPROGMISMATCH: (libc)Error Codes.
* EPROGUNAVAIL: (libc)Error Codes.
* EPROTO: (libc)Error Codes.
* EPROTONOSUPPORT: (libc)Error Codes.
* EPROTOTYPE: (libc)Error Codes.
* EQUIV_CLASS_MAX: (libc)Utility Limits.
* ERANGE: (libc)Error Codes.
* EREMCHG: (libc)Error Codes.
* EREMOTE: (libc)Error Codes.
* EREMOTEIO: (libc)Error Codes.
* ERESTART: (libc)Error Codes.
* ERFKILL: (libc)Error Codes.
* EROFS: (libc)Error Codes.
* ERPCMISMATCH: (libc)Error Codes.
* ESHUTDOWN: (libc)Error Codes.
* ESOCKTNOSUPPORT: (libc)Error Codes.
* ESPIPE: (libc)Error Codes.
* ESRCH: (libc)Error Codes.
* ESRMNT: (libc)Error Codes.
* ESTALE: (libc)Error Codes.
* ESTRPIPE: (libc)Error Codes.
* ETIME: (libc)Error Codes.
* ETIMEDOUT: (libc)Error Codes.
* ETOOMANYREFS: (libc)Error Codes.
* ETXTBSY: (libc)Error Codes.
* EUCLEAN: (libc)Error Codes.
* EUNATCH: (libc)Error Codes.
* EUSERS: (libc)Error Codes.
* EWOULDBLOCK: (libc)Error Codes.
* EXDEV: (libc)Error Codes.
* EXFULL: (libc)Error Codes.
* EXIT_FAILURE: (libc)Exit Status.
* EXIT_SUCCESS: (libc)Exit Status.
* EXPR_NEST_MAX: (libc)Utility Limits.
* FD_CLOEXEC: (libc)Descriptor Flags.
* FD_CLR: (libc)Waiting for I/O.
* FD_ISSET: (libc)Waiting for I/O.
* FD_SET: (libc)Waiting for I/O.
* FD_SETSIZE: (libc)Waiting for I/O.
* FD_ZERO: (libc)Waiting for I/O.
* FE_SNANS_ALWAYS_SIGNAL: (libc)Infinity and NaN.
* FILENAME_MAX: (libc)Limits for Files.
* FLUSHO: (libc)Local Modes.
* FOPEN_MAX: (libc)Opening Streams.
* FP_ILOGB0: (libc)Exponents and Logarithms.
* FP_ILOGBNAN: (libc)Exponents and Logarithms.
* FP_LLOGB0: (libc)Exponents and Logarithms.
* FP_LLOGBNAN: (libc)Exponents and Logarithms.
* F_DUPFD: (libc)Duplicating Descriptors.
* F_GETFD: (libc)Descriptor Flags.
* F_GETFL: (libc)Getting File Status Flags.
* F_GETLK: (libc)File Locks.
* F_GETOWN: (libc)Interrupt Input.
* F_OFD_GETLK: (libc)Open File Description Locks.
* F_OFD_SETLK: (libc)Open File Description Locks.
* F_OFD_SETLKW: (libc)Open File Description Locks.
* F_OK: (libc)Testing File Access.
* F_SETFD: (libc)Descriptor Flags.
* F_SETFL: (libc)Getting File Status Flags.
* F_SETLK: (libc)File Locks.
* F_SETLKW: (libc)File Locks.
* F_SETOWN: (libc)Interrupt Input.
* HAS_CPU_FEATURE: (libc)X86.
* HUGE_VAL: (libc)Math Error Reporting.
* HUGE_VALF: (libc)Math Error Reporting.
* HUGE_VALL: (libc)Math Error Reporting.
* HUGE_VAL_FN: (libc)Math Error Reporting.
* HUGE_VAL_FNx: (libc)Math Error Reporting.
* HUPCL: (libc)Control Modes.
* I: (libc)Complex Numbers.
* ICANON: (libc)Local Modes.
* ICRNL: (libc)Input Modes.
* IEXTEN: (libc)Local Modes.
* IFNAMSIZ: (libc)Interface Naming.
* IFTODT: (libc)Directory Entries.
* IGNBRK: (libc)Input Modes.
* IGNCR: (libc)Input Modes.
* IGNPAR: (libc)Input Modes.
* IMAXBEL: (libc)Input Modes.
* INADDR_ANY: (libc)Host Address Data Type.
* INADDR_BROADCAST: (libc)Host Address Data Type.
* INADDR_LOOPBACK: (libc)Host Address Data Type.
* INADDR_NONE: (libc)Host Address Data Type.
* INFINITY: (libc)Infinity and NaN.
* INLCR: (libc)Input Modes.
* INPCK: (libc)Input Modes.
* IPPORT_RESERVED: (libc)Ports.
* IPPORT_USERRESERVED: (libc)Ports.
* ISIG: (libc)Local Modes.
* ISTRIP: (libc)Input Modes.
* IXANY: (libc)Input Modes.
* IXOFF: (libc)Input Modes.
* IXON: (libc)Input Modes.
* LINE_MAX: (libc)Utility Limits.
* LINK_MAX: (libc)Limits for Files.
* L_ctermid: (libc)Identifying the Terminal.
* L_cuserid: (libc)Who Logged In.
* L_tmpnam: (libc)Temporary Files.
* MAXNAMLEN: (libc)Limits for Files.
* MAXSYMLINKS: (libc)Symbolic Links.
* MAX_CANON: (libc)Limits for Files.
* MAX_INPUT: (libc)Limits for Files.
* MB_CUR_MAX: (libc)Selecting the Conversion.
* MB_LEN_MAX: (libc)Selecting the Conversion.
* MDMBUF: (libc)Control Modes.
* MSG_DONTROUTE: (libc)Socket Data Options.
* MSG_OOB: (libc)Socket Data Options.
* MSG_PEEK: (libc)Socket Data Options.
* NAME_MAX: (libc)Limits for Files.
* NAN: (libc)Infinity and NaN.
* NCCS: (libc)Mode Data Types.
* NGROUPS_MAX: (libc)General Limits.
* NOFLSH: (libc)Local Modes.
* NOKERNINFO: (libc)Local Modes.
* NSIG: (libc)Standard Signals.
* NULL: (libc)Null Pointer Constant.
* ONLCR: (libc)Output Modes.
* ONOEOT: (libc)Output Modes.
* OPEN_MAX: (libc)General Limits.
* OPOST: (libc)Output Modes.
* OXTABS: (libc)Output Modes.
* O_ACCMODE: (libc)Access Modes.
* O_APPEND: (libc)Operating Modes.
* O_ASYNC: (libc)Operating Modes.
* O_CREAT: (libc)Open-time Flags.
* O_DIRECTORY: (libc)Open-time Flags.
* O_EXCL: (libc)Open-time Flags.
* O_EXEC: (libc)Access Modes.
* O_EXLOCK: (libc)Open-time Flags.
* O_FSYNC: (libc)Operating Modes.
* O_IGNORE_CTTY: (libc)Open-time Flags.
* O_NDELAY: (libc)Operating Modes.
* O_NOATIME: (libc)Operating Modes.
* O_NOCTTY: (libc)Open-time Flags.
* O_NOFOLLOW: (libc)Open-time Flags.
* O_NOLINK: (libc)Open-time Flags.
* O_NONBLOCK: (libc)Open-time Flags.
* O_NONBLOCK: (libc)Operating Modes.
* O_NOTRANS: (libc)Open-time Flags.
* O_PATH: (libc)Access Modes.
* O_RDONLY: (libc)Access Modes.
* O_RDWR: (libc)Access Modes.
* O_READ: (libc)Access Modes.
* O_SHLOCK: (libc)Open-time Flags.
* O_SYNC: (libc)Operating Modes.
* O_TMPFILE: (libc)Open-time Flags.
* O_TRUNC: (libc)Open-time Flags.
* O_WRITE: (libc)Access Modes.
* O_WRONLY: (libc)Access Modes.
* PARENB: (libc)Control Modes.
* PARMRK: (libc)Input Modes.
* PARODD: (libc)Control Modes.
* PATH_MAX: (libc)Limits for Files.
* PA_FLAG_MASK: (libc)Parsing a Template String.
* PENDIN: (libc)Local Modes.
* PF_FILE: (libc)Local Namespace Details.
* PF_INET6: (libc)Internet Namespace.
* PF_INET: (libc)Internet Namespace.
* PF_LOCAL: (libc)Local Namespace Details.
* PF_UNIX: (libc)Local Namespace Details.
* PIPE_BUF: (libc)Limits for Files.
* PTHREAD_ATTR_NO_SIGMASK_NP: (libc)Initial Thread Signal Mask.
* P_tmpdir: (libc)Temporary Files.
* RAND_MAX: (libc)ISO Random.
* RE_DUP_MAX: (libc)General Limits.
* RLIM_INFINITY: (libc)Limits on Resources.
* R_OK: (libc)Testing File Access.
* SA_NOCLDSTOP: (libc)Flags for Sigaction.
* SA_ONSTACK: (libc)Flags for Sigaction.
* SA_RESTART: (libc)Flags for Sigaction.
* SEEK_CUR: (libc)File Positioning.
* SEEK_END: (libc)File Positioning.
* SEEK_SET: (libc)File Positioning.
* SIGABRT: (libc)Program Error Signals.
* SIGALRM: (libc)Alarm Signals.
* SIGBUS: (libc)Program Error Signals.
* SIGCHLD: (libc)Job Control Signals.
* SIGCLD: (libc)Job Control Signals.
* SIGCONT: (libc)Job Control Signals.
* SIGEMT: (libc)Program Error Signals.
* SIGFPE: (libc)Program Error Signals.
* SIGHUP: (libc)Termination Signals.
* SIGILL: (libc)Program Error Signals.
* SIGINFO: (libc)Miscellaneous Signals.
* SIGINT: (libc)Termination Signals.
* SIGIO: (libc)Asynchronous I/O Signals.
* SIGIOT: (libc)Program Error Signals.
* SIGKILL: (libc)Termination Signals.
* SIGLOST: (libc)Operation Error Signals.
* SIGPIPE: (libc)Operation Error Signals.
* SIGPOLL: (libc)Asynchronous I/O Signals.
* SIGPROF: (libc)Alarm Signals.
* SIGQUIT: (libc)Termination Signals.
* SIGSEGV: (libc)Program Error Signals.
* SIGSTOP: (libc)Job Control Signals.
* SIGSYS: (libc)Program Error Signals.
* SIGTERM: (libc)Termination Signals.
* SIGTRAP: (libc)Program Error Signals.
* SIGTSTP: (libc)Job Control Signals.
* SIGTTIN: (libc)Job Control Signals.
* SIGTTOU: (libc)Job Control Signals.
* SIGURG: (libc)Asynchronous I/O Signals.
* SIGUSR1: (libc)Miscellaneous Signals.
* SIGUSR2: (libc)Miscellaneous Signals.
* SIGVTALRM: (libc)Alarm Signals.
* SIGWINCH: (libc)Miscellaneous Signals.
* SIGXCPU: (libc)Operation Error Signals.
* SIGXFSZ: (libc)Operation Error Signals.
* SIG_ERR: (libc)Basic Signal Handling.
* SNAN: (libc)Infinity and NaN.
* SNANF: (libc)Infinity and NaN.
* SNANFN: (libc)Infinity and NaN.
* SNANFNx: (libc)Infinity and NaN.
* SNANL: (libc)Infinity and NaN.
* SOCK_DGRAM: (libc)Communication Styles.
* SOCK_RAW: (libc)Communication Styles.
* SOCK_RDM: (libc)Communication Styles.
* SOCK_SEQPACKET: (libc)Communication Styles.
* SOCK_STREAM: (libc)Communication Styles.
* SOL_SOCKET: (libc)Socket-Level Options.
* SSIZE_MAX: (libc)General Limits.
* STREAM_MAX: (libc)General Limits.
* SUN_LEN: (libc)Local Namespace Details.
* S_IFMT: (libc)Testing File Type.
* S_ISBLK: (libc)Testing File Type.
* S_ISCHR: (libc)Testing File Type.
* S_ISDIR: (libc)Testing File Type.
* S_ISFIFO: (libc)Testing File Type.
* S_ISLNK: (libc)Testing File Type.
* S_ISREG: (libc)Testing File Type.
* S_ISSOCK: (libc)Testing File Type.
* S_TYPEISMQ: (libc)Testing File Type.
* S_TYPEISSEM: (libc)Testing File Type.
* S_TYPEISSHM: (libc)Testing File Type.
* TMP_MAX: (libc)Temporary Files.
* TOSTOP: (libc)Local Modes.
* TZNAME_MAX: (libc)General Limits.
* VDISCARD: (libc)Other Special.
* VDSUSP: (libc)Signal Characters.
* VEOF: (libc)Editing Characters.
* VEOL2: (libc)Editing Characters.
* VEOL: (libc)Editing Characters.
* VERASE: (libc)Editing Characters.
* VINTR: (libc)Signal Characters.
* VKILL: (libc)Editing Characters.
* VLNEXT: (libc)Other Special.
* VMIN: (libc)Noncanonical Input.
* VQUIT: (libc)Signal Characters.
* VREPRINT: (libc)Editing Characters.
* VSTART: (libc)Start/Stop Characters.
* VSTATUS: (libc)Other Special.
* VSTOP: (libc)Start/Stop Characters.
* VSUSP: (libc)Signal Characters.
* VTIME: (libc)Noncanonical Input.
* VWERASE: (libc)Editing Characters.
* WCHAR_MAX: (libc)Extended Char Intro.
* WCHAR_MIN: (libc)Extended Char Intro.
* WCOREDUMP: (libc)Process Completion Status.
* WEOF: (libc)EOF and Errors.
* WEOF: (libc)Extended Char Intro.
* WEXITSTATUS: (libc)Process Completion Status.
* WIFEXITED: (libc)Process Completion Status.
* WIFSIGNALED: (libc)Process Completion Status.
* WIFSTOPPED: (libc)Process Completion Status.
* WSTOPSIG: (libc)Process Completion Status.
* WTERMSIG: (libc)Process Completion Status.
* W_OK: (libc)Testing File Access.
* X_OK: (libc)Testing File Access.
* _Complex_I: (libc)Complex Numbers.
* _Exit: (libc)Termination Internals.
* _IOFBF: (libc)Controlling Buffering.
* _IOLBF: (libc)Controlling Buffering.
* _IONBF: (libc)Controlling Buffering.
* _Imaginary_I: (libc)Complex Numbers.
* _PATH_UTMP: (libc)Manipulating the Database.
* _PATH_WTMP: (libc)Manipulating the Database.
* _POSIX2_C_DEV: (libc)System Options.
* _POSIX2_C_VERSION: (libc)Version Supported.
* _POSIX2_FORT_DEV: (libc)System Options.
* _POSIX2_FORT_RUN: (libc)System Options.
* _POSIX2_LOCALEDEF: (libc)System Options.
* _POSIX2_SW_DEV: (libc)System Options.
* _POSIX_CHOWN_RESTRICTED: (libc)Options for Files.
* _POSIX_JOB_CONTROL: (libc)System Options.
* _POSIX_NO_TRUNC: (libc)Options for Files.
* _POSIX_SAVED_IDS: (libc)System Options.
* _POSIX_VDISABLE: (libc)Options for Files.
* _POSIX_VERSION: (libc)Version Supported.
* __fbufsize: (libc)Controlling Buffering.
* __flbf: (libc)Controlling Buffering.
* __fpending: (libc)Controlling Buffering.
* __fpurge: (libc)Flushing Buffers.
* __freadable: (libc)Opening Streams.
* __freading: (libc)Opening Streams.
* __fsetlocking: (libc)Streams and Threads.
* __fwritable: (libc)Opening Streams.
* __fwriting: (libc)Opening Streams.
* __gconv_end_fct: (libc)glibc iconv Implementation.
* __gconv_fct: (libc)glibc iconv Implementation.
* __gconv_init_fct: (libc)glibc iconv Implementation.
* __ppc_get_timebase: (libc)PowerPC.
* __ppc_get_timebase_freq: (libc)PowerPC.
* __ppc_mdoio: (libc)PowerPC.
* __ppc_mdoom: (libc)PowerPC.
* __ppc_set_ppr_low: (libc)PowerPC.
* __ppc_set_ppr_med: (libc)PowerPC.
* __ppc_set_ppr_med_high: (libc)PowerPC.
* __ppc_set_ppr_med_low: (libc)PowerPC.
* __ppc_set_ppr_very_low: (libc)PowerPC.
* __ppc_yield: (libc)PowerPC.
* __riscv_flush_icache: (libc)RISC-V.
* __va_copy: (libc)Argument Macros.
* __x86_get_cpuid_feature_leaf: (libc)X86.
* _exit: (libc)Termination Internals.
* _flushlbf: (libc)Flushing Buffers.
* _tolower: (libc)Case Conversion.
* _toupper: (libc)Case Conversion.
* a64l: (libc)Encode Binary Data.
* abort: (libc)Aborting a Program.
* abs: (libc)Absolute Value.
* accept: (libc)Accepting Connections.
* access: (libc)Testing File Access.
* acos: (libc)Inverse Trig Functions.
* acosf: (libc)Inverse Trig Functions.
* acosfN: (libc)Inverse Trig Functions.
* acosfNx: (libc)Inverse Trig Functions.
* acosh: (libc)Hyperbolic Functions.
* acoshf: (libc)Hyperbolic Functions.
* acoshfN: (libc)Hyperbolic Functions.
* acoshfNx: (libc)Hyperbolic Functions.
* acoshl: (libc)Hyperbolic Functions.
* acosl: (libc)Inverse Trig Functions.
* addmntent: (libc)mtab.
* addseverity: (libc)Adding Severity Classes.
* adjtime: (libc)Setting and Adjusting the Time.
* adjtimex: (libc)Setting and Adjusting the Time.
* aio_cancel64: (libc)Cancel AIO Operations.
* aio_cancel: (libc)Cancel AIO Operations.
* aio_error64: (libc)Status of AIO Operations.
* aio_error: (libc)Status of AIO Operations.
* aio_fsync64: (libc)Synchronizing AIO Operations.
* aio_fsync: (libc)Synchronizing AIO Operations.
* aio_init: (libc)Configuration of AIO.
* aio_read64: (libc)Asynchronous Reads/Writes.
* aio_read: (libc)Asynchronous Reads/Writes.
* aio_return64: (libc)Status of AIO Operations.
* aio_return: (libc)Status of AIO Operations.
* aio_suspend64: (libc)Synchronizing AIO Operations.
* aio_suspend: (libc)Synchronizing AIO Operations.
* aio_write64: (libc)Asynchronous Reads/Writes.
* aio_write: (libc)Asynchronous Reads/Writes.
* alarm: (libc)Setting an Alarm.
* aligned_alloc: (libc)Aligned Memory Blocks.
* alloca: (libc)Variable Size Automatic.
* alphasort64: (libc)Scanning Directory Content.
* alphasort: (libc)Scanning Directory Content.
* argp_error: (libc)Argp Helper Functions.
* argp_failure: (libc)Argp Helper Functions.
* argp_help: (libc)Argp Help.
* argp_parse: (libc)Argp.
* argp_state_help: (libc)Argp Helper Functions.
* argp_usage: (libc)Argp Helper Functions.
* argz_add: (libc)Argz Functions.
* argz_add_sep: (libc)Argz Functions.
* argz_append: (libc)Argz Functions.
* argz_count: (libc)Argz Functions.
* argz_create: (libc)Argz Functions.
* argz_create_sep: (libc)Argz Functions.
* argz_delete: (libc)Argz Functions.
* argz_extract: (libc)Argz Functions.
* argz_insert: (libc)Argz Functions.
* argz_next: (libc)Argz Functions.
* argz_replace: (libc)Argz Functions.
* argz_stringify: (libc)Argz Functions.
* asctime: (libc)Formatting Calendar Time.
* asctime_r: (libc)Formatting Calendar Time.
* asin: (libc)Inverse Trig Functions.
* asinf: (libc)Inverse Trig Functions.
* asinfN: (libc)Inverse Trig Functions.
* asinfNx: (libc)Inverse Trig Functions.
* asinh: (libc)Hyperbolic Functions.
* asinhf: (libc)Hyperbolic Functions.
* asinhfN: (libc)Hyperbolic Functions.
* asinhfNx: (libc)Hyperbolic Functions.
* asinhl: (libc)Hyperbolic Functions.
* asinl: (libc)Inverse Trig Functions.
* asprintf: (libc)Dynamic Output.
* assert: (libc)Consistency Checking.
* assert_perror: (libc)Consistency Checking.
* atan2: (libc)Inverse Trig Functions.
* atan2f: (libc)Inverse Trig Functions.
* atan2fN: (libc)Inverse Trig Functions.
* atan2fNx: (libc)Inverse Trig Functions.
* atan2l: (libc)Inverse Trig Functions.
* atan: (libc)Inverse Trig Functions.
* atanf: (libc)Inverse Trig Functions.
* atanfN: (libc)Inverse Trig Functions.
* atanfNx: (libc)Inverse Trig Functions.
* atanh: (libc)Hyperbolic Functions.
* atanhf: (libc)Hyperbolic Functions.
* atanhfN: (libc)Hyperbolic Functions.
* atanhfNx: (libc)Hyperbolic Functions.
* atanhl: (libc)Hyperbolic Functions.
* atanl: (libc)Inverse Trig Functions.
* atexit: (libc)Cleanups on Exit.
* atof: (libc)Parsing of Floats.
* atoi: (libc)Parsing of Integers.
* atol: (libc)Parsing of Integers.
* atoll: (libc)Parsing of Integers.
* backtrace: (libc)Backtraces.
* backtrace_symbols: (libc)Backtraces.
* backtrace_symbols_fd: (libc)Backtraces.
* basename: (libc)Finding Tokens in a String.
* basename: (libc)Finding Tokens in a String.
* bcmp: (libc)String/Array Comparison.
* bcopy: (libc)Copying Strings and Arrays.
* bind: (libc)Setting Address.
* bind_textdomain_codeset: (libc)Charset conversion in gettext.
* bindtextdomain: (libc)Locating gettext catalog.
* brk: (libc)Resizing the Data Segment.
* bsearch: (libc)Array Search Function.
* btowc: (libc)Converting a Character.
* bzero: (libc)Copying Strings and Arrays.
* cabs: (libc)Absolute Value.
* cabsf: (libc)Absolute Value.
* cabsfN: (libc)Absolute Value.
* cabsfNx: (libc)Absolute Value.
* cabsl: (libc)Absolute Value.
* cacos: (libc)Inverse Trig Functions.
* cacosf: (libc)Inverse Trig Functions.
* cacosfN: (libc)Inverse Trig Functions.
* cacosfNx: (libc)Inverse Trig Functions.
* cacosh: (libc)Hyperbolic Functions.
* cacoshf: (libc)Hyperbolic Functions.
* cacoshfN: (libc)Hyperbolic Functions.
* cacoshfNx: (libc)Hyperbolic Functions.
* cacoshl: (libc)Hyperbolic Functions.
* cacosl: (libc)Inverse Trig Functions.
* call_once: (libc)Call Once.
* calloc: (libc)Allocating Cleared Space.
* canonicalize: (libc)FP Bit Twiddling.
* canonicalize_file_name: (libc)Symbolic Links.
* canonicalizef: (libc)FP Bit Twiddling.
* canonicalizefN: (libc)FP Bit Twiddling.
* canonicalizefNx: (libc)FP Bit Twiddling.
* canonicalizel: (libc)FP Bit Twiddling.
* carg: (libc)Operations on Complex.
* cargf: (libc)Operations on Complex.
* cargfN: (libc)Operations on Complex.
* cargfNx: (libc)Operations on Complex.
* cargl: (libc)Operations on Complex.
* casin: (libc)Inverse Trig Functions.
* casinf: (libc)Inverse Trig Functions.
* casinfN: (libc)Inverse Trig Functions.
* casinfNx: (libc)Inverse Trig Functions.
* casinh: (libc)Hyperbolic Functions.
* casinhf: (libc)Hyperbolic Functions.
* casinhfN: (libc)Hyperbolic Functions.
* casinhfNx: (libc)Hyperbolic Functions.
* casinhl: (libc)Hyperbolic Functions.
* casinl: (libc)Inverse Trig Functions.
* catan: (libc)Inverse Trig Functions.
* catanf: (libc)Inverse Trig Functions.
* catanfN: (libc)Inverse Trig Functions.
* catanfNx: (libc)Inverse Trig Functions.
* catanh: (libc)Hyperbolic Functions.
* catanhf: (libc)Hyperbolic Functions.
* catanhfN: (libc)Hyperbolic Functions.
* catanhfNx: (libc)Hyperbolic Functions.
* catanhl: (libc)Hyperbolic Functions.
* catanl: (libc)Inverse Trig Functions.
* catclose: (libc)The catgets Functions.
* catgets: (libc)The catgets Functions.
* catopen: (libc)The catgets Functions.
* cbrt: (libc)Exponents and Logarithms.
* cbrtf: (libc)Exponents and Logarithms.
* cbrtfN: (libc)Exponents and Logarithms.
* cbrtfNx: (libc)Exponents and Logarithms.
* cbrtl: (libc)Exponents and Logarithms.
* ccos: (libc)Trig Functions.
* ccosf: (libc)Trig Functions.
* ccosfN: (libc)Trig Functions.
* ccosfNx: (libc)Trig Functions.
* ccosh: (libc)Hyperbolic Functions.
* ccoshf: (libc)Hyperbolic Functions.
* ccoshfN: (libc)Hyperbolic Functions.
* ccoshfNx: (libc)Hyperbolic Functions.
* ccoshl: (libc)Hyperbolic Functions.
* ccosl: (libc)Trig Functions.
* ceil: (libc)Rounding Functions.
* ceilf: (libc)Rounding Functions.
* ceilfN: (libc)Rounding Functions.
* ceilfNx: (libc)Rounding Functions.
* ceill: (libc)Rounding Functions.
* cexp: (libc)Exponents and Logarithms.
* cexpf: (libc)Exponents and Logarithms.
* cexpfN: (libc)Exponents and Logarithms.
* cexpfNx: (libc)Exponents and Logarithms.
* cexpl: (libc)Exponents and Logarithms.
* cfgetispeed: (libc)Line Speed.
* cfgetospeed: (libc)Line Speed.
* cfmakeraw: (libc)Noncanonical Input.
* cfsetispeed: (libc)Line Speed.
* cfsetospeed: (libc)Line Speed.
* cfsetspeed: (libc)Line Speed.
* chdir: (libc)Working Directory.
* chmod: (libc)Setting Permissions.
* chown: (libc)File Owner.
* cimag: (libc)Operations on Complex.
* cimagf: (libc)Operations on Complex.
* cimagfN: (libc)Operations on Complex.
* cimagfNx: (libc)Operations on Complex.
* cimagl: (libc)Operations on Complex.
* clearenv: (libc)Environment Access.
* clearerr: (libc)Error Recovery.
* clearerr_unlocked: (libc)Error Recovery.
* clock: (libc)CPU Time.
* clock_getres: (libc)Getting the Time.
* clock_gettime: (libc)Getting the Time.
* clock_settime: (libc)Setting and Adjusting the Time.
* clog10: (libc)Exponents and Logarithms.
* clog10f: (libc)Exponents and Logarithms.
* clog10fN: (libc)Exponents and Logarithms.
* clog10fNx: (libc)Exponents and Logarithms.
* clog10l: (libc)Exponents and Logarithms.
* clog: (libc)Exponents and Logarithms.
* clogf: (libc)Exponents and Logarithms.
* clogfN: (libc)Exponents and Logarithms.
* clogfNx: (libc)Exponents and Logarithms.
* clogl: (libc)Exponents and Logarithms.
* close: (libc)Opening and Closing Files.
* closedir: (libc)Reading/Closing Directory.
* closelog: (libc)closelog.
* cnd_broadcast: (libc)ISO C Condition Variables.
* cnd_destroy: (libc)ISO C Condition Variables.
* cnd_init: (libc)ISO C Condition Variables.
* cnd_signal: (libc)ISO C Condition Variables.
* cnd_timedwait: (libc)ISO C Condition Variables.
* cnd_wait: (libc)ISO C Condition Variables.
* confstr: (libc)String Parameters.
* conj: (libc)Operations on Complex.
* conjf: (libc)Operations on Complex.
* conjfN: (libc)Operations on Complex.
* conjfNx: (libc)Operations on Complex.
* conjl: (libc)Operations on Complex.
* connect: (libc)Connecting.
* copy_file_range: (libc)Copying File Data.
* copysign: (libc)FP Bit Twiddling.
* copysignf: (libc)FP Bit Twiddling.
* copysignfN: (libc)FP Bit Twiddling.
* copysignfNx: (libc)FP Bit Twiddling.
* copysignl: (libc)FP Bit Twiddling.
* cos: (libc)Trig Functions.
* cosf: (libc)Trig Functions.
* cosfN: (libc)Trig Functions.
* cosfNx: (libc)Trig Functions.
* cosh: (libc)Hyperbolic Functions.
* coshf: (libc)Hyperbolic Functions.
* coshfN: (libc)Hyperbolic Functions.
* coshfNx: (libc)Hyperbolic Functions.
* coshl: (libc)Hyperbolic Functions.
* cosl: (libc)Trig Functions.
* cpow: (libc)Exponents and Logarithms.
* cpowf: (libc)Exponents and Logarithms.
* cpowfN: (libc)Exponents and Logarithms.
* cpowfNx: (libc)Exponents and Logarithms.
* cpowl: (libc)Exponents and Logarithms.
* cproj: (libc)Operations on Complex.
* cprojf: (libc)Operations on Complex.
* cprojfN: (libc)Operations on Complex.
* cprojfNx: (libc)Operations on Complex.
* cprojl: (libc)Operations on Complex.
* creal: (libc)Operations on Complex.
* crealf: (libc)Operations on Complex.
* crealfN: (libc)Operations on Complex.
* crealfNx: (libc)Operations on Complex.
* creall: (libc)Operations on Complex.
* creat64: (libc)Opening and Closing Files.
* creat: (libc)Opening and Closing Files.
* crypt: (libc)Passphrase Storage.
* crypt_r: (libc)Passphrase Storage.
* csin: (libc)Trig Functions.
* csinf: (libc)Trig Functions.
* csinfN: (libc)Trig Functions.
* csinfNx: (libc)Trig Functions.
* csinh: (libc)Hyperbolic Functions.
* csinhf: (libc)Hyperbolic Functions.
* csinhfN: (libc)Hyperbolic Functions.
* csinhfNx: (libc)Hyperbolic Functions.
* csinhl: (libc)Hyperbolic Functions.
* csinl: (libc)Trig Functions.
* csqrt: (libc)Exponents and Logarithms.
* csqrtf: (libc)Exponents and Logarithms.
* csqrtfN: (libc)Exponents and Logarithms.
* csqrtfNx: (libc)Exponents and Logarithms.
* csqrtl: (libc)Exponents and Logarithms.
* ctan: (libc)Trig Functions.
* ctanf: (libc)Trig Functions.
* ctanfN: (libc)Trig Functions.
* ctanfNx: (libc)Trig Functions.
* ctanh: (libc)Hyperbolic Functions.
* ctanhf: (libc)Hyperbolic Functions.
* ctanhfN: (libc)Hyperbolic Functions.
* ctanhfNx: (libc)Hyperbolic Functions.
* ctanhl: (libc)Hyperbolic Functions.
* ctanl: (libc)Trig Functions.
* ctermid: (libc)Identifying the Terminal.
* ctime: (libc)Formatting Calendar Time.
* ctime_r: (libc)Formatting Calendar Time.
* cuserid: (libc)Who Logged In.
* daddl: (libc)Misc FP Arithmetic.
* dcgettext: (libc)Translation with gettext.
* dcngettext: (libc)Advanced gettext functions.
* ddivl: (libc)Misc FP Arithmetic.
* dgettext: (libc)Translation with gettext.
* difftime: (libc)Calculating Elapsed Time.
* dirfd: (libc)Opening a Directory.
* dirname: (libc)Finding Tokens in a String.
* div: (libc)Integer Division.
* dmull: (libc)Misc FP Arithmetic.
* dngettext: (libc)Advanced gettext functions.
* drand48: (libc)SVID Random.
* drand48_r: (libc)SVID Random.
* drem: (libc)Remainder Functions.
* dremf: (libc)Remainder Functions.
* dreml: (libc)Remainder Functions.
* dsubl: (libc)Misc FP Arithmetic.
* dup2: (libc)Duplicating Descriptors.
* dup: (libc)Duplicating Descriptors.
* ecvt: (libc)System V Number Conversion.
* ecvt_r: (libc)System V Number Conversion.
* endfsent: (libc)fstab.
* endgrent: (libc)Scanning All Groups.
* endhostent: (libc)Host Names.
* endmntent: (libc)mtab.
* endnetent: (libc)Networks Database.
* endnetgrent: (libc)Lookup Netgroup.
* endprotoent: (libc)Protocols Database.
* endpwent: (libc)Scanning All Users.
* endservent: (libc)Services Database.
* endutent: (libc)Manipulating the Database.
* endutxent: (libc)XPG Functions.
* envz_add: (libc)Envz Functions.
* envz_entry: (libc)Envz Functions.
* envz_get: (libc)Envz Functions.
* envz_merge: (libc)Envz Functions.
* envz_remove: (libc)Envz Functions.
* envz_strip: (libc)Envz Functions.
* erand48: (libc)SVID Random.
* erand48_r: (libc)SVID Random.
* erf: (libc)Special Functions.
* erfc: (libc)Special Functions.
* erfcf: (libc)Special Functions.
* erfcfN: (libc)Special Functions.
* erfcfNx: (libc)Special Functions.
* erfcl: (libc)Special Functions.
* erff: (libc)Special Functions.
* erffN: (libc)Special Functions.
* erffNx: (libc)Special Functions.
* erfl: (libc)Special Functions.
* err: (libc)Error Messages.
* errno: (libc)Checking for Errors.
* error: (libc)Error Messages.
* error_at_line: (libc)Error Messages.
* errx: (libc)Error Messages.
* execl: (libc)Executing a File.
* execle: (libc)Executing a File.
* execlp: (libc)Executing a File.
* execv: (libc)Executing a File.
* execve: (libc)Executing a File.
* execvp: (libc)Executing a File.
* exit: (libc)Normal Termination.
* exp10: (libc)Exponents and Logarithms.
* exp10f: (libc)Exponents and Logarithms.
* exp10fN: (libc)Exponents and Logarithms.
* exp10fNx: (libc)Exponents and Logarithms.
* exp10l: (libc)Exponents and Logarithms.
* exp2: (libc)Exponents and Logarithms.
* exp2f: (libc)Exponents and Logarithms.
* exp2fN: (libc)Exponents and Logarithms.
* exp2fNx: (libc)Exponents and Logarithms.
* exp2l: (libc)Exponents and Logarithms.
* exp: (libc)Exponents and Logarithms.
* expf: (libc)Exponents and Logarithms.
* expfN: (libc)Exponents and Logarithms.
* expfNx: (libc)Exponents and Logarithms.
* expl: (libc)Exponents and Logarithms.
* explicit_bzero: (libc)Erasing Sensitive Data.
* expm1: (libc)Exponents and Logarithms.
* expm1f: (libc)Exponents and Logarithms.
* expm1fN: (libc)Exponents and Logarithms.
* expm1fNx: (libc)Exponents and Logarithms.
* expm1l: (libc)Exponents and Logarithms.
* fMaddfN: (libc)Misc FP Arithmetic.
* fMaddfNx: (libc)Misc FP Arithmetic.
* fMdivfN: (libc)Misc FP Arithmetic.
* fMdivfNx: (libc)Misc FP Arithmetic.
* fMmulfN: (libc)Misc FP Arithmetic.
* fMmulfNx: (libc)Misc FP Arithmetic.
* fMsubfN: (libc)Misc FP Arithmetic.
* fMsubfNx: (libc)Misc FP Arithmetic.
* fMxaddfN: (libc)Misc FP Arithmetic.
* fMxaddfNx: (libc)Misc FP Arithmetic.
* fMxdivfN: (libc)Misc FP Arithmetic.
* fMxdivfNx: (libc)Misc FP Arithmetic.
* fMxmulfN: (libc)Misc FP Arithmetic.
* fMxmulfNx: (libc)Misc FP Arithmetic.
* fMxsubfN: (libc)Misc FP Arithmetic.
* fMxsubfNx: (libc)Misc FP Arithmetic.
* fabs: (libc)Absolute Value.
* fabsf: (libc)Absolute Value.
* fabsfN: (libc)Absolute Value.
* fabsfNx: (libc)Absolute Value.
* fabsl: (libc)Absolute Value.
* fadd: (libc)Misc FP Arithmetic.
* faddl: (libc)Misc FP Arithmetic.
* fchdir: (libc)Working Directory.
* fchmod: (libc)Setting Permissions.
* fchown: (libc)File Owner.
* fclose: (libc)Closing Streams.
* fcloseall: (libc)Closing Streams.
* fcntl: (libc)Control Operations.
* fcvt: (libc)System V Number Conversion.
* fcvt_r: (libc)System V Number Conversion.
* fdatasync: (libc)Synchronizing I/O.
* fdim: (libc)Misc FP Arithmetic.
* fdimf: (libc)Misc FP Arithmetic.
* fdimfN: (libc)Misc FP Arithmetic.
* fdimfNx: (libc)Misc FP Arithmetic.
* fdiml: (libc)Misc FP Arithmetic.
* fdiv: (libc)Misc FP Arithmetic.
* fdivl: (libc)Misc FP Arithmetic.
* fdopen: (libc)Descriptors and Streams.
* fdopendir: (libc)Opening a Directory.
* feclearexcept: (libc)Status bit operations.
* fedisableexcept: (libc)Control Functions.
* feenableexcept: (libc)Control Functions.
* fegetenv: (libc)Control Functions.
* fegetexcept: (libc)Control Functions.
* fegetexceptflag: (libc)Status bit operations.
* fegetmode: (libc)Control Functions.
* fegetround: (libc)Rounding.
* feholdexcept: (libc)Control Functions.
* feof: (libc)EOF and Errors.
* feof_unlocked: (libc)EOF and Errors.
* feraiseexcept: (libc)Status bit operations.
* ferror: (libc)EOF and Errors.
* ferror_unlocked: (libc)EOF and Errors.
* fesetenv: (libc)Control Functions.
* fesetexcept: (libc)Status bit operations.
* fesetexceptflag: (libc)Status bit operations.
* fesetmode: (libc)Control Functions.
* fesetround: (libc)Rounding.
* fetestexcept: (libc)Status bit operations.
* fetestexceptflag: (libc)Status bit operations.
* feupdateenv: (libc)Control Functions.
* fexecve: (libc)Executing a File.
* fflush: (libc)Flushing Buffers.
* fflush_unlocked: (libc)Flushing Buffers.
* fgetc: (libc)Character Input.
* fgetc_unlocked: (libc)Character Input.
* fgetgrent: (libc)Scanning All Groups.
* fgetgrent_r: (libc)Scanning All Groups.
* fgetpos64: (libc)Portable Positioning.
* fgetpos: (libc)Portable Positioning.
* fgetpwent: (libc)Scanning All Users.
* fgetpwent_r: (libc)Scanning All Users.
* fgets: (libc)Line Input.
* fgets_unlocked: (libc)Line Input.
* fgetwc: (libc)Character Input.
* fgetwc_unlocked: (libc)Character Input.
* fgetws: (libc)Line Input.
* fgetws_unlocked: (libc)Line Input.
* fileno: (libc)Descriptors and Streams.
* fileno_unlocked: (libc)Descriptors and Streams.
* finite: (libc)Floating Point Classes.
* finitef: (libc)Floating Point Classes.
* finitel: (libc)Floating Point Classes.
* flockfile: (libc)Streams and Threads.
* floor: (libc)Rounding Functions.
* floorf: (libc)Rounding Functions.
* floorfN: (libc)Rounding Functions.
* floorfNx: (libc)Rounding Functions.
* floorl: (libc)Rounding Functions.
* fma: (libc)Misc FP Arithmetic.
* fmaf: (libc)Misc FP Arithmetic.
* fmafN: (libc)Misc FP Arithmetic.
* fmafNx: (libc)Misc FP Arithmetic.
* fmal: (libc)Misc FP Arithmetic.
* fmax: (libc)Misc FP Arithmetic.
* fmaxf: (libc)Misc FP Arithmetic.
* fmaxfN: (libc)Misc FP Arithmetic.
* fmaxfNx: (libc)Misc FP Arithmetic.
* fmaxl: (libc)Misc FP Arithmetic.
* fmaxmag: (libc)Misc FP Arithmetic.
* fmaxmagf: (libc)Misc FP Arithmetic.
* fmaxmagfN: (libc)Misc FP Arithmetic.
* fmaxmagfNx: (libc)Misc FP Arithmetic.
* fmaxmagl: (libc)Misc FP Arithmetic.
* fmemopen: (libc)String Streams.
* fmin: (libc)Misc FP Arithmetic.
* fminf: (libc)Misc FP Arithmetic.
* fminfN: (libc)Misc FP Arithmetic.
* fminfNx: (libc)Misc FP Arithmetic.
* fminl: (libc)Misc FP Arithmetic.
* fminmag: (libc)Misc FP Arithmetic.
* fminmagf: (libc)Misc FP Arithmetic.
* fminmagfN: (libc)Misc FP Arithmetic.
* fminmagfNx: (libc)Misc FP Arithmetic.
* fminmagl: (libc)Misc FP Arithmetic.
* fmod: (libc)Remainder Functions.
* fmodf: (libc)Remainder Functions.
* fmodfN: (libc)Remainder Functions.
* fmodfNx: (libc)Remainder Functions.
* fmodl: (libc)Remainder Functions.
* fmtmsg: (libc)Printing Formatted Messages.
* fmul: (libc)Misc FP Arithmetic.
* fmull: (libc)Misc FP Arithmetic.
* fnmatch: (libc)Wildcard Matching.
* fopen64: (libc)Opening Streams.
* fopen: (libc)Opening Streams.
* fopencookie: (libc)Streams and Cookies.
* fork: (libc)Creating a Process.
* forkpty: (libc)Pseudo-Terminal Pairs.
* fpathconf: (libc)Pathconf.
* fpclassify: (libc)Floating Point Classes.
* fprintf: (libc)Formatted Output Functions.
* fputc: (libc)Simple Output.
* fputc_unlocked: (libc)Simple Output.
* fputs: (libc)Simple Output.
* fputs_unlocked: (libc)Simple Output.
* fputwc: (libc)Simple Output.
* fputwc_unlocked: (libc)Simple Output.
* fputws: (libc)Simple Output.
* fputws_unlocked: (libc)Simple Output.
* fread: (libc)Block Input/Output.
* fread_unlocked: (libc)Block Input/Output.
* free: (libc)Freeing after Malloc.
* freopen64: (libc)Opening Streams.
* freopen: (libc)Opening Streams.
* frexp: (libc)Normalization Functions.
* frexpf: (libc)Normalization Functions.
* frexpfN: (libc)Normalization Functions.
* frexpfNx: (libc)Normalization Functions.
* frexpl: (libc)Normalization Functions.
* fromfp: (libc)Rounding Functions.
* fromfpf: (libc)Rounding Functions.
* fromfpfN: (libc)Rounding Functions.
* fromfpfNx: (libc)Rounding Functions.
* fromfpl: (libc)Rounding Functions.
* fromfpx: (libc)Rounding Functions.
* fromfpxf: (libc)Rounding Functions.
* fromfpxfN: (libc)Rounding Functions.
* fromfpxfNx: (libc)Rounding Functions.
* fromfpxl: (libc)Rounding Functions.
* fscanf: (libc)Formatted Input Functions.
* fseek: (libc)File Positioning.
* fseeko64: (libc)File Positioning.
* fseeko: (libc)File Positioning.
* fsetpos64: (libc)Portable Positioning.
* fsetpos: (libc)Portable Positioning.
* fstat64: (libc)Reading Attributes.
* fstat: (libc)Reading Attributes.
* fsub: (libc)Misc FP Arithmetic.
* fsubl: (libc)Misc FP Arithmetic.
* fsync: (libc)Synchronizing I/O.
* ftell: (libc)File Positioning.
* ftello64: (libc)File Positioning.
* ftello: (libc)File Positioning.
* ftruncate64: (libc)File Size.
* ftruncate: (libc)File Size.
* ftrylockfile: (libc)Streams and Threads.
* ftw64: (libc)Working with Directory Trees.
* ftw: (libc)Working with Directory Trees.
* funlockfile: (libc)Streams and Threads.
* futimes: (libc)File Times.
* fwide: (libc)Streams and I18N.
* fwprintf: (libc)Formatted Output Functions.
* fwrite: (libc)Block Input/Output.
* fwrite_unlocked: (libc)Block Input/Output.
* fwscanf: (libc)Formatted Input Functions.
* gamma: (libc)Special Functions.
* gammaf: (libc)Special Functions.
* gammal: (libc)Special Functions.
* gcvt: (libc)System V Number Conversion.
* get_avphys_pages: (libc)Query Memory Parameters.
* get_current_dir_name: (libc)Working Directory.
* get_nprocs: (libc)Processor Resources.
* get_nprocs_conf: (libc)Processor Resources.
* get_phys_pages: (libc)Query Memory Parameters.
* getauxval: (libc)Auxiliary Vector.
* getc: (libc)Character Input.
* getc_unlocked: (libc)Character Input.
* getchar: (libc)Character Input.
* getchar_unlocked: (libc)Character Input.
* getcontext: (libc)System V contexts.
* getcpu: (libc)CPU Affinity.
* getcwd: (libc)Working Directory.
* getdate: (libc)General Time String Parsing.
* getdate_r: (libc)General Time String Parsing.
* getdelim: (libc)Line Input.
* getdents64: (libc)Low-level Directory Access.
* getdomainnname: (libc)Host Identification.
* getegid: (libc)Reading Persona.
* getentropy: (libc)Unpredictable Bytes.
* getenv: (libc)Environment Access.
* geteuid: (libc)Reading Persona.
* getfsent: (libc)fstab.
* getfsfile: (libc)fstab.
* getfsspec: (libc)fstab.
* getgid: (libc)Reading Persona.
* getgrent: (libc)Scanning All Groups.
* getgrent_r: (libc)Scanning All Groups.
* getgrgid: (libc)Lookup Group.
* getgrgid_r: (libc)Lookup Group.
* getgrnam: (libc)Lookup Group.
* getgrnam_r: (libc)Lookup Group.
* getgrouplist: (libc)Setting Groups.
* getgroups: (libc)Reading Persona.
* gethostbyaddr: (libc)Host Names.
* gethostbyaddr_r: (libc)Host Names.
* gethostbyname2: (libc)Host Names.
* gethostbyname2_r: (libc)Host Names.
* gethostbyname: (libc)Host Names.
* gethostbyname_r: (libc)Host Names.
* gethostent: (libc)Host Names.
* gethostid: (libc)Host Identification.
* gethostname: (libc)Host Identification.
* getitimer: (libc)Setting an Alarm.
* getline: (libc)Line Input.
* getloadavg: (libc)Processor Resources.
* getlogin: (libc)Who Logged In.
* getmntent: (libc)mtab.
* getmntent_r: (libc)mtab.
* getnetbyaddr: (libc)Networks Database.
* getnetbyname: (libc)Networks Database.
* getnetent: (libc)Networks Database.
* getnetgrent: (libc)Lookup Netgroup.
* getnetgrent_r: (libc)Lookup Netgroup.
* getopt: (libc)Using Getopt.
* getopt_long: (libc)Getopt Long Options.
* getopt_long_only: (libc)Getopt Long Options.
* getpagesize: (libc)Query Memory Parameters.
* getpass: (libc)getpass.
* getpayload: (libc)FP Bit Twiddling.
* getpayloadf: (libc)FP Bit Twiddling.
* getpayloadfN: (libc)FP Bit Twiddling.
* getpayloadfNx: (libc)FP Bit Twiddling.
* getpayloadl: (libc)FP Bit Twiddling.
* getpeername: (libc)Who is Connected.
* getpgid: (libc)Process Group Functions.
* getpgrp: (libc)Process Group Functions.
* getpid: (libc)Process Identification.
* getppid: (libc)Process Identification.
* getpriority: (libc)Traditional Scheduling Functions.
* getprotobyname: (libc)Protocols Database.
* getprotobynumber: (libc)Protocols Database.
* getprotoent: (libc)Protocols Database.
* getpt: (libc)Allocation.
* getpwent: (libc)Scanning All Users.
* getpwent_r: (libc)Scanning All Users.
* getpwnam: (libc)Lookup User.
* getpwnam_r: (libc)Lookup User.
* getpwuid: (libc)Lookup User.
* getpwuid_r: (libc)Lookup User.
* getrandom: (libc)Unpredictable Bytes.
* getrlimit64: (libc)Limits on Resources.
* getrlimit: (libc)Limits on Resources.
* getrusage: (libc)Resource Usage.
* gets: (libc)Line Input.
* getservbyname: (libc)Services Database.
* getservbyport: (libc)Services Database.
* getservent: (libc)Services Database.
* getsid: (libc)Process Group Functions.
* getsockname: (libc)Reading Address.
* getsockopt: (libc)Socket Option Functions.
* getsubopt: (libc)Suboptions.
* gettext: (libc)Translation with gettext.
* gettid: (libc)Process Identification.
* gettimeofday: (libc)Getting the Time.
* getuid: (libc)Reading Persona.
* getumask: (libc)Setting Permissions.
* getutent: (libc)Manipulating the Database.
* getutent_r: (libc)Manipulating the Database.
* getutid: (libc)Manipulating the Database.
* getutid_r: (libc)Manipulating the Database.
* getutline: (libc)Manipulating the Database.
* getutline_r: (libc)Manipulating the Database.
* getutmp: (libc)XPG Functions.
* getutmpx: (libc)XPG Functions.
* getutxent: (libc)XPG Functions.
* getutxid: (libc)XPG Functions.
* getutxline: (libc)XPG Functions.
* getw: (libc)Character Input.
* getwc: (libc)Character Input.
* getwc_unlocked: (libc)Character Input.
* getwchar: (libc)Character Input.
* getwchar_unlocked: (libc)Character Input.
* getwd: (libc)Working Directory.
* glob64: (libc)Calling Glob.
* glob: (libc)Calling Glob.
* globfree64: (libc)More Flags for Globbing.
* globfree: (libc)More Flags for Globbing.
* gmtime: (libc)Broken-down Time.
* gmtime_r: (libc)Broken-down Time.
* grantpt: (libc)Allocation.
* gsignal: (libc)Signaling Yourself.
* gtty: (libc)BSD Terminal Modes.
* hasmntopt: (libc)mtab.
* hcreate: (libc)Hash Search Function.
* hcreate_r: (libc)Hash Search Function.
* hdestroy: (libc)Hash Search Function.
* hdestroy_r: (libc)Hash Search Function.
* hsearch: (libc)Hash Search Function.
* hsearch_r: (libc)Hash Search Function.
* htonl: (libc)Byte Order.
* htons: (libc)Byte Order.
* hypot: (libc)Exponents and Logarithms.
* hypotf: (libc)Exponents and Logarithms.
* hypotfN: (libc)Exponents and Logarithms.
* hypotfNx: (libc)Exponents and Logarithms.
* hypotl: (libc)Exponents and Logarithms.
* iconv: (libc)Generic Conversion Interface.
* iconv_close: (libc)Generic Conversion Interface.
* iconv_open: (libc)Generic Conversion Interface.
* if_freenameindex: (libc)Interface Naming.
* if_indextoname: (libc)Interface Naming.
* if_nameindex: (libc)Interface Naming.
* if_nametoindex: (libc)Interface Naming.
* ilogb: (libc)Exponents and Logarithms.
* ilogbf: (libc)Exponents and Logarithms.
* ilogbfN: (libc)Exponents and Logarithms.
* ilogbfNx: (libc)Exponents and Logarithms.
* ilogbl: (libc)Exponents and Logarithms.
* imaxabs: (libc)Absolute Value.
* imaxdiv: (libc)Integer Division.
* in6addr_any: (libc)Host Address Data Type.
* in6addr_loopback: (libc)Host Address Data Type.
* index: (libc)Search Functions.
* inet_addr: (libc)Host Address Functions.
* inet_aton: (libc)Host Address Functions.
* inet_lnaof: (libc)Host Address Functions.
* inet_makeaddr: (libc)Host Address Functions.
* inet_netof: (libc)Host Address Functions.
* inet_network: (libc)Host Address Functions.
* inet_ntoa: (libc)Host Address Functions.
* inet_ntop: (libc)Host Address Functions.
* inet_pton: (libc)Host Address Functions.
* initgroups: (libc)Setting Groups.
* initstate: (libc)BSD Random.
* initstate_r: (libc)BSD Random.
* innetgr: (libc)Netgroup Membership.
* ioctl: (libc)IOCTLs.
* isalnum: (libc)Classification of Characters.
* isalpha: (libc)Classification of Characters.
* isascii: (libc)Classification of Characters.
* isatty: (libc)Is It a Terminal.
* isblank: (libc)Classification of Characters.
* iscanonical: (libc)Floating Point Classes.
* iscntrl: (libc)Classification of Characters.
* isdigit: (libc)Classification of Characters.
* iseqsig: (libc)FP Comparison Functions.
* isfinite: (libc)Floating Point Classes.
* isgraph: (libc)Classification of Characters.
* isgreater: (libc)FP Comparison Functions.
* isgreaterequal: (libc)FP Comparison Functions.
* isinf: (libc)Floating Point Classes.
* isinff: (libc)Floating Point Classes.
* isinfl: (libc)Floating Point Classes.
* isless: (libc)FP Comparison Functions.
* islessequal: (libc)FP Comparison Functions.
* islessgreater: (libc)FP Comparison Functions.
* islower: (libc)Classification of Characters.
* isnan: (libc)Floating Point Classes.
* isnan: (libc)Floating Point Classes.
* isnanf: (libc)Floating Point Classes.
* isnanl: (libc)Floating Point Classes.
* isnormal: (libc)Floating Point Classes.
* isprint: (libc)Classification of Characters.
* ispunct: (libc)Classification of Characters.
* issignaling: (libc)Floating Point Classes.
* isspace: (libc)Classification of Characters.
* issubnormal: (libc)Floating Point Classes.
* isunordered: (libc)FP Comparison Functions.
* isupper: (libc)Classification of Characters.
* iswalnum: (libc)Classification of Wide Characters.
* iswalpha: (libc)Classification of Wide Characters.
* iswblank: (libc)Classification of Wide Characters.
* iswcntrl: (libc)Classification of Wide Characters.
* iswctype: (libc)Classification of Wide Characters.
* iswdigit: (libc)Classification of Wide Characters.
* iswgraph: (libc)Classification of Wide Characters.
* iswlower: (libc)Classification of Wide Characters.
* iswprint: (libc)Classification of Wide Characters.
* iswpunct: (libc)Classification of Wide Characters.
* iswspace: (libc)Classification of Wide Characters.
* iswupper: (libc)Classification of Wide Characters.
* iswxdigit: (libc)Classification of Wide Characters.
* isxdigit: (libc)Classification of Characters.
* iszero: (libc)Floating Point Classes.
* j0: (libc)Special Functions.
* j0f: (libc)Special Functions.
* j0fN: (libc)Special Functions.
* j0fNx: (libc)Special Functions.
* j0l: (libc)Special Functions.
* j1: (libc)Special Functions.
* j1f: (libc)Special Functions.
* j1fN: (libc)Special Functions.
* j1fNx: (libc)Special Functions.
* j1l: (libc)Special Functions.
* jn: (libc)Special Functions.
* jnf: (libc)Special Functions.
* jnfN: (libc)Special Functions.
* jnfNx: (libc)Special Functions.
* jnl: (libc)Special Functions.
* jrand48: (libc)SVID Random.
* jrand48_r: (libc)SVID Random.
* kill: (libc)Signaling Another Process.
* killpg: (libc)Signaling Another Process.
* l64a: (libc)Encode Binary Data.
* labs: (libc)Absolute Value.
* lcong48: (libc)SVID Random.
* lcong48_r: (libc)SVID Random.
* ldexp: (libc)Normalization Functions.
* ldexpf: (libc)Normalization Functions.
* ldexpfN: (libc)Normalization Functions.
* ldexpfNx: (libc)Normalization Functions.
* ldexpl: (libc)Normalization Functions.
* ldiv: (libc)Integer Division.
* lfind: (libc)Array Search Function.
* lgamma: (libc)Special Functions.
* lgamma_r: (libc)Special Functions.
* lgammaf: (libc)Special Functions.
* lgammafN: (libc)Special Functions.
* lgammafN_r: (libc)Special Functions.
* lgammafNx: (libc)Special Functions.
* lgammafNx_r: (libc)Special Functions.
* lgammaf_r: (libc)Special Functions.
* lgammal: (libc)Special Functions.
* lgammal_r: (libc)Special Functions.
* link: (libc)Hard Links.
* linkat: (libc)Hard Links.
* lio_listio64: (libc)Asynchronous Reads/Writes.
* lio_listio: (libc)Asynchronous Reads/Writes.
* listen: (libc)Listening.
* llabs: (libc)Absolute Value.
* lldiv: (libc)Integer Division.
* llogb: (libc)Exponents and Logarithms.
* llogbf: (libc)Exponents and Logarithms.
* llogbfN: (libc)Exponents and Logarithms.
* llogbfNx: (libc)Exponents and Logarithms.
* llogbl: (libc)Exponents and Logarithms.
* llrint: (libc)Rounding Functions.
* llrintf: (libc)Rounding Functions.
* llrintfN: (libc)Rounding Functions.
* llrintfNx: (libc)Rounding Functions.
* llrintl: (libc)Rounding Functions.
* llround: (libc)Rounding Functions.
* llroundf: (libc)Rounding Functions.
* llroundfN: (libc)Rounding Functions.
* llroundfNx: (libc)Rounding Functions.
* llroundl: (libc)Rounding Functions.
* localeconv: (libc)The Lame Way to Locale Data.
* localtime: (libc)Broken-down Time.
* localtime_r: (libc)Broken-down Time.
* log10: (libc)Exponents and Logarithms.
* log10f: (libc)Exponents and Logarithms.
* log10fN: (libc)Exponents and Logarithms.
* log10fNx: (libc)Exponents and Logarithms.
* log10l: (libc)Exponents and Logarithms.
* log1p: (libc)Exponents and Logarithms.
* log1pf: (libc)Exponents and Logarithms.
* log1pfN: (libc)Exponents and Logarithms.
* log1pfNx: (libc)Exponents and Logarithms.
* log1pl: (libc)Exponents and Logarithms.
* log2: (libc)Exponents and Logarithms.
* log2f: (libc)Exponents and Logarithms.
* log2fN: (libc)Exponents and Logarithms.
* log2fNx: (libc)Exponents and Logarithms.
* log2l: (libc)Exponents and Logarithms.
* log: (libc)Exponents and Logarithms.
* logb: (libc)Exponents and Logarithms.
* logbf: (libc)Exponents and Logarithms.
* logbfN: (libc)Exponents and Logarithms.
* logbfNx: (libc)Exponents and Logarithms.
* logbl: (libc)Exponents and Logarithms.
* logf: (libc)Exponents and Logarithms.
* logfN: (libc)Exponents and Logarithms.
* logfNx: (libc)Exponents and Logarithms.
* login: (libc)Logging In and Out.
* login_tty: (libc)Logging In and Out.
* logl: (libc)Exponents and Logarithms.
* logout: (libc)Logging In and Out.
* logwtmp: (libc)Logging In and Out.
* longjmp: (libc)Non-Local Details.
* lrand48: (libc)SVID Random.
* lrand48_r: (libc)SVID Random.
* lrint: (libc)Rounding Functions.
* lrintf: (libc)Rounding Functions.
* lrintfN: (libc)Rounding Functions.
* lrintfNx: (libc)Rounding Functions.
* lrintl: (libc)Rounding Functions.
* lround: (libc)Rounding Functions.
* lroundf: (libc)Rounding Functions.
* lroundfN: (libc)Rounding Functions.
* lroundfNx: (libc)Rounding Functions.
* lroundl: (libc)Rounding Functions.
* lsearch: (libc)Array Search Function.
* lseek64: (libc)File Position Primitive.
* lseek: (libc)File Position Primitive.
* lstat64: (libc)Reading Attributes.
* lstat: (libc)Reading Attributes.
* lutimes: (libc)File Times.
* madvise: (libc)Memory-mapped I/O.
* makecontext: (libc)System V contexts.
* mallinfo2: (libc)Statistics of Malloc.
* malloc: (libc)Basic Allocation.
* mallopt: (libc)Malloc Tunable Parameters.
* mblen: (libc)Non-reentrant Character Conversion.
* mbrlen: (libc)Converting a Character.
* mbrtowc: (libc)Converting a Character.
* mbsinit: (libc)Keeping the state.
* mbsnrtowcs: (libc)Converting Strings.
* mbsrtowcs: (libc)Converting Strings.
* mbstowcs: (libc)Non-reentrant String Conversion.
* mbtowc: (libc)Non-reentrant Character Conversion.
* mcheck: (libc)Heap Consistency Checking.
* memalign: (libc)Aligned Memory Blocks.
* memccpy: (libc)Copying Strings and Arrays.
* memchr: (libc)Search Functions.
* memcmp: (libc)String/Array Comparison.
* memcpy: (libc)Copying Strings and Arrays.
* memfd_create: (libc)Memory-mapped I/O.
* memfrob: (libc)Obfuscating Data.
* memmem: (libc)Search Functions.
* memmove: (libc)Copying Strings and Arrays.
* mempcpy: (libc)Copying Strings and Arrays.
* memrchr: (libc)Search Functions.
* memset: (libc)Copying Strings and Arrays.
* mkdir: (libc)Creating Directories.
* mkdtemp: (libc)Temporary Files.
* mkfifo: (libc)FIFO Special Files.
* mknod: (libc)Making Special Files.
* mkstemp: (libc)Temporary Files.
* mktemp: (libc)Temporary Files.
* mktime: (libc)Broken-down Time.
* mlock2: (libc)Page Lock Functions.
* mlock: (libc)Page Lock Functions.
* mlockall: (libc)Page Lock Functions.
* mmap64: (libc)Memory-mapped I/O.
* mmap: (libc)Memory-mapped I/O.
* modf: (libc)Rounding Functions.
* modff: (libc)Rounding Functions.
* modffN: (libc)Rounding Functions.
* modffNx: (libc)Rounding Functions.
* modfl: (libc)Rounding Functions.
* mount: (libc)Mount-Unmount-Remount.
* mprobe: (libc)Heap Consistency Checking.
* mprotect: (libc)Memory Protection.
* mrand48: (libc)SVID Random.
* mrand48_r: (libc)SVID Random.
* mremap: (libc)Memory-mapped I/O.
* msync: (libc)Memory-mapped I/O.
* mtrace: (libc)Tracing malloc.
* mtx_destroy: (libc)ISO C Mutexes.
* mtx_init: (libc)ISO C Mutexes.
* mtx_lock: (libc)ISO C Mutexes.
* mtx_timedlock: (libc)ISO C Mutexes.
* mtx_trylock: (libc)ISO C Mutexes.
* mtx_unlock: (libc)ISO C Mutexes.
* munlock: (libc)Page Lock Functions.
* munlockall: (libc)Page Lock Functions.
* munmap: (libc)Memory-mapped I/O.
* muntrace: (libc)Tracing malloc.
* nan: (libc)FP Bit Twiddling.
* nanf: (libc)FP Bit Twiddling.
* nanfN: (libc)FP Bit Twiddling.
* nanfNx: (libc)FP Bit Twiddling.
* nanl: (libc)FP Bit Twiddling.
* nanosleep: (libc)Sleeping.
* nearbyint: (libc)Rounding Functions.
* nearbyintf: (libc)Rounding Functions.
* nearbyintfN: (libc)Rounding Functions.
* nearbyintfNx: (libc)Rounding Functions.
* nearbyintl: (libc)Rounding Functions.
* nextafter: (libc)FP Bit Twiddling.
* nextafterf: (libc)FP Bit Twiddling.
* nextafterfN: (libc)FP Bit Twiddling.
* nextafterfNx: (libc)FP Bit Twiddling.
* nextafterl: (libc)FP Bit Twiddling.
* nextdown: (libc)FP Bit Twiddling.
* nextdownf: (libc)FP Bit Twiddling.
* nextdownfN: (libc)FP Bit Twiddling.
* nextdownfNx: (libc)FP Bit Twiddling.
* nextdownl: (libc)FP Bit Twiddling.
* nexttoward: (libc)FP Bit Twiddling.
* nexttowardf: (libc)FP Bit Twiddling.
* nexttowardl: (libc)FP Bit Twiddling.
* nextup: (libc)FP Bit Twiddling.
* nextupf: (libc)FP Bit Twiddling.
* nextupfN: (libc)FP Bit Twiddling.
* nextupfNx: (libc)FP Bit Twiddling.
* nextupl: (libc)FP Bit Twiddling.
* nftw64: (libc)Working with Directory Trees.
* nftw: (libc)Working with Directory Trees.
* ngettext: (libc)Advanced gettext functions.
* nice: (libc)Traditional Scheduling Functions.
* nl_langinfo: (libc)The Elegant and Fast Way.
* nrand48: (libc)SVID Random.
* nrand48_r: (libc)SVID Random.
* ntohl: (libc)Byte Order.
* ntohs: (libc)Byte Order.
* ntp_adjtime: (libc)Setting and Adjusting the Time.
* ntp_gettime: (libc)Setting and Adjusting the Time.
* obstack_1grow: (libc)Growing Objects.
* obstack_1grow_fast: (libc)Extra Fast Growing.
* obstack_alignment_mask: (libc)Obstacks Data Alignment.
* obstack_alloc: (libc)Allocation in an Obstack.
* obstack_base: (libc)Status of an Obstack.
* obstack_blank: (libc)Growing Objects.
* obstack_blank_fast: (libc)Extra Fast Growing.
* obstack_chunk_size: (libc)Obstack Chunks.
* obstack_copy0: (libc)Allocation in an Obstack.
* obstack_copy: (libc)Allocation in an Obstack.
* obstack_finish: (libc)Growing Objects.
* obstack_free: (libc)Freeing Obstack Objects.
* obstack_grow0: (libc)Growing Objects.
* obstack_grow: (libc)Growing Objects.
* obstack_init: (libc)Preparing for Obstacks.
* obstack_int_grow: (libc)Growing Objects.
* obstack_int_grow_fast: (libc)Extra Fast Growing.
* obstack_next_free: (libc)Status of an Obstack.
* obstack_object_size: (libc)Growing Objects.
* obstack_object_size: (libc)Status of an Obstack.
* obstack_printf: (libc)Dynamic Output.
* obstack_ptr_grow: (libc)Growing Objects.
* obstack_ptr_grow_fast: (libc)Extra Fast Growing.
* obstack_room: (libc)Extra Fast Growing.
* obstack_vprintf: (libc)Variable Arguments Output.
* offsetof: (libc)Structure Measurement.
* on_exit: (libc)Cleanups on Exit.
* open64: (libc)Opening and Closing Files.
* open: (libc)Opening and Closing Files.
* open_memstream: (libc)String Streams.
* opendir: (libc)Opening a Directory.
* openlog: (libc)openlog.
* openpty: (libc)Pseudo-Terminal Pairs.
* parse_printf_format: (libc)Parsing a Template String.
* pathconf: (libc)Pathconf.
* pause: (libc)Using Pause.
* pclose: (libc)Pipe to a Subprocess.
* perror: (libc)Error Messages.
* pipe: (libc)Creating a Pipe.
* pkey_alloc: (libc)Memory Protection.
* pkey_free: (libc)Memory Protection.
* pkey_get: (libc)Memory Protection.
* pkey_mprotect: (libc)Memory Protection.
* pkey_set: (libc)Memory Protection.
* popen: (libc)Pipe to a Subprocess.
* posix_fallocate64: (libc)Storage Allocation.
* posix_fallocate: (libc)Storage Allocation.
* posix_memalign: (libc)Aligned Memory Blocks.
* pow: (libc)Exponents and Logarithms.
* powf: (libc)Exponents and Logarithms.
* powfN: (libc)Exponents and Logarithms.
* powfNx: (libc)Exponents and Logarithms.
* powl: (libc)Exponents and Logarithms.
* pread64: (libc)I/O Primitives.
* pread: (libc)I/O Primitives.
* preadv2: (libc)Scatter-Gather.
* preadv64: (libc)Scatter-Gather.
* preadv64v2: (libc)Scatter-Gather.
* preadv: (libc)Scatter-Gather.
* printf: (libc)Formatted Output Functions.
* printf_size: (libc)Predefined Printf Handlers.
* printf_size_info: (libc)Predefined Printf Handlers.
* psignal: (libc)Signal Messages.
* pthread_attr_getsigmask_np: (libc)Initial Thread Signal Mask.
* pthread_attr_setsigmask_np: (libc)Initial Thread Signal Mask.
* pthread_clockjoin_np: (libc)Waiting with Explicit Clocks.
* pthread_cond_clockwait: (libc)Waiting with Explicit Clocks.
* pthread_getattr_default_np: (libc)Default Thread Attributes.
* pthread_getspecific: (libc)Thread-specific Data.
* pthread_key_create: (libc)Thread-specific Data.
* pthread_key_delete: (libc)Thread-specific Data.
* pthread_rwlock_clockrdlock: (libc)Waiting with Explicit Clocks.
* pthread_rwlock_clockwrlock: (libc)Waiting with Explicit Clocks.
* pthread_setattr_default_np: (libc)Default Thread Attributes.
* pthread_setspecific: (libc)Thread-specific Data.
* pthread_timedjoin_np: (libc)Waiting with Explicit Clocks.
* pthread_tryjoin_np: (libc)Waiting with Explicit Clocks.
* ptsname: (libc)Allocation.
* ptsname_r: (libc)Allocation.
* putc: (libc)Simple Output.
* putc_unlocked: (libc)Simple Output.
* putchar: (libc)Simple Output.
* putchar_unlocked: (libc)Simple Output.
* putenv: (libc)Environment Access.
* putpwent: (libc)Writing a User Entry.
* puts: (libc)Simple Output.
* pututline: (libc)Manipulating the Database.
* pututxline: (libc)XPG Functions.
* putw: (libc)Simple Output.
* putwc: (libc)Simple Output.
* putwc_unlocked: (libc)Simple Output.
* putwchar: (libc)Simple Output.
* putwchar_unlocked: (libc)Simple Output.
* pwrite64: (libc)I/O Primitives.
* pwrite: (libc)I/O Primitives.
* pwritev2: (libc)Scatter-Gather.
* pwritev64: (libc)Scatter-Gather.
* pwritev64v2: (libc)Scatter-Gather.
* pwritev: (libc)Scatter-Gather.
* qecvt: (libc)System V Number Conversion.
* qecvt_r: (libc)System V Number Conversion.
* qfcvt: (libc)System V Number Conversion.
* qfcvt_r: (libc)System V Number Conversion.
* qgcvt: (libc)System V Number Conversion.
* qsort: (libc)Array Sort Function.
* raise: (libc)Signaling Yourself.
* rand: (libc)ISO Random.
* rand_r: (libc)ISO Random.
* random: (libc)BSD Random.
* random_r: (libc)BSD Random.
* rawmemchr: (libc)Search Functions.
* read: (libc)I/O Primitives.
* readdir64: (libc)Reading/Closing Directory.
* readdir64_r: (libc)Reading/Closing Directory.
* readdir: (libc)Reading/Closing Directory.
* readdir_r: (libc)Reading/Closing Directory.
* readlink: (libc)Symbolic Links.
* readv: (libc)Scatter-Gather.
* realloc: (libc)Changing Block Size.
* reallocarray: (libc)Changing Block Size.
* realpath: (libc)Symbolic Links.
* recv: (libc)Receiving Data.
* recvfrom: (libc)Receiving Datagrams.
* recvmsg: (libc)Receiving Datagrams.
* regcomp: (libc)POSIX Regexp Compilation.
* regerror: (libc)Regexp Cleanup.
* regexec: (libc)Matching POSIX Regexps.
* regfree: (libc)Regexp Cleanup.
* register_printf_function: (libc)Registering New Conversions.
* remainder: (libc)Remainder Functions.
* remainderf: (libc)Remainder Functions.
* remainderfN: (libc)Remainder Functions.
* remainderfNx: (libc)Remainder Functions.
* remainderl: (libc)Remainder Functions.
* remove: (libc)Deleting Files.
* rename: (libc)Renaming Files.
* rewind: (libc)File Positioning.
* rewinddir: (libc)Random Access Directory.
* rindex: (libc)Search Functions.
* rint: (libc)Rounding Functions.
* rintf: (libc)Rounding Functions.
* rintfN: (libc)Rounding Functions.
* rintfNx: (libc)Rounding Functions.
* rintl: (libc)Rounding Functions.
* rmdir: (libc)Deleting Files.
* round: (libc)Rounding Functions.
* roundeven: (libc)Rounding Functions.
* roundevenf: (libc)Rounding Functions.
* roundevenfN: (libc)Rounding Functions.
* roundevenfNx: (libc)Rounding Functions.
* roundevenl: (libc)Rounding Functions.
* roundf: (libc)Rounding Functions.
* roundfN: (libc)Rounding Functions.
* roundfNx: (libc)Rounding Functions.
* roundl: (libc)Rounding Functions.
* rpmatch: (libc)Yes-or-No Questions.
* sbrk: (libc)Resizing the Data Segment.
* scalb: (libc)Normalization Functions.
* scalbf: (libc)Normalization Functions.
* scalbl: (libc)Normalization Functions.
* scalbln: (libc)Normalization Functions.
* scalblnf: (libc)Normalization Functions.
* scalblnfN: (libc)Normalization Functions.
* scalblnfNx: (libc)Normalization Functions.
* scalblnl: (libc)Normalization Functions.
* scalbn: (libc)Normalization Functions.
* scalbnf: (libc)Normalization Functions.
* scalbnfN: (libc)Normalization Functions.
* scalbnfNx: (libc)Normalization Functions.
* scalbnl: (libc)Normalization Functions.
* scandir64: (libc)Scanning Directory Content.
* scandir: (libc)Scanning Directory Content.
* scanf: (libc)Formatted Input Functions.
* sched_get_priority_max: (libc)Basic Scheduling Functions.
* sched_get_priority_min: (libc)Basic Scheduling Functions.
* sched_getaffinity: (libc)CPU Affinity.
* sched_getparam: (libc)Basic Scheduling Functions.
* sched_getscheduler: (libc)Basic Scheduling Functions.
* sched_rr_get_interval: (libc)Basic Scheduling Functions.
* sched_setaffinity: (libc)CPU Affinity.
* sched_setparam: (libc)Basic Scheduling Functions.
* sched_setscheduler: (libc)Basic Scheduling Functions.
* sched_yield: (libc)Basic Scheduling Functions.
* secure_getenv: (libc)Environment Access.
* seed48: (libc)SVID Random.
* seed48_r: (libc)SVID Random.
* seekdir: (libc)Random Access Directory.
* select: (libc)Waiting for I/O.
* sem_clockwait: (libc)Waiting with Explicit Clocks.
* sem_close: (libc)Semaphores.
* sem_destroy: (libc)Semaphores.
* sem_getvalue: (libc)Semaphores.
* sem_init: (libc)Semaphores.
* sem_open: (libc)Semaphores.
* sem_post: (libc)Semaphores.
* sem_timedwait: (libc)Semaphores.
* sem_trywait: (libc)Semaphores.
* sem_unlink: (libc)Semaphores.
* sem_wait: (libc)Semaphores.
* semctl: (libc)Semaphores.
* semget: (libc)Semaphores.
* semop: (libc)Semaphores.
* semtimedop: (libc)Semaphores.
* send: (libc)Sending Data.
* sendmsg: (libc)Receiving Datagrams.
* sendto: (libc)Sending Datagrams.
* setbuf: (libc)Controlling Buffering.
* setbuffer: (libc)Controlling Buffering.
* setcontext: (libc)System V contexts.
* setdomainname: (libc)Host Identification.
* setegid: (libc)Setting Groups.
* setenv: (libc)Environment Access.
* seteuid: (libc)Setting User ID.
* setfsent: (libc)fstab.
* setgid: (libc)Setting Groups.
* setgrent: (libc)Scanning All Groups.
* setgroups: (libc)Setting Groups.
* sethostent: (libc)Host Names.
* sethostid: (libc)Host Identification.
* sethostname: (libc)Host Identification.
* setitimer: (libc)Setting an Alarm.
* setjmp: (libc)Non-Local Details.
* setlinebuf: (libc)Controlling Buffering.
* setlocale: (libc)Setting the Locale.
* setlogmask: (libc)setlogmask.
* setmntent: (libc)mtab.
* setnetent: (libc)Networks Database.
* setnetgrent: (libc)Lookup Netgroup.
* setpayload: (libc)FP Bit Twiddling.
* setpayloadf: (libc)FP Bit Twiddling.
* setpayloadfN: (libc)FP Bit Twiddling.
* setpayloadfNx: (libc)FP Bit Twiddling.
* setpayloadl: (libc)FP Bit Twiddling.
* setpayloadsig: (libc)FP Bit Twiddling.
* setpayloadsigf: (libc)FP Bit Twiddling.
* setpayloadsigfN: (libc)FP Bit Twiddling.
* setpayloadsigfNx: (libc)FP Bit Twiddling.
* setpayloadsigl: (libc)FP Bit Twiddling.
* setpgid: (libc)Process Group Functions.
* setpgrp: (libc)Process Group Functions.
* setpriority: (libc)Traditional Scheduling Functions.
* setprotoent: (libc)Protocols Database.
* setpwent: (libc)Scanning All Users.
* setregid: (libc)Setting Groups.
* setreuid: (libc)Setting User ID.
* setrlimit64: (libc)Limits on Resources.
* setrlimit: (libc)Limits on Resources.
* setservent: (libc)Services Database.
* setsid: (libc)Process Group Functions.
* setsockopt: (libc)Socket Option Functions.
* setstate: (libc)BSD Random.
* setstate_r: (libc)BSD Random.
* settimeofday: (libc)Setting and Adjusting the Time.
* setuid: (libc)Setting User ID.
* setutent: (libc)Manipulating the Database.
* setutxent: (libc)XPG Functions.
* setvbuf: (libc)Controlling Buffering.
* shm_open: (libc)Memory-mapped I/O.
* shm_unlink: (libc)Memory-mapped I/O.
* shutdown: (libc)Closing a Socket.
* sigabbrev_np: (libc)Signal Messages.
* sigaction: (libc)Advanced Signal Handling.
* sigaddset: (libc)Signal Sets.
* sigaltstack: (libc)Signal Stack.
* sigblock: (libc)BSD Signal Handling.
* sigdelset: (libc)Signal Sets.
* sigdescr_np: (libc)Signal Messages.
* sigemptyset: (libc)Signal Sets.
* sigfillset: (libc)Signal Sets.
* siginterrupt: (libc)BSD Signal Handling.
* sigismember: (libc)Signal Sets.
* siglongjmp: (libc)Non-Local Exits and Signals.
* sigmask: (libc)BSD Signal Handling.
* signal: (libc)Basic Signal Handling.
* signbit: (libc)FP Bit Twiddling.
* significand: (libc)Normalization Functions.
* significandf: (libc)Normalization Functions.
* significandl: (libc)Normalization Functions.
* sigpause: (libc)BSD Signal Handling.
* sigpending: (libc)Checking for Pending Signals.
* sigprocmask: (libc)Process Signal Mask.
* sigsetjmp: (libc)Non-Local Exits and Signals.
* sigsetmask: (libc)BSD Signal Handling.
* sigstack: (libc)Signal Stack.
* sigsuspend: (libc)Sigsuspend.
* sin: (libc)Trig Functions.
* sincos: (libc)Trig Functions.
* sincosf: (libc)Trig Functions.
* sincosfN: (libc)Trig Functions.
* sincosfNx: (libc)Trig Functions.
* sincosl: (libc)Trig Functions.
* sinf: (libc)Trig Functions.
* sinfN: (libc)Trig Functions.
* sinfNx: (libc)Trig Functions.
* sinh: (libc)Hyperbolic Functions.
* sinhf: (libc)Hyperbolic Functions.
* sinhfN: (libc)Hyperbolic Functions.
* sinhfNx: (libc)Hyperbolic Functions.
* sinhl: (libc)Hyperbolic Functions.
* sinl: (libc)Trig Functions.
* sleep: (libc)Sleeping.
* snprintf: (libc)Formatted Output Functions.
* socket: (libc)Creating a Socket.
* socketpair: (libc)Socket Pairs.
* sprintf: (libc)Formatted Output Functions.
* sqrt: (libc)Exponents and Logarithms.
* sqrtf: (libc)Exponents and Logarithms.
* sqrtfN: (libc)Exponents and Logarithms.
* sqrtfNx: (libc)Exponents and Logarithms.
* sqrtl: (libc)Exponents and Logarithms.
* srand48: (libc)SVID Random.
* srand48_r: (libc)SVID Random.
* srand: (libc)ISO Random.
* srandom: (libc)BSD Random.
* srandom_r: (libc)BSD Random.
* sscanf: (libc)Formatted Input Functions.
* ssignal: (libc)Basic Signal Handling.
* stat64: (libc)Reading Attributes.
* stat: (libc)Reading Attributes.
* stime: (libc)Setting and Adjusting the Time.
* stpcpy: (libc)Copying Strings and Arrays.
* stpncpy: (libc)Truncating Strings.
* strcasecmp: (libc)String/Array Comparison.
* strcasestr: (libc)Search Functions.
* strcat: (libc)Concatenating Strings.
* strchr: (libc)Search Functions.
* strchrnul: (libc)Search Functions.
* strcmp: (libc)String/Array Comparison.
* strcoll: (libc)Collation Functions.
* strcpy: (libc)Copying Strings and Arrays.
* strcspn: (libc)Search Functions.
* strdup: (libc)Copying Strings and Arrays.
* strdupa: (libc)Copying Strings and Arrays.
* strerror: (libc)Error Messages.
* strerror_r: (libc)Error Messages.
* strerrordesc_np: (libc)Error Messages.
* strerrorname_np: (libc)Error Messages.
* strfmon: (libc)Formatting Numbers.
* strfromd: (libc)Printing of Floats.
* strfromf: (libc)Printing of Floats.
* strfromfN: (libc)Printing of Floats.
* strfromfNx: (libc)Printing of Floats.
* strfroml: (libc)Printing of Floats.
* strfry: (libc)Shuffling Bytes.
* strftime: (libc)Formatting Calendar Time.
* strlen: (libc)String Length.
* strncasecmp: (libc)String/Array Comparison.
* strncat: (libc)Truncating Strings.
* strncmp: (libc)String/Array Comparison.
* strncpy: (libc)Truncating Strings.
* strndup: (libc)Truncating Strings.
* strndupa: (libc)Truncating Strings.
* strnlen: (libc)String Length.
* strpbrk: (libc)Search Functions.
* strptime: (libc)Low-Level Time String Parsing.
* strrchr: (libc)Search Functions.
* strsep: (libc)Finding Tokens in a String.
* strsignal: (libc)Signal Messages.
* strspn: (libc)Search Functions.
* strstr: (libc)Search Functions.
* strtod: (libc)Parsing of Floats.
* strtof: (libc)Parsing of Floats.
* strtofN: (libc)Parsing of Floats.
* strtofNx: (libc)Parsing of Floats.
* strtoimax: (libc)Parsing of Integers.
* strtok: (libc)Finding Tokens in a String.
* strtok_r: (libc)Finding Tokens in a String.
* strtol: (libc)Parsing of Integers.
* strtold: (libc)Parsing of Floats.
* strtoll: (libc)Parsing of Integers.
* strtoq: (libc)Parsing of Integers.
* strtoul: (libc)Parsing of Integers.
* strtoull: (libc)Parsing of Integers.
* strtoumax: (libc)Parsing of Integers.
* strtouq: (libc)Parsing of Integers.
* strverscmp: (libc)String/Array Comparison.
* strxfrm: (libc)Collation Functions.
* stty: (libc)BSD Terminal Modes.
* swapcontext: (libc)System V contexts.
* swprintf: (libc)Formatted Output Functions.
* swscanf: (libc)Formatted Input Functions.
* symlink: (libc)Symbolic Links.
* sync: (libc)Synchronizing I/O.
* syscall: (libc)System Calls.
* sysconf: (libc)Sysconf Definition.
* syslog: (libc)syslog; vsyslog.
* system: (libc)Running a Command.
* sysv_signal: (libc)Basic Signal Handling.
* tan: (libc)Trig Functions.
* tanf: (libc)Trig Functions.
* tanfN: (libc)Trig Functions.
* tanfNx: (libc)Trig Functions.
* tanh: (libc)Hyperbolic Functions.
* tanhf: (libc)Hyperbolic Functions.
* tanhfN: (libc)Hyperbolic Functions.
* tanhfNx: (libc)Hyperbolic Functions.
* tanhl: (libc)Hyperbolic Functions.
* tanl: (libc)Trig Functions.
* tcdrain: (libc)Line Control.
* tcflow: (libc)Line Control.
* tcflush: (libc)Line Control.
* tcgetattr: (libc)Mode Functions.
* tcgetpgrp: (libc)Terminal Access Functions.
* tcgetsid: (libc)Terminal Access Functions.
* tcsendbreak: (libc)Line Control.
* tcsetattr: (libc)Mode Functions.
* tcsetpgrp: (libc)Terminal Access Functions.
* tdelete: (libc)Tree Search Function.
* tdestroy: (libc)Tree Search Function.
* telldir: (libc)Random Access Directory.
* tempnam: (libc)Temporary Files.
* textdomain: (libc)Locating gettext catalog.
* tfind: (libc)Tree Search Function.
* tgamma: (libc)Special Functions.
* tgammaf: (libc)Special Functions.
* tgammafN: (libc)Special Functions.
* tgammafNx: (libc)Special Functions.
* tgammal: (libc)Special Functions.
* tgkill: (libc)Signaling Another Process.
* thrd_create: (libc)ISO C Thread Management.
* thrd_current: (libc)ISO C Thread Management.
* thrd_detach: (libc)ISO C Thread Management.
* thrd_equal: (libc)ISO C Thread Management.
* thrd_exit: (libc)ISO C Thread Management.
* thrd_join: (libc)ISO C Thread Management.
* thrd_sleep: (libc)ISO C Thread Management.
* thrd_yield: (libc)ISO C Thread Management.
* time: (libc)Getting the Time.
* timegm: (libc)Broken-down Time.
* timelocal: (libc)Broken-down Time.
* times: (libc)Processor Time.
* tmpfile64: (libc)Temporary Files.
* tmpfile: (libc)Temporary Files.
* tmpnam: (libc)Temporary Files.
* tmpnam_r: (libc)Temporary Files.
* toascii: (libc)Case Conversion.
* tolower: (libc)Case Conversion.
* totalorder: (libc)FP Comparison Functions.
* totalorderf: (libc)FP Comparison Functions.
* totalorderfN: (libc)FP Comparison Functions.
* totalorderfNx: (libc)FP Comparison Functions.
* totalorderl: (libc)FP Comparison Functions.
* totalordermag: (libc)FP Comparison Functions.
* totalordermagf: (libc)FP Comparison Functions.
* totalordermagfN: (libc)FP Comparison Functions.
* totalordermagfNx: (libc)FP Comparison Functions.
* totalordermagl: (libc)FP Comparison Functions.
* toupper: (libc)Case Conversion.
* towctrans: (libc)Wide Character Case Conversion.
* towlower: (libc)Wide Character Case Conversion.
* towupper: (libc)Wide Character Case Conversion.
* trunc: (libc)Rounding Functions.
* truncate64: (libc)File Size.
* truncate: (libc)File Size.
* truncf: (libc)Rounding Functions.
* truncfN: (libc)Rounding Functions.
* truncfNx: (libc)Rounding Functions.
* truncl: (libc)Rounding Functions.
* tsearch: (libc)Tree Search Function.
* tss_create: (libc)ISO C Thread-local Storage.
* tss_delete: (libc)ISO C Thread-local Storage.
* tss_get: (libc)ISO C Thread-local Storage.
* tss_set: (libc)ISO C Thread-local Storage.
* ttyname: (libc)Is It a Terminal.
* ttyname_r: (libc)Is It a Terminal.
* twalk: (libc)Tree Search Function.
* twalk_r: (libc)Tree Search Function.
* tzset: (libc)Time Zone Functions.
* ufromfp: (libc)Rounding Functions.
* ufromfpf: (libc)Rounding Functions.
* ufromfpfN: (libc)Rounding Functions.
* ufromfpfNx: (libc)Rounding Functions.
* ufromfpl: (libc)Rounding Functions.
* ufromfpx: (libc)Rounding Functions.
* ufromfpxf: (libc)Rounding Functions.
* ufromfpxfN: (libc)Rounding Functions.
* ufromfpxfNx: (libc)Rounding Functions.
* ufromfpxl: (libc)Rounding Functions.
* ulimit: (libc)Limits on Resources.
* umask: (libc)Setting Permissions.
* umount2: (libc)Mount-Unmount-Remount.
* umount: (libc)Mount-Unmount-Remount.
* uname: (libc)Platform Type.
* ungetc: (libc)How Unread.
* ungetwc: (libc)How Unread.
* unlink: (libc)Deleting Files.
* unlockpt: (libc)Allocation.
* unsetenv: (libc)Environment Access.
* updwtmp: (libc)Manipulating the Database.
* utime: (libc)File Times.
* utimes: (libc)File Times.
* utmpname: (libc)Manipulating the Database.
* utmpxname: (libc)XPG Functions.
* va_arg: (libc)Argument Macros.
* va_copy: (libc)Argument Macros.
* va_end: (libc)Argument Macros.
* va_start: (libc)Argument Macros.
* valloc: (libc)Aligned Memory Blocks.
* vasprintf: (libc)Variable Arguments Output.
* verr: (libc)Error Messages.
* verrx: (libc)Error Messages.
* versionsort64: (libc)Scanning Directory Content.
* versionsort: (libc)Scanning Directory Content.
* vfork: (libc)Creating a Process.
* vfprintf: (libc)Variable Arguments Output.
* vfscanf: (libc)Variable Arguments Input.
* vfwprintf: (libc)Variable Arguments Output.
* vfwscanf: (libc)Variable Arguments Input.
* vlimit: (libc)Limits on Resources.
* vprintf: (libc)Variable Arguments Output.
* vscanf: (libc)Variable Arguments Input.
* vsnprintf: (libc)Variable Arguments Output.
* vsprintf: (libc)Variable Arguments Output.
* vsscanf: (libc)Variable Arguments Input.
* vswprintf: (libc)Variable Arguments Output.
* vswscanf: (libc)Variable Arguments Input.
* vsyslog: (libc)syslog; vsyslog.
* vwarn: (libc)Error Messages.
* vwarnx: (libc)Error Messages.
* vwprintf: (libc)Variable Arguments Output.
* vwscanf: (libc)Variable Arguments Input.
* wait3: (libc)BSD Wait Functions.
* wait4: (libc)Process Completion.
* wait: (libc)Process Completion.
* waitpid: (libc)Process Completion.
* warn: (libc)Error Messages.
* warnx: (libc)Error Messages.
* wcpcpy: (libc)Copying Strings and Arrays.
* wcpncpy: (libc)Truncating Strings.
* wcrtomb: (libc)Converting a Character.
* wcscasecmp: (libc)String/Array Comparison.
* wcscat: (libc)Concatenating Strings.
* wcschr: (libc)Search Functions.
* wcschrnul: (libc)Search Functions.
* wcscmp: (libc)String/Array Comparison.
* wcscoll: (libc)Collation Functions.
* wcscpy: (libc)Copying Strings and Arrays.
* wcscspn: (libc)Search Functions.
* wcsdup: (libc)Copying Strings and Arrays.
* wcsftime: (libc)Formatting Calendar Time.
* wcslen: (libc)String Length.
* wcsncasecmp: (libc)String/Array Comparison.
* wcsncat: (libc)Truncating Strings.
* wcsncmp: (libc)String/Array Comparison.
* wcsncpy: (libc)Truncating Strings.
* wcsnlen: (libc)String Length.
* wcsnrtombs: (libc)Converting Strings.
* wcspbrk: (libc)Search Functions.
* wcsrchr: (libc)Search Functions.
* wcsrtombs: (libc)Converting Strings.
* wcsspn: (libc)Search Functions.
* wcsstr: (libc)Search Functions.
* wcstod: (libc)Parsing of Floats.
* wcstof: (libc)Parsing of Floats.
* wcstofN: (libc)Parsing of Floats.
* wcstofNx: (libc)Parsing of Floats.
* wcstoimax: (libc)Parsing of Integers.
* wcstok: (libc)Finding Tokens in a String.
* wcstol: (libc)Parsing of Integers.
* wcstold: (libc)Parsing of Floats.
* wcstoll: (libc)Parsing of Integers.
* wcstombs: (libc)Non-reentrant String Conversion.
* wcstoq: (libc)Parsing of Integers.
* wcstoul: (libc)Parsing of Integers.
* wcstoull: (libc)Parsing of Integers.
* wcstoumax: (libc)Parsing of Integers.
* wcstouq: (libc)Parsing of Integers.
* wcswcs: (libc)Search Functions.
* wcsxfrm: (libc)Collation Functions.
* wctob: (libc)Converting a Character.
* wctomb: (libc)Non-reentrant Character Conversion.
* wctrans: (libc)Wide Character Case Conversion.
* wctype: (libc)Classification of Wide Characters.
* wmemchr: (libc)Search Functions.
* wmemcmp: (libc)String/Array Comparison.
* wmemcpy: (libc)Copying Strings and Arrays.
* wmemmove: (libc)Copying Strings and Arrays.
* wmempcpy: (libc)Copying Strings and Arrays.
* wmemset: (libc)Copying Strings and Arrays.
* wordexp: (libc)Calling Wordexp.
* wordfree: (libc)Calling Wordexp.
* wprintf: (libc)Formatted Output Functions.
* write: (libc)I/O Primitives.
* writev: (libc)Scatter-Gather.
* wscanf: (libc)Formatted Input Functions.
* y0: (libc)Special Functions.
* y0f: (libc)Special Functions.
* y0fN: (libc)Special Functions.
* y0fNx: (libc)Special Functions.
* y0l: (libc)Special Functions.
* y1: (libc)Special Functions.
* y1f: (libc)Special Functions.
* y1fN: (libc)Special Functions.
* y1fNx: (libc)Special Functions.
* y1l: (libc)Special Functions.
* yn: (libc)Special Functions.
* ynf: (libc)Special Functions.
* ynfN: (libc)Special Functions.
* ynfNx: (libc)Special Functions.
* ynl: (libc)Special Functions.
END-INFO-DIR-ENTRY


File: libc.info,  Node: Broken-down Time,  Next: Formatting Calendar Time,  Prev: Setting and Adjusting the Time,  Up: Calendar Time

21.5.3 Broken-down Time
-----------------------

Simple calendar times represent absolute times as elapsed times since an
epoch.  This is convenient for computation, but has no relation to the
way people normally think of calendar time.  By contrast, “broken-down
time” is a binary representation of calendar time separated into year,
month, day, and so on.  Broken-down time values are not useful for
calculations, but they are useful for printing human readable time
information.

   A broken-down time value is always relative to a choice of time zone,
and it also indicates which time zone that is.

   The symbols in this section are declared in the header file ‘time.h’.

 -- Data Type: struct tm

     This is the data type used to represent a broken-down time.  The
     structure contains at least the following members, which can appear
     in any order.

     ‘int tm_sec’
          This is the number of full seconds since the top of the minute
          (normally in the range ‘0’ through ‘59’, but the actual upper
          limit is ‘60’, to allow for leap seconds if leap second
          support is available).

     ‘int tm_min’
          This is the number of full minutes since the top of the hour
          (in the range ‘0’ through ‘59’).

     ‘int tm_hour’
          This is the number of full hours past midnight (in the range
          ‘0’ through ‘23’).

     ‘int tm_mday’
          This is the ordinal day of the month (in the range ‘1’ through
          ‘31’).  Watch out for this one!  As the only ordinal number in
          the structure, it is inconsistent with the rest of the
          structure.

     ‘int tm_mon’
          This is the number of full calendar months since the beginning
          of the year (in the range ‘0’ through ‘11’).  Watch out for
          this one!  People usually use ordinal numbers for
          month-of-year (where January = 1).

     ‘int tm_year’
          This is the number of full calendar years since 1900.

     ‘int tm_wday’
          This is the number of full days since Sunday (in the range ‘0’
          through ‘6’).

     ‘int tm_yday’
          This is the number of full days since the beginning of the
          year (in the range ‘0’ through ‘365’).

     ‘int tm_isdst’
          This is a flag that indicates whether Daylight Saving Time is
          (or was, or will be) in effect at the time described.  The
          value is positive if Daylight Saving Time is in effect, zero
          if it is not, and negative if the information is not
          available.

     ‘long int tm_gmtoff’
          This field describes the time zone that was used to compute
          this broken-down time value, including any adjustment for
          daylight saving; it is the number of seconds that you must add
          to UTC to get local time.  You can also think of this as the
          number of seconds east of UTC. For example, for U.S. Eastern
          Standard Time, the value is ‘-5*60*60’.  The ‘tm_gmtoff’ field
          is derived from BSD and is a GNU library extension; it is not
          visible in a strict ISO C environment.

     ‘const char *tm_zone’
          This field is the name for the time zone that was used to
          compute this broken-down time value.  Like ‘tm_gmtoff’, this
          field is a BSD and GNU extension, and is not visible in a
          strict ISO C environment.

 -- Function: struct tm * localtime (const time_t *TIME)

     Preliminary: | MT-Unsafe race:tmbuf env locale | AS-Unsafe heap
     lock | AC-Unsafe lock mem fd | *Note POSIX Safety Concepts::.

     The ‘localtime’ function converts the simple time pointed to by
     TIME to broken-down time representation, expressed relative to the
     user’s specified time zone.

     The return value is a pointer to a static broken-down time
     structure, which might be overwritten by subsequent calls to
     ‘ctime’, ‘gmtime’, or ‘localtime’.  (But no other library function
     overwrites the contents of this object.)

     The return value is the null pointer if TIME cannot be represented
     as a broken-down time; typically this is because the year cannot
     fit into an ‘int’.

     Calling ‘localtime’ also sets the current time zone as if ‘tzset’
     were called.  *Note Time Zone Functions::.

   Using the ‘localtime’ function is a big problem in multi-threaded
programs.  The result is returned in a static buffer and this is used in
all threads.  POSIX.1c introduced a variant of this function.

 -- Function: struct tm * localtime_r (const time_t *TIME, struct tm
          *RESULTP)

     Preliminary: | MT-Safe env locale | AS-Unsafe heap lock | AC-Unsafe
     lock mem fd | *Note POSIX Safety Concepts::.

     The ‘localtime_r’ function works just like the ‘localtime’
     function.  It takes a pointer to a variable containing a simple
     time and converts it to the broken-down time format.

     But the result is not placed in a static buffer.  Instead it is
     placed in the object of type ‘struct tm’ to which the parameter
     RESULTP points.

     If the conversion is successful the function returns a pointer to
     the object the result was written into, i.e., it returns RESULTP.

 -- Function: struct tm * gmtime (const time_t *TIME)

     Preliminary: | MT-Unsafe race:tmbuf env locale | AS-Unsafe heap
     lock | AC-Unsafe lock mem fd | *Note POSIX Safety Concepts::.

     This function is similar to ‘localtime’, except that the
     broken-down time is expressed as Coordinated Universal Time (UTC)
     (formerly called Greenwich Mean Time (GMT)) rather than relative to
     a local time zone.

   As for the ‘localtime’ function we have the problem that the result
is placed in a static variable.  POSIX.1c also provides a replacement
for ‘gmtime’.

 -- Function: struct tm * gmtime_r (const time_t *TIME, struct tm
          *RESULTP)

     Preliminary: | MT-Safe env locale | AS-Unsafe heap lock | AC-Unsafe
     lock mem fd | *Note POSIX Safety Concepts::.

     This function is similar to ‘localtime_r’, except that it converts
     just like ‘gmtime’ the given time as Coordinated Universal Time.

     If the conversion is successful the function returns a pointer to
     the object the result was written into, i.e., it returns RESULTP.

 -- Function: time_t mktime (struct tm *BROKENTIME)

     Preliminary: | MT-Safe env locale | AS-Unsafe heap lock | AC-Unsafe
     lock mem fd | *Note POSIX Safety Concepts::.

     The ‘mktime’ function converts a broken-down time structure to a
     simple time representation.  It also normalizes the contents of the
     broken-down time structure, and fills in some components based on
     the values of the others.

     The ‘mktime’ function ignores the specified contents of the
     ‘tm_wday’, ‘tm_yday’, ‘tm_gmtoff’, and ‘tm_zone’ members of the
     broken-down time structure.  It uses the values of the other
     components to determine the calendar time; it’s permissible for
     these components to have unnormalized values outside their normal
     ranges.  The last thing that ‘mktime’ does is adjust the components
     of the BROKENTIME structure, including the members that were
     initially ignored.

     If the specified broken-down time cannot be represented as a simple
     time, ‘mktime’ returns a value of ‘(time_t)(-1)’ and does not
     modify the contents of BROKENTIME.

     Calling ‘mktime’ also sets the current time zone as if ‘tzset’ were
     called; ‘mktime’ uses this information instead of BROKENTIME’s
     initial ‘tm_gmtoff’ and ‘tm_zone’ members.  *Note Time Zone
     Functions::.

 -- Function: time_t timelocal (struct tm *BROKENTIME)

     Preliminary: | MT-Safe env locale | AS-Unsafe heap lock | AC-Unsafe
     lock mem fd | *Note POSIX Safety Concepts::.

     ‘timelocal’ is functionally identical to ‘mktime’, but more
     mnemonically named.  Note that it is the inverse of the ‘localtime’
     function.

     *Portability note:* ‘mktime’ is essentially universally available.
     ‘timelocal’ is rather rare.

 -- Function: time_t timegm (struct tm *BROKENTIME)

     Preliminary: | MT-Safe env locale | AS-Unsafe heap lock | AC-Unsafe
     lock mem fd | *Note POSIX Safety Concepts::.

     ‘timegm’ is functionally identical to ‘mktime’ except it always
     takes the input values to be Coordinated Universal Time (UTC)
     regardless of any local time zone setting.

     Note that ‘timegm’ is the inverse of ‘gmtime’.

     *Portability note:* ‘mktime’ is essentially universally available.
     ‘timegm’ is rather rare.  For the most portable conversion from a
     UTC broken-down time to a simple time, set the ‘TZ’ environment
     variable to UTC, call ‘mktime’, then set ‘TZ’ back.


File: libc.info,  Node: Formatting Calendar Time,  Next: Parsing Date and Time,  Prev: Broken-down Time,  Up: Calendar Time

21.5.4 Formatting Calendar Time
-------------------------------

The functions described in this section format calendar time values as
strings.  These functions are declared in the header file ‘time.h’.

 -- Function: char * asctime (const struct tm *BROKENTIME)

     Preliminary: | MT-Unsafe race:asctime locale | AS-Unsafe | AC-Safe
     | *Note POSIX Safety Concepts::.

     The ‘asctime’ function converts the broken-down time value that
     BROKENTIME points to into a string in a standard format:

          "Tue May 21 13:46:22 1991\n"

     The abbreviations for the days of week are: ‘Sun’, ‘Mon’, ‘Tue’,
     ‘Wed’, ‘Thu’, ‘Fri’, and ‘Sat’.

     The abbreviations for the months are: ‘Jan’, ‘Feb’, ‘Mar’, ‘Apr’,
     ‘May’, ‘Jun’, ‘Jul’, ‘Aug’, ‘Sep’, ‘Oct’, ‘Nov’, and ‘Dec’.

     The return value points to a statically allocated string, which
     might be overwritten by subsequent calls to ‘asctime’ or ‘ctime’.
     (But no other library function overwrites the contents of this
     string.)

 -- Function: char * asctime_r (const struct tm *BROKENTIME, char
          *BUFFER)

     Preliminary: | MT-Safe locale | AS-Safe | AC-Safe | *Note POSIX
     Safety Concepts::.

     This function is similar to ‘asctime’ but instead of placing the
     result in a static buffer it writes the string in the buffer
     pointed to by the parameter BUFFER.  This buffer should have room
     for at least 26 bytes, including the terminating null.

     If no error occurred the function returns a pointer to the string
     the result was written into, i.e., it returns BUFFER.  Otherwise it
     returns ‘NULL’.

 -- Function: char * ctime (const time_t *TIME)

     Preliminary: | MT-Unsafe race:tmbuf race:asctime env locale |
     AS-Unsafe heap lock | AC-Unsafe lock mem fd | *Note POSIX Safety
     Concepts::.

     The ‘ctime’ function is similar to ‘asctime’, except that you
     specify the calendar time argument as a ‘time_t’ simple time value
     rather than in broken-down local time format.  It is equivalent to

          asctime (localtime (TIME))

     Calling ‘ctime’ also sets the current time zone as if ‘tzset’ were
     called.  *Note Time Zone Functions::.

 -- Function: char * ctime_r (const time_t *TIME, char *BUFFER)

     Preliminary: | MT-Safe env locale | AS-Unsafe heap lock | AC-Unsafe
     lock mem fd | *Note POSIX Safety Concepts::.

     This function is similar to ‘ctime’, but places the result in the
     string pointed to by BUFFER.  It is equivalent to (written using
     gcc extensions, *note (gcc)Statement Exprs::):

          ({ struct tm tm; asctime_r (localtime_r (time, &tm), buf); })

     If no error occurred the function returns a pointer to the string
     the result was written into, i.e., it returns BUFFER.  Otherwise it
     returns ‘NULL’.

 -- Function: size_t strftime (char *S, size_t SIZE, const char
          *TEMPLATE, const struct tm *BROKENTIME)

     Preliminary: | MT-Safe env locale | AS-Unsafe corrupt heap lock
     dlopen | AC-Unsafe corrupt lock mem fd | *Note POSIX Safety
     Concepts::.

     This function is similar to the ‘sprintf’ function (*note Formatted
     Input::), but the conversion specifications that can appear in the
     format template TEMPLATE are specialized for printing components of
     the date and time BROKENTIME according to the locale currently
     specified for time conversion (*note Locales::) and the current
     time zone (*note Time Zone Functions::).

     Ordinary characters appearing in the TEMPLATE are copied to the
     output string S; this can include multibyte character sequences.
     Conversion specifiers are introduced by a ‘%’ character, followed
     by an optional flag which can be one of the following.  These flags
     are all GNU extensions.  The first three affect only the output of
     numbers:

     ‘_’
          The number is padded with spaces.

     ‘-’
          The number is not padded at all.

     ‘0’
          The number is padded with zeros even if the format specifies
          padding with spaces.

     ‘^’
          The output uses uppercase characters, but only if this is
          possible (*note Case Conversion::).

     The default action is to pad the number with zeros to keep it a
     constant width.  Numbers that do not have a range indicated below
     are never padded, since there is no natural width for them.

     Following the flag an optional specification of the width is
     possible.  This is specified in decimal notation.  If the natural
     size of the output of the field has less than the specified number
     of characters, the result is written right adjusted and space
     padded to the given size.

     An optional modifier can follow the optional flag and width
     specification.  The modifiers, which were first standardized by
     POSIX.2-1992 and by ISO C99, are:

     ‘E’
          Use the locale’s alternative representation for date and time.
          This modifier applies to the ‘%c’, ‘%C’, ‘%x’, ‘%X’, ‘%y’ and
          ‘%Y’ format specifiers.  In a Japanese locale, for example,
          ‘%Ex’ might yield a date format based on the Japanese
          Emperors’ reigns.

     ‘O’
          With all format specifiers that produce numbers: use the
          locale’s alternative numeric symbols.

          With ‘%B’, ‘%b’, and ‘%h’: use the grammatical form for month
          names that is appropriate when the month is named by itself,
          rather than the form that is appropriate when the month is
          used as part of a complete date.  The ‘%OB’ and ‘%Ob’ formats
          are a C2X feature, specified in C2X to use the locale’s
          ‘alternative’ month name; the GNU C Library extends this
          specification to say that the form used in a complete date is
          the default and the form naming the month by itself is the
          alternative.

     If the format supports the modifier but no alternative
     representation is available, it is ignored.

     The conversion specifier ends with a format specifier taken from
     the following list.  The whole ‘%’ sequence is replaced in the
     output string as follows:

     ‘%a’
          The abbreviated weekday name according to the current locale.

     ‘%A’
          The full weekday name according to the current locale.

     ‘%b’
          The abbreviated month name according to the current locale, in
          the grammatical form used when the month is part of a complete
          date.  As a C2X feature (with a more detailed specification in
          the GNU C Library), the ‘O’ modifier can be used (‘%Ob’) to
          get the grammatical form used when the month is named by
          itself.

     ‘%B’
          The full month name according to the current locale, in the
          grammatical form used when the month is part of a complete
          date.  As a C2X feature (with a more detailed specification in
          the GNU C Library), the ‘O’ modifier can be used (‘%OB’) to
          get the grammatical form used when the month is named by
          itself.

          Note that not all languages need two different forms of the
          month names, so the text produced by ‘%B’ and ‘%OB’, and by
          ‘%b’ and ‘%Ob’, may or may not be the same, depending on the
          locale.

     ‘%c’
          The preferred calendar time representation for the current
          locale.

     ‘%C’
          The century of the year.  This is equivalent to the greatest
          integer not greater than the year divided by 100.

          If the ‘E’ modifier is specified (‘%EC’), instead produces the
          name of the period for the year (e.g. an era name) in the
          locale’s alternative calendar.

          This format was first standardized by POSIX.2-1992 and by
          ISO C99.

     ‘%d’
          The day of the month as a decimal number (range ‘01’ through
          ‘31’).

     ‘%D’
          The date using the format ‘%m/%d/%y’.

          This format was first standardized by POSIX.2-1992 and by
          ISO C99.

     ‘%e’
          The day of the month like with ‘%d’, but padded with spaces
          (range ‘ 1’ through ‘31’).

          This format was first standardized by POSIX.2-1992 and by
          ISO C99.

     ‘%F’
          The date using the format ‘%Y-%m-%d’.  This is the form
          specified in the ISO 8601 standard and is the preferred form
          for all uses.

          This format was first standardized by ISO C99 and by
          POSIX.1-2001.

     ‘%g’
          The year corresponding to the ISO week number, but without the
          century (range ‘00’ through ‘99’).  This has the same format
          and value as ‘%y’, except that if the ISO week number (see
          ‘%V’) belongs to the previous or next year, that year is used
          instead.

          This format was first standardized by ISO C99 and by
          POSIX.1-2001.

     ‘%G’
          The year corresponding to the ISO week number.  This has the
          same format and value as ‘%Y’, except that if the ISO week
          number (see ‘%V’) belongs to the previous or next year, that
          year is used instead.

          This format was first standardized by ISO C99 and by
          POSIX.1-2001 but was previously available as a GNU extension.

     ‘%h’
          The abbreviated month name according to the current locale.
          The action is the same as for ‘%b’.

          This format was first standardized by POSIX.2-1992 and by
          ISO C99.

     ‘%H’
          The hour as a decimal number, using a 24-hour clock (range
          ‘00’ through ‘23’).

     ‘%I’
          The hour as a decimal number, using a 12-hour clock (range
          ‘01’ through ‘12’).

     ‘%j’
          The day of the year as a decimal number (range ‘001’ through
          ‘366’).

     ‘%k’
          The hour as a decimal number, using a 24-hour clock like ‘%H’,
          but padded with spaces (range ‘ 0’ through ‘23’).

          This format is a GNU extension.

     ‘%l’
          The hour as a decimal number, using a 12-hour clock like ‘%I’,
          but padded with spaces (range ‘ 1’ through ‘12’).

          This format is a GNU extension.

     ‘%m’
          The month as a decimal number (range ‘01’ through ‘12’).

     ‘%M’
          The minute as a decimal number (range ‘00’ through ‘59’).

     ‘%n’
          A single ‘\n’ (newline) character.

          This format was first standardized by POSIX.2-1992 and by
          ISO C99.

     ‘%p’
          Either ‘AM’ or ‘PM’, according to the given time value; or the
          corresponding strings for the current locale.  Noon is treated
          as ‘PM’ and midnight as ‘AM’.  In most locales ‘AM’/‘PM’
          format is not supported, in such cases ‘"%p"’ yields an empty
          string.

     ‘%P’
          Either ‘am’ or ‘pm’, according to the given time value; or the
          corresponding strings for the current locale, printed in
          lowercase characters.  Noon is treated as ‘pm’ and midnight as
          ‘am’.  In most locales ‘AM’/‘PM’ format is not supported, in
          such cases ‘"%P"’ yields an empty string.

          This format is a GNU extension.

     ‘%r’
          The complete calendar time using the AM/PM format of the
          current locale.

          This format was first standardized by POSIX.2-1992 and by
          ISO C99.  In the POSIX locale, this format is equivalent to
          ‘%I:%M:%S %p’.

     ‘%R’
          The hour and minute in decimal numbers using the format
          ‘%H:%M’.

          This format was first standardized by ISO C99 and by
          POSIX.1-2001 but was previously available as a GNU extension.

     ‘%s’
          The number of seconds since the epoch, i.e., since 1970-01-01
          00:00:00 UTC. Leap seconds are not counted unless leap second
          support is available.

          This format is a GNU extension.

     ‘%S’
          The seconds as a decimal number (range ‘00’ through ‘60’).

     ‘%t’
          A single ‘\t’ (tabulator) character.

          This format was first standardized by POSIX.2-1992 and by
          ISO C99.

     ‘%T’
          The time of day using decimal numbers using the format
          ‘%H:%M:%S’.

          This format was first standardized by POSIX.2-1992 and by
          ISO C99.

     ‘%u’
          The day of the week as a decimal number (range ‘1’ through
          ‘7’), Monday being ‘1’.

          This format was first standardized by POSIX.2-1992 and by
          ISO C99.

     ‘%U’
          The week number of the current year as a decimal number (range
          ‘00’ through ‘53’), starting with the first Sunday as the
          first day of the first week.  Days preceding the first Sunday
          in the year are considered to be in week ‘00’.

     ‘%V’
          The ISO 8601:1988 week number as a decimal number (range ‘01’
          through ‘53’).  ISO weeks start with Monday and end with
          Sunday.  Week ‘01’ of a year is the first week which has the
          majority of its days in that year; this is equivalent to the
          week containing the year’s first Thursday, and it is also
          equivalent to the week containing January 4.  Week ‘01’ of a
          year can contain days from the previous year.  The week before
          week ‘01’ of a year is the last week (‘52’ or ‘53’) of the
          previous year even if it contains days from the new year.

          This format was first standardized by POSIX.2-1992 and by
          ISO C99.

     ‘%w’
          The day of the week as a decimal number (range ‘0’ through
          ‘6’), Sunday being ‘0’.

     ‘%W’
          The week number of the current year as a decimal number (range
          ‘00’ through ‘53’), starting with the first Monday as the
          first day of the first week.  All days preceding the first
          Monday in the year are considered to be in week ‘00’.

     ‘%x’
          The preferred date representation for the current locale.

     ‘%X’
          The preferred time of day representation for the current
          locale.

     ‘%y’
          The year without a century as a decimal number (range ‘00’
          through ‘99’).  This is equivalent to the year modulo 100.

          If the ‘E’ modifier is specified (‘%Ey’), instead produces the
          year number according to a locale-specific alternative
          calendar.  Unlike ‘%y’, the number is _not_ reduced modulo
          100.  However, by default it is zero-padded to a minimum of
          two digits (this can be overridden by an explicit field width
          or by the ‘_’ and ‘-’ flags).

     ‘%Y’
          The year as a decimal number, using the Gregorian calendar.
          Years before the year ‘1’ are numbered ‘0’, ‘-1’, and so on.

          If the ‘E’ modifier is specified (‘%EY’), instead produces a
          complete representation of the year according to the locale’s
          alternative calendar.  Generally this will be some combination
          of the information produced by ‘%EC’ and ‘%Ey’.  As a GNU
          extension, the formatting flags ‘_’ or ‘-’ may be used with
          this conversion specifier; they affect how the year number is
          printed.

     ‘%z’
          RFC 822/ISO 8601:1988 style numeric time zone (e.g., ‘-0600’
          or ‘+0100’), or nothing if no time zone is determinable.

          This format was first standardized by ISO C99 and by
          POSIX.1-2001 but was previously available as a GNU extension.

          In the POSIX locale, a full RFC 822 timestamp is generated by
          the format ‘"%a, %d %b %Y %H:%M:%S %z"’ (or the equivalent
          ‘"%a, %d %b %Y %T %z"’).

     ‘%Z’
          The time zone abbreviation (empty if the time zone can’t be
          determined).

     ‘%%’
          A literal ‘%’ character.

     The SIZE parameter can be used to specify the maximum number of
     characters to be stored in the array S, including the terminating
     null character.  If the formatted time requires more than SIZE
     characters, ‘strftime’ returns zero and the contents of the array S
     are undefined.  Otherwise the return value indicates the number of
     characters placed in the array S, not including the terminating
     null character.

     _Warning:_ This convention for the return value which is prescribed
     in ISO C can lead to problems in some situations.  For certain
     format strings and certain locales the output really can be the
     empty string and this cannot be discovered by testing the return
     value only.  E.g., in most locales the AM/PM time format is not
     supported (most of the world uses the 24 hour time representation).
     In such locales ‘"%p"’ will return the empty string, i.e., the
     return value is zero.  To detect situations like this something
     similar to the following code should be used:

          buf[0] = '\1';
          len = strftime (buf, bufsize, format, tp);
          if (len == 0 && buf[0] != '\0')
            {
              /* Something went wrong in the strftime call.  */
              ...
            }

     If S is a null pointer, ‘strftime’ does not actually write
     anything, but instead returns the number of characters it would
     have written.

     Calling ‘strftime’ also sets the current time zone as if ‘tzset’
     were called; ‘strftime’ uses this information instead of
     BROKENTIME’s ‘tm_gmtoff’ and ‘tm_zone’ members.  *Note Time Zone
     Functions::.

     For an example of ‘strftime’, see *note Time Functions Example::.

 -- Function: size_t wcsftime (wchar_t *S, size_t SIZE, const wchar_t
          *TEMPLATE, const struct tm *BROKENTIME)

     Preliminary: | MT-Safe env locale | AS-Unsafe corrupt heap lock
     dlopen | AC-Unsafe corrupt lock mem fd | *Note POSIX Safety
     Concepts::.

     The ‘wcsftime’ function is equivalent to the ‘strftime’ function
     with the difference that it operates on wide character strings.
     The buffer where the result is stored, pointed to by S, must be an
     array of wide characters.  The parameter SIZE which specifies the
     size of the output buffer gives the number of wide characters, not
     the number of bytes.

     Also the format string TEMPLATE is a wide character string.  Since
     all characters needed to specify the format string are in the basic
     character set it is portably possible to write format strings in
     the C source code using the ‘L"..."’ notation.  The parameter
     BROKENTIME has the same meaning as in the ‘strftime’ call.

     The ‘wcsftime’ function supports the same flags, modifiers, and
     format specifiers as the ‘strftime’ function.

     The return value of ‘wcsftime’ is the number of wide characters
     stored in ‘s’.  When more characters would have to be written than
     can be placed in the buffer S the return value is zero, with the
     same problems indicated in the ‘strftime’ documentation.


File: libc.info,  Node: Parsing Date and Time,  Next: TZ Variable,  Prev: Formatting Calendar Time,  Up: Calendar Time

21.5.5 Convert textual time and date information back
-----------------------------------------------------

The ISO C standard does not specify any functions which can convert the
output of the ‘strftime’ function back into a binary format.  This led
to a variety of more-or-less successful implementations with different
interfaces over the years.  Then the Unix standard was extended by the
addition of two functions: ‘strptime’ and ‘getdate’.  Both have strange
interfaces but at least they are widely available.

* Menu:

* Low-Level Time String Parsing::  Interpret string according to given format.
* General Time String Parsing::    User-friendly function to parse data and
                                    time strings.


File: libc.info,  Node: Low-Level Time String Parsing,  Next: General Time String Parsing,  Up: Parsing Date and Time

21.5.5.1 Interpret string according to given format
...................................................

The first function is rather low-level.  It is nevertheless frequently
used in software since it is better known.  Its interface and
implementation are heavily influenced by the ‘getdate’ function, which
is defined and implemented in terms of calls to ‘strptime’.

 -- Function: char * strptime (const char *S, const char *FMT, struct tm
          *TP)

     Preliminary: | MT-Safe env locale | AS-Unsafe heap lock | AC-Unsafe
     lock mem fd | *Note POSIX Safety Concepts::.

     The ‘strptime’ function parses the input string S according to the
     format string FMT and stores its results in the structure TP.

     The input string could be generated by a ‘strftime’ call or
     obtained any other way.  It does not need to be in a
     human-recognizable format; e.g.  a date passed as ‘"02:1999:9"’ is
     acceptable, even though it is ambiguous without context.  As long
     as the format string FMT matches the input string the function will
     succeed.

     The user has to make sure, though, that the input can be parsed in
     a unambiguous way.  The string ‘"1999112"’ can be parsed using the
     format ‘"%Y%m%d"’ as 1999-1-12, 1999-11-2, or even 19991-1-2.  It
     is necessary to add appropriate separators to reliably get results.

     The format string consists of the same components as the format
     string of the ‘strftime’ function.  The only difference is that the
     flags ‘_’, ‘-’, ‘0’, and ‘^’ are not allowed.  Several of the
     distinct formats of ‘strftime’ do the same work in ‘strptime’ since
     differences like case of the input do not matter.  For reasons of
     symmetry all formats are supported, though.

     The modifiers ‘E’ and ‘O’ are also allowed everywhere the
     ‘strftime’ function allows them.

     The formats are:

     ‘%a’
     ‘%A’
          The weekday name according to the current locale, in
          abbreviated form or the full name.

     ‘%b’
     ‘%B’
     ‘%h’
          A month name according to the current locale.  All three
          specifiers will recognize both abbreviated and full month
          names.  If the locale provides two different grammatical forms
          of month names, all three specifiers will recognize both
          forms.

          As a GNU extension, the ‘O’ modifier can be used with these
          specifiers; it has no effect, as both grammatical forms of
          month names are recognized.

     ‘%c’
          The date and time representation for the current locale.

     ‘%Ec’
          Like ‘%c’ but the locale’s alternative date and time format is
          used.

     ‘%C’
          The century of the year.

          It makes sense to use this format only if the format string
          also contains the ‘%y’ format.

     ‘%EC’
          The locale’s representation of the period.

          Unlike ‘%C’ it sometimes makes sense to use this format since
          some cultures represent years relative to the beginning of
          eras instead of using the Gregorian years.

     ‘%d’
     ‘%e’
          The day of the month as a decimal number (range ‘1’ through
          ‘31’).  Leading zeroes are permitted but not required.

     ‘%Od’
     ‘%Oe’
          Same as ‘%d’ but using the locale’s alternative numeric
          symbols.

          Leading zeroes are permitted but not required.

     ‘%D’
          Equivalent to ‘%m/%d/%y’.

     ‘%F’
          Equivalent to ‘%Y-%m-%d’, which is the ISO 8601 date format.

          This is a GNU extension following an ISO C99 extension to
          ‘strftime’.

     ‘%g’
          The year corresponding to the ISO week number, but without the
          century (range ‘00’ through ‘99’).

          _Note:_ Currently, this is not fully implemented.  The format
          is recognized, input is consumed but no field in TM is set.

          This format is a GNU extension following a GNU extension of
          ‘strftime’.

     ‘%G’
          The year corresponding to the ISO week number.

          _Note:_ Currently, this is not fully implemented.  The format
          is recognized, input is consumed but no field in TM is set.

          This format is a GNU extension following a GNU extension of
          ‘strftime’.

     ‘%H’
     ‘%k’
          The hour as a decimal number, using a 24-hour clock (range
          ‘00’ through ‘23’).

          ‘%k’ is a GNU extension following a GNU extension of
          ‘strftime’.

     ‘%OH’
          Same as ‘%H’ but using the locale’s alternative numeric
          symbols.

     ‘%I’
     ‘%l’
          The hour as a decimal number, using a 12-hour clock (range
          ‘01’ through ‘12’).

          ‘%l’ is a GNU extension following a GNU extension of
          ‘strftime’.

     ‘%OI’
          Same as ‘%I’ but using the locale’s alternative numeric
          symbols.

     ‘%j’
          The day of the year as a decimal number (range ‘1’ through
          ‘366’).

          Leading zeroes are permitted but not required.

     ‘%m’
          The month as a decimal number (range ‘1’ through ‘12’).

          Leading zeroes are permitted but not required.

     ‘%Om’
          Same as ‘%m’ but using the locale’s alternative numeric
          symbols.

     ‘%M’
          The minute as a decimal number (range ‘0’ through ‘59’).

          Leading zeroes are permitted but not required.

     ‘%OM’
          Same as ‘%M’ but using the locale’s alternative numeric
          symbols.

     ‘%n’
     ‘%t’
          Matches any white space.

     ‘%p’
     ‘%P’
          The locale-dependent equivalent to ‘AM’ or ‘PM’.

          This format is not useful unless ‘%I’ or ‘%l’ is also used.
          Another complication is that the locale might not define these
          values at all and therefore the conversion fails.

          ‘%P’ is a GNU extension following a GNU extension to
          ‘strftime’.

     ‘%r’
          The complete time using the AM/PM format of the current
          locale.

          A complication is that the locale might not define this format
          at all and therefore the conversion fails.

     ‘%R’
          The hour and minute in decimal numbers using the format
          ‘%H:%M’.

          ‘%R’ is a GNU extension following a GNU extension to
          ‘strftime’.

     ‘%s’
          The number of seconds since the epoch, i.e., since 1970-01-01
          00:00:00 UTC. Leap seconds are not counted unless leap second
          support is available.

          ‘%s’ is a GNU extension following a GNU extension to
          ‘strftime’.

     ‘%S’
          The seconds as a decimal number (range ‘0’ through ‘60’).

          Leading zeroes are permitted but not required.

          *NB:* The Unix specification says the upper bound on this
          value is ‘61’, a result of a decision to allow double leap
          seconds.  You will not see the value ‘61’ because no minute
          has more than one leap second, but the myth persists.

     ‘%OS’
          Same as ‘%S’ but using the locale’s alternative numeric
          symbols.

     ‘%T’
          Equivalent to the use of ‘%H:%M:%S’ in this place.

     ‘%u’
          The day of the week as a decimal number (range ‘1’ through
          ‘7’), Monday being ‘1’.

          Leading zeroes are permitted but not required.

          _Note:_ Currently, this is not fully implemented.  The format
          is recognized, input is consumed but no field in TM is set.

     ‘%U’
          The week number of the current year as a decimal number (range
          ‘0’ through ‘53’).

          Leading zeroes are permitted but not required.

     ‘%OU’
          Same as ‘%U’ but using the locale’s alternative numeric
          symbols.

     ‘%V’
          The ISO 8601:1988 week number as a decimal number (range ‘1’
          through ‘53’).

          Leading zeroes are permitted but not required.

          _Note:_ Currently, this is not fully implemented.  The format
          is recognized, input is consumed but no field in TM is set.

     ‘%w’
          The day of the week as a decimal number (range ‘0’ through
          ‘6’), Sunday being ‘0’.

          Leading zeroes are permitted but not required.

          _Note:_ Currently, this is not fully implemented.  The format
          is recognized, input is consumed but no field in TM is set.

     ‘%Ow’
          Same as ‘%w’ but using the locale’s alternative numeric
          symbols.

     ‘%W’
          The week number of the current year as a decimal number (range
          ‘0’ through ‘53’).

          Leading zeroes are permitted but not required.

          _Note:_ Currently, this is not fully implemented.  The format
          is recognized, input is consumed but no field in TM is set.

     ‘%OW’
          Same as ‘%W’ but using the locale’s alternative numeric
          symbols.

     ‘%x’
          The date using the locale’s date format.

     ‘%Ex’
          Like ‘%x’ but the locale’s alternative data representation is
          used.

     ‘%X’
          The time using the locale’s time format.

     ‘%EX’
          Like ‘%X’ but the locale’s alternative time representation is
          used.

     ‘%y’
          The year without a century as a decimal number (range ‘0’
          through ‘99’).

          Leading zeroes are permitted but not required.

          Note that it is questionable to use this format without the
          ‘%C’ format.  The ‘strptime’ function does regard input values
          in the range 68 to 99 as the years 1969 to 1999 and the values
          0 to 68 as the years 2000 to 2068.  But maybe this heuristic
          fails for some input data.

          Therefore it is best to avoid ‘%y’ completely and use ‘%Y’
          instead.

     ‘%Ey’
          The offset from ‘%EC’ in the locale’s alternative
          representation.

     ‘%Oy’
          The offset of the year (from ‘%C’) using the locale’s
          alternative numeric symbols.

     ‘%Y’
          The year as a decimal number, using the Gregorian calendar.

     ‘%EY’
          The full alternative year representation.

     ‘%z’
          The offset from GMT in ISO 8601/RFC822 format.

     ‘%Z’
          The timezone name.

          _Note:_ Currently, this is not fully implemented.  The format
          is recognized, input is consumed but no field in TM is set.

     ‘%%’
          A literal ‘%’ character.

     All other characters in the format string must have a matching
     character in the input string.  Exceptions are white spaces in the
     input string which can match zero or more whitespace characters in
     the format string.

     *Portability Note:* The XPG standard advises applications to use at
     least one whitespace character (as specified by ‘isspace’) or other
     non-alphanumeric characters between any two conversion
     specifications.  The GNU C Library does not have this limitation
     but other libraries might have trouble parsing formats like
     ‘"%d%m%Y%H%M%S"’.

     The ‘strptime’ function processes the input string from right to
     left.  Each of the three possible input elements (white space,
     literal, or format) are handled one after the other.  If the input
     cannot be matched to the format string the function stops.  The
     remainder of the format and input strings are not processed.

     The function returns a pointer to the first character it was unable
     to process.  If the input string contains more characters than
     required by the format string the return value points right after
     the last consumed input character.  If the whole input string is
     consumed the return value points to the ‘NULL’ byte at the end of
     the string.  If an error occurs, i.e., ‘strptime’ fails to match
     all of the format string, the function returns ‘NULL’.

   The specification of the function in the XPG standard is rather
vague, leaving out a few important pieces of information.  Most
importantly, it does not specify what happens to those elements of TM
which are not directly initialized by the different formats.  The
implementations on different Unix systems vary here.

   The GNU C Library implementation does not touch those fields which
are not directly initialized.  Exceptions are the ‘tm_wday’ and
‘tm_yday’ elements, which are recomputed if any of the year, month, or
date elements changed.  This has two implications:

   • Before calling the ‘strptime’ function for a new input string, you
     should prepare the TM structure you pass.  Normally this will mean
     initializing all values to zero.  Alternatively, you can set all
     fields to values like ‘INT_MAX’, allowing you to determine which
     elements were set by the function call.  Zero does not work here
     since it is a valid value for many of the fields.

     Careful initialization is necessary if you want to find out whether
     a certain field in TM was initialized by the function call.

   • You can construct a ‘struct tm’ value with several consecutive
     ‘strptime’ calls.  A useful application of this is e.g.  the
     parsing of two separate strings, one containing date information
     and the other time information.  By parsing one after the other
     without clearing the structure in-between, you can construct a
     complete broken-down time.

   The following example shows a function which parses a string which
contains the date information in either US style or ISO 8601 form:

     const char *
     parse_date (const char *input, struct tm *tm)
     {
       const char *cp;

       /* First clear the result structure.  */
       memset (tm, '\0', sizeof (*tm));

       /* Try the ISO format first.  */
       cp = strptime (input, "%F", tm);
       if (cp == NULL)
         {
           /* Does not match.  Try the US form.  */
           cp = strptime (input, "%D", tm);
         }

       return cp;
     }


File: libc.info,  Node: General Time String Parsing,  Prev: Low-Level Time String Parsing,  Up: Parsing Date and Time

21.5.5.2 A More User-friendly Way to Parse Times and Dates
..........................................................

The Unix standard defines another function for parsing date strings.
The interface is weird, but if the function happens to suit your
application it is just fine.  It is problematic to use this function in
multi-threaded programs or libraries, since it returns a pointer to a
static variable, and uses a global variable and global state (an
environment variable).

 -- Variable: getdate_err

     This variable of type ‘int’ contains the error code of the last
     unsuccessful call to ‘getdate’.  Defined values are:

     1
          The environment variable ‘DATEMSK’ is not defined or null.
     2
          The template file denoted by the ‘DATEMSK’ environment
          variable cannot be opened.
     3
          Information about the template file cannot retrieved.
     4
          The template file is not a regular file.
     5
          An I/O error occurred while reading the template file.
     6
          Not enough memory available to execute the function.
     7
          The template file contains no matching template.
     8
          The input date is invalid, but would match a template
          otherwise.  This includes dates like February 31st, and dates
          which cannot be represented in a ‘time_t’ variable.

 -- Function: struct tm * getdate (const char *STRING)

     Preliminary: | MT-Unsafe race:getdate env locale | AS-Unsafe heap
     lock | AC-Unsafe lock mem fd | *Note POSIX Safety Concepts::.

     The interface to ‘getdate’ is the simplest possible for a function
     to parse a string and return the value.  STRING is the input string
     and the result is returned in a statically-allocated variable.

     The details about how the string is processed are hidden from the
     user.  In fact, they can be outside the control of the program.
     Which formats are recognized is controlled by the file named by the
     environment variable ‘DATEMSK’.  This file should contain lines of
     valid format strings which could be passed to ‘strptime’.

     The ‘getdate’ function reads these format strings one after the
     other and tries to match the input string.  The first line which
     completely matches the input string is used.

     Elements not initialized through the format string retain the
     values present at the time of the ‘getdate’ function call.

     The formats recognized by ‘getdate’ are the same as for ‘strptime’.
     See above for an explanation.  There are only a few extensions to
     the ‘strptime’ behavior:

        • If the ‘%Z’ format is given the broken-down time is based on
          the current time of the timezone matched, not of the current
          timezone of the runtime environment.

          _Note_: This is not implemented (currently).  The problem is
          that timezone names are not unique.  If a fixed timezone is
          assumed for a given string (say ‘EST’ meaning US East Coast
          time), then uses for countries other than the USA will fail.
          So far we have found no good solution to this.

        • If only the weekday is specified the selected day depends on
          the current date.  If the current weekday is greater than or
          equal to the ‘tm_wday’ value the current week’s day is chosen,
          otherwise the day next week is chosen.

        • A similar heuristic is used when only the month is given and
          not the year.  If the month is greater than or equal to the
          current month, then the current year is used.  Otherwise it
          wraps to next year.  The first day of the month is assumed if
          one is not explicitly specified.

        • The current hour, minute, and second are used if the
          appropriate value is not set through the format.

        • If no date is given tomorrow’s date is used if the time is
          smaller than the current time.  Otherwise today’s date is
          taken.

     It should be noted that the format in the template file need not
     only contain format elements.  The following is a list of possible
     format strings (taken from the Unix standard):

          %m
          %A %B %d, %Y %H:%M:%S
          %A
          %B
          %m/%d/%y %I %p
          %d,%m,%Y %H:%M
          at %A the %dst of %B in %Y
          run job at %I %p,%B %dnd
          %A den %d. %B %Y %H.%M Uhr

     As you can see, the template list can contain very specific strings
     like ‘run job at %I %p,%B %dnd’.  Using the above list of templates
     and assuming the current time is Mon Sep 22 12:19:47 EDT 1986, we
     can obtain the following results for the given input.

     Input          Match        Result
     Mon            %a           Mon Sep 22 12:19:47 EDT 1986
     Sun            %a           Sun Sep 28 12:19:47 EDT 1986
     Fri            %a           Fri Sep 26 12:19:47 EDT 1986
     September      %B           Mon Sep 1 12:19:47 EDT 1986
     January        %B           Thu Jan 1 12:19:47 EST 1987
     December       %B           Mon Dec 1 12:19:47 EST 1986
     Sep Mon        %b %a        Mon Sep 1 12:19:47 EDT 1986
     Jan Fri        %b %a        Fri Jan 2 12:19:47 EST 1987
     Dec Mon        %b %a        Mon Dec 1 12:19:47 EST 1986
     Jan Wed 1989   %b %a %Y     Wed Jan 4 12:19:47 EST 1989
     Fri 9          %a %H        Fri Sep 26 09:00:00 EDT 1986
     Feb 10:30      %b %H:%S     Sun Feb 1 10:00:30 EST 1987
     10:30          %H:%M        Tue Sep 23 10:30:00 EDT 1986
     13:30          %H:%M        Mon Sep 22 13:30:00 EDT 1986

     The return value of the function is a pointer to a static variable
     of type ‘struct tm’, or a null pointer if an error occurred.  The
     result is only valid until the next ‘getdate’ call, making this
     function unusable in multi-threaded applications.

     The ‘errno’ variable is _not_ changed.  Error conditions are stored
     in the global variable ‘getdate_err’.  See the description above
     for a list of the possible error values.

     _Warning:_ The ‘getdate’ function should _never_ be used in
     SUID-programs.  The reason is obvious: using the ‘DATEMSK’
     environment variable you can get the function to open any arbitrary
     file and chances are high that with some bogus input (such as a
     binary file) the program will crash.

 -- Function: int getdate_r (const char *STRING, struct tm *TP)

     Preliminary: | MT-Safe env locale | AS-Unsafe heap lock | AC-Unsafe
     lock mem fd | *Note POSIX Safety Concepts::.

     The ‘getdate_r’ function is the reentrant counterpart of ‘getdate’.
     It does not use the global variable ‘getdate_err’ to signal an
     error, but instead returns an error code.  The same error codes as
     described in the ‘getdate_err’ documentation above are used, with 0
     meaning success.

     Moreover, ‘getdate_r’ stores the broken-down time in the variable
     of type ‘struct tm’ pointed to by the second argument, rather than
     in a static variable.

     This function is not defined in the Unix standard.  Nevertheless it
     is available on some other Unix systems as well.

     The warning against using ‘getdate’ in SUID-programs applies to
     ‘getdate_r’ as well.


File: libc.info,  Node: TZ Variable,  Next: Time Zone Functions,  Prev: Parsing Date and Time,  Up: Calendar Time

21.5.6 Specifying the Time Zone with ‘TZ’
-----------------------------------------

In POSIX systems, a user can specify the time zone by means of the ‘TZ’
environment variable.  For information about how to set environment
variables, see *note Environment Variables::.  The functions for
accessing the time zone are declared in ‘time.h’.

   You should not normally need to set ‘TZ’.  If the system is
configured properly, the default time zone will be correct.  You might
set ‘TZ’ if you are using a computer over a network from a different
time zone, and would like times reported to you in the time zone local
to you, rather than what is local to the computer.

   In POSIX.1 systems the value of the ‘TZ’ variable can be in one of
three formats.  With the GNU C Library, the most common format is the
last one, which can specify a selection from a large database of time
zone information for many regions of the world.  The first two formats
are used to describe the time zone information directly, which is both
more cumbersome and less precise.  But the POSIX.1 standard only
specifies the details of the first two formats, so it is good to be
familiar with them in case you come across a POSIX.1 system that doesn’t
support a time zone information database.

   The first format is used when there is no Daylight Saving Time (or
summer time) in the local time zone:

     STD OFFSET

   The STD string specifies the name of the time zone.  It must be three
or more characters long and must not contain a leading colon, embedded
digits, commas, nor plus and minus signs.  There is no space character
separating the time zone name from the OFFSET, so these restrictions are
necessary to parse the specification correctly.

   The OFFSET specifies the time value you must add to the local time to
get a Coordinated Universal Time value.  It has syntax like
[‘+’|‘-’]HH[‘:’MM[‘:’SS]].  This is positive if the local time zone is
west of the Prime Meridian and negative if it is east.  The hour must be
between ‘0’ and ‘24’, and the minute and seconds between ‘0’ and ‘59’.

   For example, here is how we would specify Eastern Standard Time, but
without any Daylight Saving Time alternative:

     EST+5

   The second format is used when there is Daylight Saving Time:

     STD OFFSET DST [OFFSET]‘,’START[‘/’TIME]‘,’END[‘/’TIME]

   The initial STD and OFFSET specify the standard time zone, as
described above.  The DST string and OFFSET specify the name and offset
for the corresponding Daylight Saving Time zone; if the OFFSET is
omitted, it defaults to one hour ahead of standard time.

   The remainder of the specification describes when Daylight Saving
Time is in effect.  The START field is when Daylight Saving Time goes
into effect and the END field is when the change is made back to
standard time.  The following formats are recognized for these fields:

‘JN’
     This specifies the Julian day, with N between ‘1’ and ‘365’.
     February 29 is never counted, even in leap years.

‘N’
     This specifies the Julian day, with N between ‘0’ and ‘365’.
     February 29 is counted in leap years.

‘MM.W.D’
     This specifies day D of week W of month M.  The day D must be
     between ‘0’ (Sunday) and ‘6’.  The week W must be between ‘1’ and
     ‘5’; week ‘1’ is the first week in which day D occurs, and week ‘5’
     specifies the _last_ D day in the month.  The month M should be
     between ‘1’ and ‘12’.

   The TIME fields specify when, in the local time currently in effect,
the change to the other time occurs.  If omitted, the default is
‘02:00:00’.  The hours part of the time fields can range from −167
through 167; this is an extension to POSIX.1, which allows only the
range 0 through 24.

   Here are some example ‘TZ’ values, including the appropriate Daylight
Saving Time and its dates of applicability.  In North American Eastern
Standard Time (EST) and Eastern Daylight Time (EDT), the normal offset
from UTC is 5 hours; since this is west of the prime meridian, the sign
is positive.  Summer time begins on March’s second Sunday at 2:00am, and
ends on November’s first Sunday at 2:00am.

     EST+5EDT,M3.2.0/2,M11.1.0/2

   Israel Standard Time (IST) and Israel Daylight Time (IDT) are 2 hours
ahead of the prime meridian in winter, springing forward an hour on
March’s fourth Thursday at 26:00 (i.e., 02:00 on the first Friday on or
after March 23), and falling back on October’s last Sunday at 02:00.

     IST-2IDT,M3.4.4/26,M10.5.0

   Western Argentina Summer Time (WARST) is 3 hours behind the prime
meridian all year.  There is a dummy fall-back transition on December 31
at 25:00 daylight saving time (i.e., 24:00 standard time, equivalent to
January 1 at 00:00 standard time), and a simultaneous spring-forward
transition on January 1 at 00:00 standard time, so daylight saving time
is in effect all year and the initial ‘WART’ is a placeholder.

     WART4WARST,J1/0,J365/25

   Western Greenland Time (WGT) and Western Greenland Summer Time (WGST)
are 3 hours behind UTC in the winter.  Its clocks follow the European
Union rules of springing forward by one hour on March’s last Sunday at
01:00 UTC (−02:00 local time) and falling back on October’s last Sunday
at 01:00 UTC (−01:00 local time).

     WGT3WGST,M3.5.0/-2,M10.5.0/-1

   The schedule of Daylight Saving Time in any particular jurisdiction
has changed over the years.  To be strictly correct, the conversion of
dates and times in the past should be based on the schedule that was in
effect then.  However, this format has no facilities to let you specify
how the schedule has changed from year to year.  The most you can do is
specify one particular schedule—usually the present day schedule—and
this is used to convert any date, no matter when.  For precise time zone
specifications, it is best to use the time zone information database
(see below).

   The third format looks like this:

     :CHARACTERS

   Each operating system interprets this format differently; in the GNU
C Library, CHARACTERS is the name of a file which describes the time
zone.

   If the ‘TZ’ environment variable does not have a value, the operation
chooses a time zone by default.  In the GNU C Library, the default time
zone is like the specification ‘TZ=:/etc/localtime’ (or
‘TZ=:/usr/local/etc/localtime’, depending on how the GNU C Library was
configured; *note Installation::).  Other C libraries use their own rule
for choosing the default time zone, so there is little we can say about
them.

   If CHARACTERS begins with a slash, it is an absolute file name;
otherwise the library looks for the file
‘/usr/share/zoneinfo/CHARACTERS’.  The ‘zoneinfo’ directory contains
data files describing local time zones in many different parts of the
world.  The names represent major cities, with subdirectories for
geographical areas; for example, ‘America/New_York’, ‘Europe/London’,
‘Asia/Hong_Kong’.  These data files are installed by the system
administrator, who also sets ‘/etc/localtime’ to point to the data file
for the local time zone.  The files typically come from the Time Zone
Database (http://www.iana.org/time-zones) of time zone and daylight
saving time information for most regions of the world, which is
maintained by a community of volunteers and put in the public domain.


File: libc.info,  Node: Time Zone Functions,  Next: Time Functions Example,  Prev: TZ Variable,  Up: Calendar Time

21.5.7 Functions and Variables for Time Zones
---------------------------------------------

 -- Variable: char * tzname [2]

     The array ‘tzname’ contains two strings, which are the standard
     names of the pair of time zones (standard and Daylight Saving) that
     the user has selected.  ‘tzname[0]’ is the name of the standard
     time zone (for example, ‘"EST"’), and ‘tzname[1]’ is the name for
     the time zone when Daylight Saving Time is in use (for example,
     ‘"EDT"’).  These correspond to the STD and DST strings
     (respectively) from the ‘TZ’ environment variable.  If Daylight
     Saving Time is never used, ‘tzname[1]’ is the empty string.

     The ‘tzname’ array is initialized from the ‘TZ’ environment
     variable whenever ‘tzset’, ‘ctime’, ‘strftime’, ‘mktime’, or
     ‘localtime’ is called.  If multiple abbreviations have been used
     (e.g.  ‘"EWT"’ and ‘"EDT"’ for U.S. Eastern War Time and Eastern
     Daylight Time), the array contains the most recent abbreviation.

     The ‘tzname’ array is required for POSIX.1 compatibility, but in
     GNU programs it is better to use the ‘tm_zone’ member of the
     broken-down time structure, since ‘tm_zone’ reports the correct
     abbreviation even when it is not the latest one.

     Though the strings are declared as ‘char *’ the user must refrain
     from modifying these strings.  Modifying the strings will almost
     certainly lead to trouble.

 -- Function: void tzset (void)

     Preliminary: | MT-Safe env locale | AS-Unsafe heap lock | AC-Unsafe
     lock mem fd | *Note POSIX Safety Concepts::.

     The ‘tzset’ function initializes the ‘tzname’ variable from the
     value of the ‘TZ’ environment variable.  It is not usually
     necessary for your program to call this function, because it is
     called automatically when you use the other time conversion
     functions that depend on the time zone.

   The following variables are defined for compatibility with System V
Unix.  Like ‘tzname’, these variables are set by calling ‘tzset’ or the
other time conversion functions.

 -- Variable: long int timezone

     This contains the difference between UTC and the latest local
     standard time, in seconds west of UTC. For example, in the U.S.
     Eastern time zone, the value is ‘5*60*60’.  Unlike the ‘tm_gmtoff’
     member of the broken-down time structure, this value is not
     adjusted for daylight saving, and its sign is reversed.  In GNU
     programs it is better to use ‘tm_gmtoff’, since it contains the
     correct offset even when it is not the latest one.

 -- Variable: int daylight

     This variable has a nonzero value if Daylight Saving Time rules
     apply.  A nonzero value does not necessarily mean that Daylight
     Saving Time is now in effect; it means only that Daylight Saving
     Time is sometimes in effect.


File: libc.info,  Node: Time Functions Example,  Prev: Time Zone Functions,  Up: Calendar Time

21.5.8 Time Functions Example
-----------------------------

Here is an example program showing the use of some of the calendar time
functions.


     #include <time.h>
     #include <stdio.h>

     #define SIZE 256

     int
     main (void)
     {
       char buffer[SIZE];
       time_t curtime;
       struct tm *loctime;

       /* Get the current time. */
       curtime = time (NULL);

       /* Convert it to local time representation. */
       loctime = localtime (&curtime);

       /* Print out the date and time in the standard format. */
       fputs (asctime (loctime), stdout);

       /* Print it out in a nice format. */
       strftime (buffer, SIZE, "Today is %A, %B %d.\n", loctime);
       fputs (buffer, stdout);
       strftime (buffer, SIZE, "The time is %I:%M %p.\n", loctime);
       fputs (buffer, stdout);

       return 0;
     }

   It produces output like this:

     Wed Jul 31 13:02:36 1991
     Today is Wednesday, July 31.
     The time is 01:02 PM.


File: libc.info,  Node: Setting an Alarm,  Next: Sleeping,  Prev: Calendar Time,  Up: Date and Time

21.6 Setting an Alarm
=====================

The ‘alarm’ and ‘setitimer’ functions provide a mechanism for a process
to interrupt itself in the future.  They do this by setting a timer;
when the timer expires, the process receives a signal.

   Each process has three independent interval timers available:

   • A real-time timer that counts elapsed time.  This timer sends a
     ‘SIGALRM’ signal to the process when it expires.

   • A virtual timer that counts processor time used by the process.
     This timer sends a ‘SIGVTALRM’ signal to the process when it
     expires.

   • A profiling timer that counts both processor time used by the
     process, and processor time spent in system calls on behalf of the
     process.  This timer sends a ‘SIGPROF’ signal to the process when
     it expires.

     This timer is useful for profiling in interpreters.  The interval
     timer mechanism does not have the fine granularity necessary for
     profiling native code.

   You can only have one timer of each kind set at any given time.  If
you set a timer that has not yet expired, that timer is simply reset to
the new value.

   You should establish a handler for the appropriate alarm signal using
‘signal’ or ‘sigaction’ before issuing a call to ‘setitimer’ or ‘alarm’.
Otherwise, an unusual chain of events could cause the timer to expire
before your program establishes the handler.  In this case it would be
terminated, since termination is the default action for the alarm
signals.  *Note Signal Handling::.

   To be able to use the alarm function to interrupt a system call which
might block otherwise indefinitely it is important to _not_ set the
‘SA_RESTART’ flag when registering the signal handler using ‘sigaction’.
When not using ‘sigaction’ things get even uglier: the ‘signal’ function
has fixed semantics with respect to restarts.  The BSD semantics for
this function is to set the flag.  Therefore, if ‘sigaction’ for
whatever reason cannot be used, it is necessary to use ‘sysv_signal’ and
not ‘signal’.

   The ‘setitimer’ function is the primary means for setting an alarm.
This facility is declared in the header file ‘sys/time.h’.  The ‘alarm’
function, declared in ‘unistd.h’, provides a somewhat simpler interface
for setting the real-time timer.

 -- Data Type: struct itimerval

     This structure is used to specify when a timer should expire.  It
     contains the following members:
     ‘struct timeval it_interval’
          This is the period between successive timer interrupts.  If
          zero, the alarm will only be sent once.

     ‘struct timeval it_value’
          This is the period between now and the first timer interrupt.
          If zero, the alarm is disabled.

     The ‘struct timeval’ data type is described in *note Time Types::.

 -- Function: int setitimer (int WHICH, const struct itimerval *NEW,
          struct itimerval *OLD)

     Preliminary: | MT-Safe timer | AS-Safe | AC-Safe | *Note POSIX
     Safety Concepts::.

     The ‘setitimer’ function sets the timer specified by WHICH
     according to NEW.  The WHICH argument can have a value of
     ‘ITIMER_REAL’, ‘ITIMER_VIRTUAL’, or ‘ITIMER_PROF’.

     If OLD is not a null pointer, ‘setitimer’ returns information about
     any previous unexpired timer of the same kind in the structure it
     points to.

     The return value is ‘0’ on success and ‘-1’ on failure.  The
     following ‘errno’ error conditions are defined for this function:

     ‘EINVAL’
          The timer period is too large.

 -- Function: int getitimer (int WHICH, struct itimerval *OLD)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     The ‘getitimer’ function stores information about the timer
     specified by WHICH in the structure pointed at by OLD.

     The return value and error conditions are the same as for
     ‘setitimer’.

‘ITIMER_REAL’

     This constant can be used as the WHICH argument to the ‘setitimer’
     and ‘getitimer’ functions to specify the real-time timer.

‘ITIMER_VIRTUAL’

     This constant can be used as the WHICH argument to the ‘setitimer’
     and ‘getitimer’ functions to specify the virtual timer.

‘ITIMER_PROF’

     This constant can be used as the WHICH argument to the ‘setitimer’
     and ‘getitimer’ functions to specify the profiling timer.

 -- Function: unsigned int alarm (unsigned int SECONDS)

     Preliminary: | MT-Safe timer | AS-Safe | AC-Safe | *Note POSIX
     Safety Concepts::.

     The ‘alarm’ function sets the real-time timer to expire in SECONDS
     seconds.  If you want to cancel any existing alarm, you can do this
     by calling ‘alarm’ with a SECONDS argument of zero.

     The return value indicates how many seconds remain before the
     previous alarm would have been sent.  If there was no previous
     alarm, ‘alarm’ returns zero.

   The ‘alarm’ function could be defined in terms of ‘setitimer’ like
this:

     unsigned int
     alarm (unsigned int seconds)
     {
       struct itimerval old, new;
       new.it_interval.tv_usec = 0;
       new.it_interval.tv_sec = 0;
       new.it_value.tv_usec = 0;
       new.it_value.tv_sec = (long int) seconds;
       if (setitimer (ITIMER_REAL, &new, &old) < 0)
         return 0;
       else
         return old.it_value.tv_sec;
     }

   There is an example showing the use of the ‘alarm’ function in *note
Handler Returns::.

   If you simply want your process to wait for a given number of
seconds, you should use the ‘sleep’ function.  *Note Sleeping::.

   You shouldn’t count on the signal arriving precisely when the timer
expires.  In a multiprocessing environment there is typically some
amount of delay involved.

   *Portability Note:* The ‘setitimer’ and ‘getitimer’ functions are
derived from BSD Unix, while the ‘alarm’ function is specified by the
POSIX.1 standard.  ‘setitimer’ is more powerful than ‘alarm’, but
‘alarm’ is more widely used.


File: libc.info,  Node: Sleeping,  Prev: Setting an Alarm,  Up: Date and Time

21.7 Sleeping
=============

The function ‘sleep’ gives a simple way to make the program wait for a
short interval.  If your program doesn’t use signals (except to
terminate), then you can expect ‘sleep’ to wait reliably throughout the
specified interval.  Otherwise, ‘sleep’ can return sooner if a signal
arrives; if you want to wait for a given interval regardless of signals,
use ‘select’ (*note Waiting for I/O::) and don’t specify any descriptors
to wait for.

 -- Function: unsigned int sleep (unsigned int SECONDS)

     Preliminary: | MT-Unsafe sig:SIGCHLD/linux | AS-Unsafe | AC-Unsafe
     | *Note POSIX Safety Concepts::.

     The ‘sleep’ function waits for SECONDS seconds or until a signal is
     delivered, whichever happens first.

     If ‘sleep’ returns because the requested interval is over, it
     returns a value of zero.  If it returns because of delivery of a
     signal, its return value is the remaining time in the sleep
     interval.

     The ‘sleep’ function is declared in ‘unistd.h’.

   Resist the temptation to implement a sleep for a fixed amount of time
by using the return value of ‘sleep’, when nonzero, to call ‘sleep’
again.  This will work with a certain amount of accuracy as long as
signals arrive infrequently.  But each signal can cause the eventual
wakeup time to be off by an additional second or so.  Suppose a few
signals happen to arrive in rapid succession by bad luck—there is no
limit on how much this could shorten or lengthen the wait.

   Instead, compute the calendar time at which the program should stop
waiting, and keep trying to wait until that calendar time.  This won’t
be off by more than a second.  With just a little more work, you can use
‘select’ and make the waiting period quite accurate.  (Of course, heavy
system load can cause additional unavoidable delays—unless the machine
is dedicated to one application, there is no way you can avoid this.)

   On some systems, ‘sleep’ can do strange things if your program uses
‘SIGALRM’ explicitly.  Even if ‘SIGALRM’ signals are being ignored or
blocked when ‘sleep’ is called, ‘sleep’ might return prematurely on
delivery of a ‘SIGALRM’ signal.  If you have established a handler for
‘SIGALRM’ signals and a ‘SIGALRM’ signal is delivered while the process
is sleeping, the action taken might be just to cause ‘sleep’ to return
instead of invoking your handler.  And, if ‘sleep’ is interrupted by
delivery of a signal whose handler requests an alarm or alters the
handling of ‘SIGALRM’, this handler and ‘sleep’ will interfere.

   On GNU systems, it is safe to use ‘sleep’ and ‘SIGALRM’ in the same
program, because ‘sleep’ does not work by means of ‘SIGALRM’.

 -- Function: int nanosleep (const struct timespec *REQUESTED_TIME,
          struct timespec *REMAINING)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     If resolution to seconds is not enough the ‘nanosleep’ function can
     be used.  As the name suggests the sleep interval can be specified
     in nanoseconds.  The actual elapsed time of the sleep interval
     might be longer since the system rounds the elapsed time you
     request up to the next integer multiple of the actual resolution
     the system can deliver.

     ‘*REQUESTED_TIME’ is the elapsed time of the interval you want to
     sleep.

     The function returns as ‘*REMAINING’ the elapsed time left in the
     interval for which you requested to sleep.  If the interval
     completed without getting interrupted by a signal, this is zero.

     ‘struct timespec’ is described in *note Time Types::.

     If the function returns because the interval is over the return
     value is zero.  If the function returns -1 the global variable
     ‘errno’ is set to the following values:

     ‘EINTR’
          The call was interrupted because a signal was delivered to the
          thread.  If the REMAINING parameter is not the null pointer
          the structure pointed to by REMAINING is updated to contain
          the remaining elapsed time.

     ‘EINVAL’
          The nanosecond value in the REQUESTED_TIME parameter contains
          an illegal value.  Either the value is negative or greater
          than or equal to 1000 million.

     This function is a cancellation point in multi-threaded programs.
     This is a problem if the thread allocates some resources (like
     memory, file descriptors, semaphores or whatever) at the time
     ‘nanosleep’ is called.  If the thread gets canceled these resources
     stay allocated until the program ends.  To avoid this calls to
     ‘nanosleep’ should be protected using cancellation handlers.

     The ‘nanosleep’ function is declared in ‘time.h’.


File: libc.info,  Node: Resource Usage And Limitation,  Next: Non-Local Exits,  Prev: Date and Time,  Up: Top

22 Resource Usage And Limitation
********************************

This chapter describes functions for examining how much of various kinds
of resources (CPU time, memory, etc.)  a process has used and getting
and setting limits on future usage.

* Menu:

* Resource Usage::		Measuring various resources used.
* Limits on Resources::		Specifying limits on resource usage.
* Priority::			Reading or setting process run priority.
* Memory Resources::            Querying memory available resources.
* Processor Resources::         Learn about the processors available.


File: libc.info,  Node: Resource Usage,  Next: Limits on Resources,  Up: Resource Usage And Limitation

22.1 Resource Usage
===================

The function ‘getrusage’ and the data type ‘struct rusage’ are used to
examine the resource usage of a process.  They are declared in
‘sys/resource.h’.

 -- Function: int getrusage (int PROCESSES, struct rusage *RUSAGE)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function reports resource usage totals for processes specified
     by PROCESSES, storing the information in ‘*RUSAGE’.

     In most systems, PROCESSES has only two valid values:

     ‘RUSAGE_SELF’

          Just the current process.

     ‘RUSAGE_CHILDREN’

          All child processes (direct and indirect) that have already
          terminated.

     The return value of ‘getrusage’ is zero for success, and ‘-1’ for
     failure.

     ‘EINVAL’
          The argument PROCESSES is not valid.

   One way of getting resource usage for a particular child process is
with the function ‘wait4’, which returns totals for a child when it
terminates.  *Note BSD Wait Functions::.

 -- Data Type: struct rusage

     This data type stores various resource usage statistics.  It has
     the following members, and possibly others:

     ‘struct timeval ru_utime’
          Time spent executing user instructions.

     ‘struct timeval ru_stime’
          Time spent in operating system code on behalf of PROCESSES.

     ‘long int ru_maxrss’
          The maximum resident set size used, in kilobytes.  That is,
          the maximum number of kilobytes of physical memory that
          PROCESSES used simultaneously.

     ‘long int ru_ixrss’
          An integral value expressed in kilobytes times ticks of
          execution, which indicates the amount of memory used by text
          that was shared with other processes.

     ‘long int ru_idrss’
          An integral value expressed the same way, which is the amount
          of unshared memory used for data.

     ‘long int ru_isrss’
          An integral value expressed the same way, which is the amount
          of unshared memory used for stack space.

     ‘long int ru_minflt’
          The number of page faults which were serviced without
          requiring any I/O.

     ‘long int ru_majflt’
          The number of page faults which were serviced by doing I/O.

     ‘long int ru_nswap’
          The number of times PROCESSES was swapped entirely out of main
          memory.

     ‘long int ru_inblock’
          The number of times the file system had to read from the disk
          on behalf of PROCESSES.

     ‘long int ru_oublock’
          The number of times the file system had to write to the disk
          on behalf of PROCESSES.

     ‘long int ru_msgsnd’
          Number of IPC messages sent.

     ‘long int ru_msgrcv’
          Number of IPC messages received.

     ‘long int ru_nsignals’
          Number of signals received.

     ‘long int ru_nvcsw’
          The number of times PROCESSES voluntarily invoked a context
          switch (usually to wait for some service).

     ‘long int ru_nivcsw’
          The number of times an involuntary context switch took place
          (because a time slice expired, or another process of higher
          priority was scheduled).


File: libc.info,  Node: Limits on Resources,  Next: Priority,  Prev: Resource Usage,  Up: Resource Usage And Limitation

22.2 Limiting Resource Usage
============================

You can specify limits for the resource usage of a process.  When the
process tries to exceed a limit, it may get a signal, or the system call
by which it tried to do so may fail, depending on the resource.  Each
process initially inherits its limit values from its parent, but it can
subsequently change them.

   There are two per-process limits associated with a resource:

“current limit”
     The current limit is the value the system will not allow usage to
     exceed.  It is also called the “soft limit” because the process
     being limited can generally raise the current limit at will.

“maximum limit”
     The maximum limit is the maximum value to which a process is
     allowed to set its current limit.  It is also called the “hard
     limit” because there is no way for a process to get around it.  A
     process may lower its own maximum limit, but only the superuser may
     increase a maximum limit.

   The symbols for use with ‘getrlimit’, ‘setrlimit’, ‘getrlimit64’, and
‘setrlimit64’ are defined in ‘sys/resource.h’.

 -- Function: int getrlimit (int RESOURCE, struct rlimit *RLP)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     Read the current and maximum limits for the resource RESOURCE and
     store them in ‘*RLP’.

     The return value is ‘0’ on success and ‘-1’ on failure.  The only
     possible ‘errno’ error condition is ‘EFAULT’.

     When the sources are compiled with ‘_FILE_OFFSET_BITS == 64’ on a
     32-bit system this function is in fact ‘getrlimit64’.  Thus, the
     LFS interface transparently replaces the old interface.

 -- Function: int getrlimit64 (int RESOURCE, struct rlimit64 *RLP)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function is similar to ‘getrlimit’ but its second parameter is
     a pointer to a variable of type ‘struct rlimit64’, which allows it
     to read values which wouldn’t fit in the member of a ‘struct
     rlimit’.

     If the sources are compiled with ‘_FILE_OFFSET_BITS == 64’ on a
     32-bit machine, this function is available under the name
     ‘getrlimit’ and so transparently replaces the old interface.

 -- Function: int setrlimit (int RESOURCE, const struct rlimit *RLP)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     Store the current and maximum limits for the resource RESOURCE in
     ‘*RLP’.

     The return value is ‘0’ on success and ‘-1’ on failure.  The
     following ‘errno’ error condition is possible:

     ‘EPERM’
             • The process tried to raise a current limit beyond the
               maximum limit.

             • The process tried to raise a maximum limit, but is not
               superuser.

     When the sources are compiled with ‘_FILE_OFFSET_BITS == 64’ on a
     32-bit system this function is in fact ‘setrlimit64’.  Thus, the
     LFS interface transparently replaces the old interface.

 -- Function: int setrlimit64 (int RESOURCE, const struct rlimit64 *RLP)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function is similar to ‘setrlimit’ but its second parameter is
     a pointer to a variable of type ‘struct rlimit64’ which allows it
     to set values which wouldn’t fit in the member of a ‘struct
     rlimit’.

     If the sources are compiled with ‘_FILE_OFFSET_BITS == 64’ on a
     32-bit machine this function is available under the name
     ‘setrlimit’ and so transparently replaces the old interface.

 -- Data Type: struct rlimit

     This structure is used with ‘getrlimit’ to receive limit values,
     and with ‘setrlimit’ to specify limit values for a particular
     process and resource.  It has two fields:

     ‘rlim_t rlim_cur’
          The current limit

     ‘rlim_t rlim_max’
          The maximum limit.

     For ‘getrlimit’, the structure is an output; it receives the
     current values.  For ‘setrlimit’, it specifies the new values.

   For the LFS functions a similar type is defined in ‘sys/resource.h’.

 -- Data Type: struct rlimit64

     This structure is analogous to the ‘rlimit’ structure above, but
     its components have wider ranges.  It has two fields:

     ‘rlim64_t rlim_cur’
          This is analogous to ‘rlimit.rlim_cur’, but with a different
          type.

     ‘rlim64_t rlim_max’
          This is analogous to ‘rlimit.rlim_max’, but with a different
          type.

   Here is a list of resources for which you can specify a limit.
Memory and file sizes are measured in bytes.

‘RLIMIT_CPU’

     The maximum amount of CPU time the process can use.  If it runs for
     longer than this, it gets a signal: ‘SIGXCPU’.  The value is
     measured in seconds.  *Note Operation Error Signals::.

‘RLIMIT_FSIZE’

     The maximum size of file the process can create.  Trying to write a
     larger file causes a signal: ‘SIGXFSZ’.  *Note Operation Error
     Signals::.

‘RLIMIT_DATA’

     The maximum size of data memory for the process.  If the process
     tries to allocate data memory beyond this amount, the allocation
     function fails.

‘RLIMIT_STACK’

     The maximum stack size for the process.  If the process tries to
     extend its stack past this size, it gets a ‘SIGSEGV’ signal.  *Note
     Program Error Signals::.

‘RLIMIT_CORE’

     The maximum size core file that this process can create.  If the
     process terminates and would dump a core file larger than this,
     then no core file is created.  So setting this limit to zero
     prevents core files from ever being created.

‘RLIMIT_RSS’

     The maximum amount of physical memory that this process should get.
     This parameter is a guide for the system’s scheduler and memory
     allocator; the system may give the process more memory when there
     is a surplus.

‘RLIMIT_MEMLOCK’

     The maximum amount of memory that can be locked into physical
     memory (so it will never be paged out).

‘RLIMIT_NPROC’

     The maximum number of processes that can be created with the same
     user ID. If you have reached the limit for your user ID, ‘fork’
     will fail with ‘EAGAIN’.  *Note Creating a Process::.

‘RLIMIT_NOFILE’
‘RLIMIT_OFILE’

     The maximum number of files that the process can open.  If it tries
     to open more files than this, its open attempt fails with ‘errno’
     ‘EMFILE’.  *Note Error Codes::.  Not all systems support this
     limit; GNU does, and 4.4 BSD does.

‘RLIMIT_AS’

     The maximum size of total memory that this process should get.  If
     the process tries to allocate more memory beyond this amount with,
     for example, ‘brk’, ‘malloc’, ‘mmap’ or ‘sbrk’, the allocation
     function fails.

‘RLIM_NLIMITS’

     The number of different resource limits.  Any valid RESOURCE
     operand must be less than ‘RLIM_NLIMITS’.

 -- Constant: rlim_t RLIM_INFINITY

     This constant stands for a value of “infinity” when supplied as the
     limit value in ‘setrlimit’.

   The following are historical functions to do some of what the
functions above do.  The functions above are better choices.

   ‘ulimit’ and the command symbols are declared in ‘ulimit.h’.

 -- Function: long int ulimit (int CMD, ...)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     ‘ulimit’ gets the current limit or sets the current and maximum
     limit for a particular resource for the calling process according
     to the command CMD.

     If you are getting a limit, the command argument is the only
     argument.  If you are setting a limit, there is a second argument:
     ‘long int’ LIMIT which is the value to which you are setting the
     limit.

     The CMD values and the operations they specify are:

     ‘GETFSIZE’
          Get the current limit on the size of a file, in units of 512
          bytes.

     ‘SETFSIZE’
          Set the current and maximum limit on the size of a file to
          LIMIT * 512 bytes.

     There are also some other CMD values that may do things on some
     systems, but they are not supported.

     Only the superuser may increase a maximum limit.

     When you successfully get a limit, the return value of ‘ulimit’ is
     that limit, which is never negative.  When you successfully set a
     limit, the return value is zero.  When the function fails, the
     return value is ‘-1’ and ‘errno’ is set according to the reason:

     ‘EPERM’
          A process tried to increase a maximum limit, but is not
          superuser.

   ‘vlimit’ and its resource symbols are declared in ‘sys/vlimit.h’.

 -- Function: int vlimit (int RESOURCE, int LIMIT)

     Preliminary: | MT-Unsafe race:setrlimit | AS-Unsafe | AC-Safe |
     *Note POSIX Safety Concepts::.

     ‘vlimit’ sets the current limit for a resource for a process.

     RESOURCE identifies the resource:

     ‘LIM_CPU’
          Maximum CPU time.  Same as ‘RLIMIT_CPU’ for ‘setrlimit’.
     ‘LIM_FSIZE’
          Maximum file size.  Same as ‘RLIMIT_FSIZE’ for ‘setrlimit’.
     ‘LIM_DATA’
          Maximum data memory.  Same as ‘RLIMIT_DATA’ for ‘setrlimit’.
     ‘LIM_STACK’
          Maximum stack size.  Same as ‘RLIMIT_STACK’ for ‘setrlimit’.
     ‘LIM_CORE’
          Maximum core file size.  Same as ‘RLIMIT_COR’ for ‘setrlimit’.
     ‘LIM_MAXRSS’
          Maximum physical memory.  Same as ‘RLIMIT_RSS’ for
          ‘setrlimit’.

     The return value is zero for success, and ‘-1’ with ‘errno’ set
     accordingly for failure:

     ‘EPERM’
          The process tried to set its current limit beyond its maximum
          limit.


File: libc.info,  Node: Priority,  Next: Memory Resources,  Prev: Limits on Resources,  Up: Resource Usage And Limitation

22.3 Process CPU Priority And Scheduling
========================================

When multiple processes simultaneously require CPU time, the system’s
scheduling policy and process CPU priorities determine which processes
get it.  This section describes how that determination is made and GNU C
Library functions to control it.

   It is common to refer to CPU scheduling simply as scheduling and a
process’ CPU priority simply as the process’ priority, with the CPU
resource being implied.  Bear in mind, though, that CPU time is not the
only resource a process uses or that processes contend for.  In some
cases, it is not even particularly important.  Giving a process a high
“priority” may have very little effect on how fast a process runs with
respect to other processes.  The priorities discussed in this section
apply only to CPU time.

   CPU scheduling is a complex issue and different systems do it in
wildly different ways.  New ideas continually develop and find their way
into the intricacies of the various systems’ scheduling algorithms.
This section discusses the general concepts, some specifics of systems
that commonly use the GNU C Library, and some standards.

   For simplicity, we talk about CPU contention as if there is only one
CPU in the system.  But all the same principles apply when a processor
has multiple CPUs, and knowing that the number of processes that can run
at any one time is equal to the number of CPUs, you can easily
extrapolate the information.

   The functions described in this section are all defined by the
POSIX.1 and POSIX.1b standards (the ‘sched...’ functions are POSIX.1b).
However, POSIX does not define any semantics for the values that these
functions get and set.  In this chapter, the semantics are based on the
Linux kernel’s implementation of the POSIX standard.  As you will see,
the Linux implementation is quite the inverse of what the authors of the
POSIX syntax had in mind.

* Menu:

* Absolute Priority::               The first tier of priority.  Posix
* Realtime Scheduling::             Scheduling among the process nobility
* Basic Scheduling Functions::      Get/set scheduling policy, priority
* Traditional Scheduling::          Scheduling among the vulgar masses
* CPU Affinity::                    Limiting execution to certain CPUs


File: libc.info,  Node: Absolute Priority,  Next: Realtime Scheduling,  Up: Priority

22.3.1 Absolute Priority
------------------------

Every process has an absolute priority, and it is represented by a
number.  The higher the number, the higher the absolute priority.

   On systems of the past, and most systems today, all processes have
absolute priority 0 and this section is irrelevant.  In that case, *Note
Traditional Scheduling::.  Absolute priorities were invented to
accommodate realtime systems, in which it is vital that certain
processes be able to respond to external events happening in real time,
which means they cannot wait around while some other process that _wants
to_, but doesn’t _need to_ run occupies the CPU.

   When two processes are in contention to use the CPU at any instant,
the one with the higher absolute priority always gets it.  This is true
even if the process with the lower priority is already using the CPU
(i.e., the scheduling is preemptive).  Of course, we’re only talking
about processes that are running or “ready to run,” which means they are
ready to execute instructions right now.  When a process blocks to wait
for something like I/O, its absolute priority is irrelevant.

   *NB:* The term “runnable” is a synonym for “ready to run.”

   When two processes are running or ready to run and both have the same
absolute priority, it’s more interesting.  In that case, who gets the
CPU is determined by the scheduling policy.  If the processes have
absolute priority 0, the traditional scheduling policy described in
*note Traditional Scheduling:: applies.  Otherwise, the policies
described in *note Realtime Scheduling:: apply.

   You normally give an absolute priority above 0 only to a process that
can be trusted not to hog the CPU. Such processes are designed to block
(or terminate) after relatively short CPU runs.

   A process begins life with the same absolute priority as its parent
process.  Functions described in *note Basic Scheduling Functions:: can
change it.

   Only a privileged process can change a process’ absolute priority to
something other than ‘0’.  Only a privileged process or the target
process’ owner can change its absolute priority at all.

   POSIX requires absolute priority values used with the realtime
scheduling policies to be consecutive with a range of at least 32.  On
Linux, they are 1 through 99.  The functions ‘sched_get_priority_max’
and ‘sched_set_priority_min’ portably tell you what the range is on a
particular system.

22.3.1.1 Using Absolute Priority
................................

One thing you must keep in mind when designing real time applications is
that having higher absolute priority than any other process doesn’t
guarantee the process can run continuously.  Two things that can wreck a
good CPU run are interrupts and page faults.

   Interrupt handlers live in that limbo between processes.  The CPU is
executing instructions, but they aren’t part of any process.  An
interrupt will stop even the highest priority process.  So you must
allow for slight delays and make sure that no device in the system has
an interrupt handler that could cause too long a delay between
instructions for your process.

   Similarly, a page fault causes what looks like a straightforward
sequence of instructions to take a long time.  The fact that other
processes get to run while the page faults in is of no consequence,
because as soon as the I/O is complete, the higher priority process will
kick them out and run again, but the wait for the I/O itself could be a
problem.  To neutralize this threat, use ‘mlock’ or ‘mlockall’.

   There are a few ramifications of the absoluteness of this priority on
a single-CPU system that you need to keep in mind when you choose to set
a priority and also when you’re working on a program that runs with high
absolute priority.  Consider a process that has higher absolute priority
than any other process in the system and due to a bug in its program, it
gets into an infinite loop.  It will never cede the CPU. You can’t run a
command to kill it because your command would need to get the CPU in
order to run.  The errant program is in complete control.  It controls
the vertical, it controls the horizontal.

   There are two ways to avoid this: 1) keep a shell running somewhere
with a higher absolute priority or 2) keep a controlling terminal
attached to the high priority process group.  All the priority in the
world won’t stop an interrupt handler from running and delivering a
signal to the process if you hit Control-C.

   Some systems use absolute priority as a means of allocating a fixed
percentage of CPU time to a process.  To do this, a super high priority
privileged process constantly monitors the process’ CPU usage and raises
its absolute priority when the process isn’t getting its entitled share
and lowers it when the process is exceeding it.

   *NB:* The absolute priority is sometimes called the “static
priority.” We don’t use that term in this manual because it misses the
most important feature of the absolute priority: its absoluteness.


File: libc.info,  Node: Realtime Scheduling,  Next: Basic Scheduling Functions,  Prev: Absolute Priority,  Up: Priority

22.3.2 Realtime Scheduling
--------------------------

Whenever two processes with the same absolute priority are ready to run,
the kernel has a decision to make, because only one can run at a time.
If the processes have absolute priority 0, the kernel makes this
decision as described in *note Traditional Scheduling::.  Otherwise, the
decision is as described in this section.

   If two processes are ready to run but have different absolute
priorities, the decision is much simpler, and is described in *note
Absolute Priority::.

   Each process has a scheduling policy.  For processes with absolute
priority other than zero, there are two available:

  1. First Come First Served
  2. Round Robin

   The most sensible case is where all the processes with a certain
absolute priority have the same scheduling policy.  We’ll discuss that
first.

   In Round Robin, processes share the CPU, each one running for a small
quantum of time (“time slice”) and then yielding to another in a
circular fashion.  Of course, only processes that are ready to run and
have the same absolute priority are in this circle.

   In First Come First Served, the process that has been waiting the
longest to run gets the CPU, and it keeps it until it voluntarily
relinquishes the CPU, runs out of things to do (blocks), or gets
preempted by a higher priority process.

   First Come First Served, along with maximal absolute priority and
careful control of interrupts and page faults, is the one to use when a
process absolutely, positively has to run at full CPU speed or not at
all.

   Judicious use of ‘sched_yield’ function invocations by processes with
First Come First Served scheduling policy forms a good compromise
between Round Robin and First Come First Served.

   To understand how scheduling works when processes of different
scheduling policies occupy the same absolute priority, you have to know
the nitty gritty details of how processes enter and exit the ready to
run list.

   In both cases, the ready to run list is organized as a true queue,
where a process gets pushed onto the tail when it becomes ready to run
and is popped off the head when the scheduler decides to run it.  Note
that ready to run and running are two mutually exclusive states.  When
the scheduler runs a process, that process is no longer ready to run and
no longer in the ready to run list.  When the process stops running, it
may go back to being ready to run again.

   The only difference between a process that is assigned the Round
Robin scheduling policy and a process that is assigned First Come First
Serve is that in the former case, the process is automatically booted
off the CPU after a certain amount of time.  When that happens, the
process goes back to being ready to run, which means it enters the queue
at the tail.  The time quantum we’re talking about is small.  Really
small.  This is not your father’s timesharing.  For example, with the
Linux kernel, the round robin time slice is a thousand times shorter
than its typical time slice for traditional scheduling.

   A process begins life with the same scheduling policy as its parent
process.  Functions described in *note Basic Scheduling Functions:: can
change it.

   Only a privileged process can set the scheduling policy of a process
that has absolute priority higher than 0.


File: libc.info,  Node: Basic Scheduling Functions,  Next: Traditional Scheduling,  Prev: Realtime Scheduling,  Up: Priority

22.3.3 Basic Scheduling Functions
---------------------------------

This section describes functions in the GNU C Library for setting the
absolute priority and scheduling policy of a process.

   *Portability Note:* On systems that have the functions in this
section, the macro _POSIX_PRIORITY_SCHEDULING is defined in
‘<unistd.h>’.

   For the case that the scheduling policy is traditional scheduling,
more functions to fine tune the scheduling are in *note Traditional
Scheduling::.

   Don’t try to make too much out of the naming and structure of these
functions.  They don’t match the concepts described in this manual
because the functions are as defined by POSIX.1b, but the implementation
on systems that use the GNU C Library is the inverse of what the POSIX
structure contemplates.  The POSIX scheme assumes that the primary
scheduling parameter is the scheduling policy and that the priority
value, if any, is a parameter of the scheduling policy.  In the
implementation, though, the priority value is king and the scheduling
policy, if anything, only fine tunes the effect of that priority.

   The symbols in this section are declared by including file ‘sched.h’.

   *Portability Note:* In POSIX, the ‘pid_t’ arguments of the functions
below refer to process IDs.  On Linux, they are actually thread IDs, and
control how specific threads are scheduled with regards to the entire
system.  The resulting behavior does not conform to POSIX. This is why
the following description refers to tasks and tasks IDs, and not
processes and process IDs.

 -- Data Type: struct sched_param

     This structure describes an absolute priority.
     ‘int sched_priority’
          absolute priority value

 -- Function: int sched_setscheduler (pid_t PID, int POLICY, const
          struct sched_param *PARAM)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function sets both the absolute priority and the scheduling
     policy for a task.

     It assigns the absolute priority value given by PARAM and the
     scheduling policy POLICY to the task with ID PID, or the calling
     task if PID is zero.  If POLICY is negative, ‘sched_setscheduler’
     keeps the existing scheduling policy.

     The following macros represent the valid values for POLICY:

     ‘SCHED_OTHER’
          Traditional Scheduling
     ‘SCHED_FIFO’
          First In First Out
     ‘SCHED_RR’
          Round Robin

     On success, the return value is ‘0’.  Otherwise, it is ‘-1’ and
     ‘ERRNO’ is set accordingly.  The ‘errno’ values specific to this
     function are:

     ‘EPERM’
             • The calling task does not have ‘CAP_SYS_NICE’ permission
               and POLICY is not ‘SCHED_OTHER’ (or it’s negative and the
               existing policy is not ‘SCHED_OTHER’.

             • The calling task does not have ‘CAP_SYS_NICE’ permission
               and its owner is not the target task’s owner.  I.e., the
               effective uid of the calling task is neither the
               effective nor the real uid of task PID.

     ‘ESRCH’
          There is no task with pid PID and PID is not zero.

     ‘EINVAL’
             • POLICY does not identify an existing scheduling policy.

             • The absolute priority value identified by *PARAM is
               outside the valid range for the scheduling policy POLICY
               (or the existing scheduling policy if POLICY is negative)
               or PARAM is null.  ‘sched_get_priority_max’ and
               ‘sched_get_priority_min’ tell you what the valid range
               is.

             • PID is negative.

 -- Function: int sched_getscheduler (pid_t PID)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function returns the scheduling policy assigned to the task
     with ID PID, or the calling task if PID is zero.

     The return value is the scheduling policy.  See
     ‘sched_setscheduler’ for the possible values.

     If the function fails, the return value is instead ‘-1’ and ‘errno’
     is set accordingly.

     The ‘errno’ values specific to this function are:

     ‘ESRCH’
          There is no task with pid PID and it is not zero.

     ‘EINVAL’
          PID is negative.

     Note that this function is not an exact mate to
     ‘sched_setscheduler’ because while that function sets the
     scheduling policy and the absolute priority, this function gets
     only the scheduling policy.  To get the absolute priority, use
     ‘sched_getparam’.

 -- Function: int sched_setparam (pid_t PID, const struct sched_param
          *PARAM)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function sets a task’s absolute priority.

     It is functionally identical to ‘sched_setscheduler’ with POLICY =
     ‘-1’.

 -- Function: int sched_getparam (pid_t PID, struct sched_param *PARAM)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function returns a task’s absolute priority.

     PID is the task ID of the task whose absolute priority you want to
     know.

     PARAM is a pointer to a structure in which the function stores the
     absolute priority of the task.

     On success, the return value is ‘0’.  Otherwise, it is ‘-1’ and
     ‘errno’ is set accordingly.  The ‘errno’ values specific to this
     function are:

     ‘ESRCH’
          There is no task with ID PID and it is not zero.

     ‘EINVAL’
          PID is negative.

 -- Function: int sched_get_priority_min (int POLICY)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function returns the lowest absolute priority value that is
     allowable for a task with scheduling policy POLICY.

     On Linux, it is 0 for SCHED_OTHER and 1 for everything else.

     On success, the return value is ‘0’.  Otherwise, it is ‘-1’ and
     ‘ERRNO’ is set accordingly.  The ‘errno’ values specific to this
     function are:

     ‘EINVAL’
          POLICY does not identify an existing scheduling policy.

 -- Function: int sched_get_priority_max (int POLICY)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function returns the highest absolute priority value that is
     allowable for a task that with scheduling policy POLICY.

     On Linux, it is 0 for SCHED_OTHER and 99 for everything else.

     On success, the return value is ‘0’.  Otherwise, it is ‘-1’ and
     ‘ERRNO’ is set accordingly.  The ‘errno’ values specific to this
     function are:

     ‘EINVAL’
          POLICY does not identify an existing scheduling policy.

 -- Function: int sched_rr_get_interval (pid_t PID, struct timespec
          *INTERVAL)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function returns the length of the quantum (time slice) used
     with the Round Robin scheduling policy, if it is used, for the task
     with task ID PID.

     It returns the length of time as INTERVAL.

     With a Linux kernel, the round robin time slice is always 150
     microseconds, and PID need not even be a real pid.

     The return value is ‘0’ on success and in the pathological case
     that it fails, the return value is ‘-1’ and ‘errno’ is set
     accordingly.  There is nothing specific that can go wrong with this
     function, so there are no specific ‘errno’ values.

 -- Function: int sched_yield (void)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function voluntarily gives up the task’s claim on the CPU.

     Technically, ‘sched_yield’ causes the calling task to be made
     immediately ready to run (as opposed to running, which is what it
     was before).  This means that if it has absolute priority higher
     than 0, it gets pushed onto the tail of the queue of tasks that
     share its absolute priority and are ready to run, and it will run
     again when its turn next arrives.  If its absolute priority is 0,
     it is more complicated, but still has the effect of yielding the
     CPU to other tasks.

     If there are no other tasks that share the calling task’s absolute
     priority, this function doesn’t have any effect.

     To the extent that the containing program is oblivious to what
     other processes in the system are doing and how fast it executes,
     this function appears as a no-op.

     The return value is ‘0’ on success and in the pathological case
     that it fails, the return value is ‘-1’ and ‘errno’ is set
     accordingly.  There is nothing specific that can go wrong with this
     function, so there are no specific ‘errno’ values.


File: libc.info,  Node: Traditional Scheduling,  Next: CPU Affinity,  Prev: Basic Scheduling Functions,  Up: Priority

22.3.4 Traditional Scheduling
-----------------------------

This section is about the scheduling among processes whose absolute
priority is 0.  When the system hands out the scraps of CPU time that
are left over after the processes with higher absolute priority have
taken all they want, the scheduling described herein determines who
among the great unwashed processes gets them.

* Menu:

* Traditional Scheduling Intro::
* Traditional Scheduling Functions::


File: libc.info,  Node: Traditional Scheduling Intro,  Next: Traditional Scheduling Functions,  Up: Traditional Scheduling

22.3.4.1 Introduction To Traditional Scheduling
...............................................

Long before there was absolute priority (See *note Absolute Priority::),
Unix systems were scheduling the CPU using this system.  When POSIX came
in like the Romans and imposed absolute priorities to accommodate the
needs of realtime processing, it left the indigenous Absolute Priority
Zero processes to govern themselves by their own familiar scheduling
policy.

   Indeed, absolute priorities higher than zero are not available on
many systems today and are not typically used when they are, being
intended mainly for computers that do realtime processing.  So this
section describes the only scheduling many programmers need to be
concerned about.

   But just to be clear about the scope of this scheduling: Any time a
process with an absolute priority of 0 and a process with an absolute
priority higher than 0 are ready to run at the same time, the one with
absolute priority 0 does not run.  If it’s already running when the
higher priority ready-to-run process comes into existence, it stops
immediately.

   In addition to its absolute priority of zero, every process has
another priority, which we will refer to as "dynamic priority" because
it changes over time.  The dynamic priority is meaningless for processes
with an absolute priority higher than zero.

   The dynamic priority sometimes determines who gets the next turn on
the CPU. Sometimes it determines how long turns last.  Sometimes it
determines whether a process can kick another off the CPU.

   In Linux, the value is a combination of these things, but mostly it
just determines the length of the time slice.  The higher a process’
dynamic priority, the longer a shot it gets on the CPU when it gets one.
If it doesn’t use up its time slice before giving up the CPU to do
something like wait for I/O, it is favored for getting the CPU back when
it’s ready for it, to finish out its time slice.  Other than that,
selection of processes for new time slices is basically round robin.
But the scheduler does throw a bone to the low priority processes: A
process’ dynamic priority rises every time it is snubbed in the
scheduling process.  In Linux, even the fat kid gets to play.

   The fluctuation of a process’ dynamic priority is regulated by
another value: The “nice” value.  The nice value is an integer, usually
in the range -20 to 20, and represents an upper limit on a process’
dynamic priority.  The higher the nice number, the lower that limit.

   On a typical Linux system, for example, a process with a nice value
of 20 can get only 10 milliseconds on the CPU at a time, whereas a
process with a nice value of -20 can achieve a high enough priority to
get 400 milliseconds.

   The idea of the nice value is deferential courtesy.  In the
beginning, in the Unix garden of Eden, all processes shared equally in
the bounty of the computer system.  But not all processes really need
the same share of CPU time, so the nice value gave a courteous process
the ability to refuse its equal share of CPU time that others might
prosper.  Hence, the higher a process’ nice value, the nicer the process
is.  (Then a snake came along and offered some process a negative nice
value and the system became the crass resource allocation system we know
today.)

   Dynamic priorities tend upward and downward with an objective of
smoothing out allocation of CPU time and giving quick response time to
infrequent requests.  But they never exceed their nice limits, so on a
heavily loaded CPU, the nice value effectively determines how fast a
process runs.

   In keeping with the socialistic heritage of Unix process priority, a
process begins life with the same nice value as its parent process and
can raise it at will.  A process can also raise the nice value of any
other process owned by the same user (or effective user).  But only a
privileged process can lower its nice value.  A privileged process can
also raise or lower another process’ nice value.

   GNU C Library functions for getting and setting nice values are
described in *Note Traditional Scheduling Functions::.


File: libc.info,  Node: Traditional Scheduling Functions,  Prev: Traditional Scheduling Intro,  Up: Traditional Scheduling

22.3.4.2 Functions For Traditional Scheduling
.............................................

This section describes how you can read and set the nice value of a
process.  All these symbols are declared in ‘sys/resource.h’.

   The function and macro names are defined by POSIX, and refer to
"priority," but the functions actually have to do with nice values, as
the terms are used both in the manual and POSIX.

   The range of valid nice values depends on the kernel, but typically
it runs from ‘-20’ to ‘20’.  A lower nice value corresponds to higher
priority for the process.  These constants describe the range of
priority values:

‘PRIO_MIN’

     The lowest valid nice value.

‘PRIO_MAX’

     The highest valid nice value.

 -- Function: int getpriority (int CLASS, int ID)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     Return the nice value of a set of processes; CLASS and ID specify
     which ones (see below).  If the processes specified do not all have
     the same nice value, this returns the lowest value that any of them
     has.

     On success, the return value is ‘0’.  Otherwise, it is ‘-1’ and
     ‘errno’ is set accordingly.  The ‘errno’ values specific to this
     function are:

     ‘ESRCH’
          The combination of CLASS and ID does not match any existing
          process.

     ‘EINVAL’
          The value of CLASS is not valid.

     If the return value is ‘-1’, it could indicate failure, or it could
     be the nice value.  The only way to make certain is to set ‘errno =
     0’ before calling ‘getpriority’, then use ‘errno != 0’ afterward as
     the criterion for failure.

 -- Function: int setpriority (int CLASS, int ID, int NICEVAL)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     Set the nice value of a set of processes to NICEVAL; CLASS and ID
     specify which ones (see below).

     The return value is ‘0’ on success, and ‘-1’ on failure.  The
     following ‘errno’ error condition are possible for this function:

     ‘ESRCH’
          The combination of CLASS and ID does not match any existing
          process.

     ‘EINVAL’
          The value of CLASS is not valid.

     ‘EPERM’
          The call would set the nice value of a process which is owned
          by a different user than the calling process (i.e., the target
          process’ real or effective uid does not match the calling
          process’ effective uid) and the calling process does not have
          ‘CAP_SYS_NICE’ permission.

     ‘EACCES’
          The call would lower the process’ nice value and the process
          does not have ‘CAP_SYS_NICE’ permission.

   The arguments CLASS and ID together specify a set of processes in
which you are interested.  These are the possible values of CLASS:

‘PRIO_PROCESS’

     One particular process.  The argument ID is a process ID (pid).

‘PRIO_PGRP’

     All the processes in a particular process group.  The argument ID
     is a process group ID (pgid).

‘PRIO_USER’

     All the processes owned by a particular user (i.e., whose real uid
     indicates the user).  The argument ID is a user ID (uid).

   If the argument ID is 0, it stands for the calling process, its
process group, or its owner (real uid), according to CLASS.

 -- Function: int nice (int INCREMENT)

     Preliminary: | MT-Unsafe race:setpriority | AS-Unsafe | AC-Safe |
     *Note POSIX Safety Concepts::.

     Increment the nice value of the calling process by INCREMENT.  The
     return value is the new nice value on success, and ‘-1’ on failure.
     In the case of failure, ‘errno’ will be set to the same values as
     for ‘setpriority’.

     Here is an equivalent definition of ‘nice’:

          int
          nice (int increment)
          {
            int result, old = getpriority (PRIO_PROCESS, 0);
            result = setpriority (PRIO_PROCESS, 0, old + increment);
            if (result != -1)
                return old + increment;
            else
                return -1;
          }


File: libc.info,  Node: CPU Affinity,  Prev: Traditional Scheduling,  Up: Priority

22.3.5 Limiting execution to certain CPUs
-----------------------------------------

On a multi-processor system the operating system usually distributes the
different processes which are runnable on all available CPUs in a way
which allows the system to work most efficiently.  Which processes and
threads run can be to some extend be control with the scheduling
functionality described in the last sections.  But which CPU finally
executes which process or thread is not covered.

   There are a number of reasons why a program might want to have
control over this aspect of the system as well:

   • One thread or process is responsible for absolutely critical work
     which under no circumstances must be interrupted or hindered from
     making progress by other processes or threads using CPU resources.
     In this case the special process would be confined to a CPU which
     no other process or thread is allowed to use.

   • The access to certain resources (RAM, I/O ports) has different
     costs from different CPUs.  This is the case in NUMA (Non-Uniform
     Memory Architecture) machines.  Preferably memory should be
     accessed locally but this requirement is usually not visible to the
     scheduler.  Therefore forcing a process or thread to the CPUs which
     have local access to the most-used memory helps to significantly
     boost the performance.

   • In controlled runtimes resource allocation and book-keeping work
     (for instance garbage collection) is performance local to
     processors.  This can help to reduce locking costs if the resources
     do not have to be protected from concurrent accesses from different
     processors.

   The POSIX standard up to this date is of not much help to solve this
problem.  The Linux kernel provides a set of interfaces to allow
specifying _affinity sets_ for a process.  The scheduler will schedule
the thread or process on CPUs specified by the affinity masks.  The
interfaces which the GNU C Library define follow to some extent the
Linux kernel interface.

 -- Data Type: cpu_set_t

     This data set is a bitset where each bit represents a CPU. How the
     system’s CPUs are mapped to bits in the bitset is system dependent.
     The data type has a fixed size; in the unlikely case that the
     number of bits are not sufficient to describe the CPUs of the
     system a different interface has to be used.

     This type is a GNU extension and is defined in ‘sched.h’.

   To manipulate the bitset, to set and reset bits, a number of macros
are defined.  Some of the macros take a CPU number as a parameter.  Here
it is important to never exceed the size of the bitset.  The following
macro specifies the number of bits in the ‘cpu_set_t’ bitset.

 -- Macro: int CPU_SETSIZE

     The value of this macro is the maximum number of CPUs which can be
     handled with a ‘cpu_set_t’ object.

   The type ‘cpu_set_t’ should be considered opaque; all manipulation
should happen via the next four macros.

 -- Macro: void CPU_ZERO (cpu_set_t *SET)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This macro initializes the CPU set SET to be the empty set.

     This macro is a GNU extension and is defined in ‘sched.h’.

 -- Macro: void CPU_SET (int CPU, cpu_set_t *SET)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This macro adds CPU to the CPU set SET.

     The CPU parameter must not have side effects since it is evaluated
     more than once.

     This macro is a GNU extension and is defined in ‘sched.h’.

 -- Macro: void CPU_CLR (int CPU, cpu_set_t *SET)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This macro removes CPU from the CPU set SET.

     The CPU parameter must not have side effects since it is evaluated
     more than once.

     This macro is a GNU extension and is defined in ‘sched.h’.

 -- Macro: int CPU_ISSET (int CPU, const cpu_set_t *SET)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This macro returns a nonzero value (true) if CPU is a member of the
     CPU set SET, and zero (false) otherwise.

     The CPU parameter must not have side effects since it is evaluated
     more than once.

     This macro is a GNU extension and is defined in ‘sched.h’.

   CPU bitsets can be constructed from scratch or the currently
installed affinity mask can be retrieved from the system.

 -- Function: int sched_getaffinity (pid_t PID, size_t CPUSETSIZE,
          cpu_set_t *CPUSET)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function stores the CPU affinity mask for the process or
     thread with the ID PID in the CPUSETSIZE bytes long bitmap pointed
     to by CPUSET.  If successful, the function always initializes all
     bits in the ‘cpu_set_t’ object and returns zero.

     If PID does not correspond to a process or thread on the system the
     or the function fails for some other reason, it returns ‘-1’ and
     ‘errno’ is set to represent the error condition.

     ‘ESRCH’
          No process or thread with the given ID found.

     ‘EFAULT’
          The pointer CPUSET does not point to a valid object.

     This function is a GNU extension and is declared in ‘sched.h’.

   Note that it is not portably possible to use this information to
retrieve the information for different POSIX threads.  A separate
interface must be provided for that.

 -- Function: int sched_setaffinity (pid_t PID, size_t CPUSETSIZE, const
          cpu_set_t *CPUSET)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     This function installs the CPUSETSIZE bytes long affinity mask
     pointed to by CPUSET for the process or thread with the ID PID.  If
     successful the function returns zero and the scheduler will in the
     future take the affinity information into account.

     If the function fails it will return ‘-1’ and ‘errno’ is set to the
     error code:

     ‘ESRCH’
          No process or thread with the given ID found.

     ‘EFAULT’
          The pointer CPUSET does not point to a valid object.

     ‘EINVAL’
          The bitset is not valid.  This might mean that the affinity
          set might not leave a processor for the process or thread to
          run on.

     This function is a GNU extension and is declared in ‘sched.h’.

 -- Function: int getcpu (unsigned int *cpu, unsigned int *node)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     The ‘getcpu’ function identifies the processor and node on which
     the calling thread or process is currently running and writes them
     into the integers pointed to by the CPU and NODE arguments.  The
     processor is a unique nonnegative integer identifying a CPU. The
     node is a unique nonnegative integer identifying a NUMA node.  When
     either CPU or NODE is ‘NULL’, nothing is written to the respective
     pointer.

     The return value is ‘0’ on success and ‘-1’ on failure.  The
     following ‘errno’ error condition is defined for this function:

     ‘ENOSYS’
          The operating system does not support this function.

     This function is Linux-specific and is declared in ‘sched.h’.


File: libc.info,  Node: Memory Resources,  Next: Processor Resources,  Prev: Priority,  Up: Resource Usage And Limitation

22.4 Querying memory available resources
========================================

The amount of memory available in the system and the way it is organized
determines oftentimes the way programs can and have to work.  For
functions like ‘mmap’ it is necessary to know about the size of
individual memory pages and knowing how much memory is available enables
a program to select appropriate sizes for, say, caches.  Before we get
into these details a few words about memory subsystems in traditional
Unix systems will be given.

* Menu:

* Memory Subsystem::           Overview about traditional Unix memory handling.
* Query Memory Parameters::    How to get information about the memory
                                subsystem?


File: libc.info,  Node: Memory Subsystem,  Next: Query Memory Parameters,  Up: Memory Resources

22.4.1 Overview about traditional Unix memory handling
------------------------------------------------------

Unix systems normally provide processes virtual address spaces.  This
means that the addresses of the memory regions do not have to correspond
directly to the addresses of the actual physical memory which stores the
data.  An extra level of indirection is introduced which translates
virtual addresses into physical addresses.  This is normally done by the
hardware of the processor.

   Using a virtual address space has several advantages.  The most
important is process isolation.  The different processes running on the
system cannot interfere directly with each other.  No process can write
into the address space of another process (except when shared memory is
used but then it is wanted and controlled).

   Another advantage of virtual memory is that the address space the
processes see can actually be larger than the physical memory available.
The physical memory can be extended by storage on an external media
where the content of currently unused memory regions is stored.  The
address translation can then intercept accesses to these memory regions
and make memory content available again by loading the data back into
memory.  This concept makes it necessary that programs which have to use
lots of memory know the difference between available virtual address
space and available physical memory.  If the working set of virtual
memory of all the processes is larger than the available physical memory
the system will slow down dramatically due to constant swapping of
memory content from the memory to the storage media and back.  This is
called “thrashing”.

   A final aspect of virtual memory which is important and follows from
what is said in the last paragraph is the granularity of the virtual
address space handling.  When we said that the virtual address handling
stores memory content externally it cannot do this on a byte-by-byte
basis.  The administrative overhead does not allow this (leaving alone
the processor hardware).  Instead several thousand bytes are handled
together and form a “page”.  The size of each page is always a power of
two bytes.  The smallest page size in use today is 4096, with 8192,
16384, and 65536 being other popular sizes.


File: libc.info,  Node: Query Memory Parameters,  Prev: Memory Subsystem,  Up: Memory Resources

22.4.2 How to get information about the memory subsystem?
---------------------------------------------------------

The page size of the virtual memory the process sees is essential to
know in several situations.  Some programming interfaces (e.g., ‘mmap’,
*note Memory-mapped I/O::) require the user to provide information
adjusted to the page size.  In the case of ‘mmap’ it is necessary to
provide a length argument which is a multiple of the page size.  Another
place where the knowledge about the page size is useful is in memory
allocation.  If one allocates pieces of memory in larger chunks which
are then subdivided by the application code it is useful to adjust the
size of the larger blocks to the page size.  If the total memory
requirement for the block is close (but not larger) to a multiple of the
page size the kernel’s memory handling can work more effectively since
it only has to allocate memory pages which are fully used.  (To do this
optimization it is necessary to know a bit about the memory allocator
which will require a bit of memory itself for each block and this
overhead must not push the total size over the page size multiple.)

   The page size traditionally was a compile time constant.  But recent
development of processors changed this.  Processors now support
different page sizes and they can possibly even vary among different
processes on the same system.  Therefore the system should be queried at
runtime about the current page size and no assumptions (except about it
being a power of two) should be made.

   The correct interface to query about the page size is ‘sysconf’
(*note Sysconf Definition::) with the parameter ‘_SC_PAGESIZE’.  There
is a much older interface available, too.

 -- Function: int getpagesize (void)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     The ‘getpagesize’ function returns the page size of the process.
     This value is fixed for the runtime of the process but can vary in
     different runs of the application.

     The function is declared in ‘unistd.h’.

   Widely available on System V derived systems is a method to get
information about the physical memory the system has.  The call

       sysconf (_SC_PHYS_PAGES)

returns the total number of pages of physical memory the system has.
This does not mean all this memory is available.  This information can
be found using

       sysconf (_SC_AVPHYS_PAGES)

   These two values help to optimize applications.  The value returned
for ‘_SC_AVPHYS_PAGES’ is the amount of memory the application can use
without hindering any other process (given that no other process
increases its memory usage).  The value returned for ‘_SC_PHYS_PAGES’ is
more or less a hard limit for the working set.  If all applications
together constantly use more than that amount of memory the system is in
trouble.

   The GNU C Library provides in addition to these already described way
to get this information two functions.  They are declared in the file
‘sys/sysinfo.h’.  Programmers should prefer to use the ‘sysconf’ method
described above.

 -- Function: long int get_phys_pages (void)

     Preliminary: | MT-Safe | AS-Unsafe heap lock | AC-Unsafe lock fd
     mem | *Note POSIX Safety Concepts::.

     The ‘get_phys_pages’ function returns the total number of pages of
     physical memory the system has.  To get the amount of memory this
     number has to be multiplied by the page size.

     This function is a GNU extension.

 -- Function: long int get_avphys_pages (void)

     Preliminary: | MT-Safe | AS-Unsafe heap lock | AC-Unsafe lock fd
     mem | *Note POSIX Safety Concepts::.

     The ‘get_avphys_pages’ function returns the number of available
     pages of physical memory the system has.  To get the amount of
     memory this number has to be multiplied by the page size.

     This function is a GNU extension.


File: libc.info,  Node: Processor Resources,  Prev: Memory Resources,  Up: Resource Usage And Limitation

22.5 Learn about the processors available
=========================================

The use of threads or processes with shared memory allows an application
to take advantage of all the processing power a system can provide.  If
the task can be parallelized the optimal way to write an application is
to have at any time as many processes running as there are processors.
To determine the number of processors available to the system one can
run

       sysconf (_SC_NPROCESSORS_CONF)

which returns the number of processors the operating system configured.
But it might be possible for the operating system to disable individual
processors and so the call

       sysconf (_SC_NPROCESSORS_ONLN)

returns the number of processors which are currently online (i.e.,
available).

   For these two pieces of information the GNU C Library also provides
functions to get the information directly.  The functions are declared
in ‘sys/sysinfo.h’.

 -- Function: int get_nprocs_conf (void)

     Preliminary: | MT-Safe | AS-Unsafe heap lock | AC-Unsafe lock fd
     mem | *Note POSIX Safety Concepts::.

     The ‘get_nprocs_conf’ function returns the number of processors the
     operating system configured.

     This function is a GNU extension.

 -- Function: int get_nprocs (void)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe fd | *Note POSIX Safety
     Concepts::.

     The ‘get_nprocs’ function returns the number of available
     processors.

     This function is a GNU extension.

   Before starting more threads it should be checked whether the
processors are not already overused.  Unix systems calculate something
called the “load average”.  This is a number indicating how many
processes were running.  This number is an average over different
periods of time (normally 1, 5, and 15 minutes).

 -- Function: int getloadavg (double LOADAVG[], int NELEM)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe fd | *Note POSIX Safety
     Concepts::.

     This function gets the 1, 5 and 15 minute load averages of the
     system.  The values are placed in LOADAVG.  ‘getloadavg’ will place
     at most NELEM elements into the array but never more than three
     elements.  The return value is the number of elements written to
     LOADAVG, or -1 on error.

     This function is declared in ‘stdlib.h’.


File: libc.info,  Node: Non-Local Exits,  Next: Signal Handling,  Prev: Resource Usage And Limitation,  Up: Top

23 Non-Local Exits
******************

Sometimes when your program detects an unusual situation inside a deeply
nested set of function calls, you would like to be able to immediately
return to an outer level of control.  This section describes how you can
do such “non-local exits” using the ‘setjmp’ and ‘longjmp’ functions.

* Menu:

* Intro: Non-Local Intro.        When and how to use these facilities.
* Details: Non-Local Details.    Functions for non-local exits.
* Non-Local Exits and Signals::  Portability issues.
* System V contexts::            Complete context control a la System V.


File: libc.info,  Node: Non-Local Intro,  Next: Non-Local Details,  Up: Non-Local Exits

23.1 Introduction to Non-Local Exits
====================================

As an example of a situation where a non-local exit can be useful,
suppose you have an interactive program that has a “main loop” that
prompts for and executes commands.  Suppose the “read” command reads
input from a file, doing some lexical analysis and parsing of the input
while processing it.  If a low-level input error is detected, it would
be useful to be able to return immediately to the “main loop” instead of
having to make each of the lexical analysis, parsing, and processing
phases all have to explicitly deal with error situations initially
detected by nested calls.

   (On the other hand, if each of these phases has to do a substantial
amount of cleanup when it exits—such as closing files, deallocating
buffers or other data structures, and the like—then it can be more
appropriate to do a normal return and have each phase do its own
cleanup, because a non-local exit would bypass the intervening phases
and their associated cleanup code entirely.  Alternatively, you could
use a non-local exit but do the cleanup explicitly either before or
after returning to the “main loop”.)

   In some ways, a non-local exit is similar to using the ‘return’
statement to return from a function.  But while ‘return’ abandons only a
single function call, transferring control back to the point at which it
was called, a non-local exit can potentially abandon many levels of
nested function calls.

   You identify return points for non-local exits by calling the
function ‘setjmp’.  This function saves information about the execution
environment in which the call to ‘setjmp’ appears in an object of type
‘jmp_buf’.  Execution of the program continues normally after the call
to ‘setjmp’, but if an exit is later made to this return point by
calling ‘longjmp’ with the corresponding ‘jmp_buf’ object, control is
transferred back to the point where ‘setjmp’ was called.  The return
value from ‘setjmp’ is used to distinguish between an ordinary return
and a return made by a call to ‘longjmp’, so calls to ‘setjmp’ usually
appear in an ‘if’ statement.

   Here is how the example program described above might be set up:


     #include <setjmp.h>
     #include <stdlib.h>
     #include <stdio.h>

     jmp_buf main_loop;

     void
     abort_to_main_loop (int status)
     {
       longjmp (main_loop, status);
     }

     int
     main (void)
     {
       while (1)
         if (setjmp (main_loop))
           puts ("Back at main loop....");
         else
           do_command ();
     }


     void
     do_command (void)
     {
       char buffer[128];
       if (fgets (buffer, 128, stdin) == NULL)
         abort_to_main_loop (-1);
       else
         exit (EXIT_SUCCESS);
     }

   The function ‘abort_to_main_loop’ causes an immediate transfer of
control back to the main loop of the program, no matter where it is
called from.

   The flow of control inside the ‘main’ function may appear a little
mysterious at first, but it is actually a common idiom with ‘setjmp’.  A
normal call to ‘setjmp’ returns zero, so the “else” clause of the
conditional is executed.  If ‘abort_to_main_loop’ is called somewhere
within the execution of ‘do_command’, then it actually appears as if the
_same_ call to ‘setjmp’ in ‘main’ were returning a second time with a
value of ‘-1’.

   So, the general pattern for using ‘setjmp’ looks something like:

     if (setjmp (BUFFER))
       /* Code to clean up after premature return. */
       ...
     else
       /* Code to be executed normally after setting up the return point. */
       ...


File: libc.info,  Node: Non-Local Details,  Next: Non-Local Exits and Signals,  Prev: Non-Local Intro,  Up: Non-Local Exits

23.2 Details of Non-Local Exits
===============================

Here are the details on the functions and data structures used for
performing non-local exits.  These facilities are declared in
‘setjmp.h’.

 -- Data Type: jmp_buf

     Objects of type ‘jmp_buf’ hold the state information to be restored
     by a non-local exit.  The contents of a ‘jmp_buf’ identify a
     specific place to return to.

 -- Macro: int setjmp (jmp_buf STATE)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     When called normally, ‘setjmp’ stores information about the
     execution state of the program in STATE and returns zero.  If
     ‘longjmp’ is later used to perform a non-local exit to this STATE,
     ‘setjmp’ returns a nonzero value.

 -- Function: void longjmp (jmp_buf STATE, int VALUE)

     Preliminary: | MT-Safe | AS-Unsafe plugin corrupt lock/hurd |
     AC-Unsafe corrupt lock/hurd | *Note POSIX Safety Concepts::.

     This function restores current execution to the state saved in
     STATE, and continues execution from the call to ‘setjmp’ that
     established that return point.  Returning from ‘setjmp’ by means of
     ‘longjmp’ returns the VALUE argument that was passed to ‘longjmp’,
     rather than ‘0’.  (But if VALUE is given as ‘0’, ‘setjmp’ returns
     ‘1’).

   There are a lot of obscure but important restrictions on the use of
‘setjmp’ and ‘longjmp’.  Most of these restrictions are present because
non-local exits require a fair amount of magic on the part of the C
compiler and can interact with other parts of the language in strange
ways.

   The ‘setjmp’ function is actually a macro without an actual function
definition, so you shouldn’t try to ‘#undef’ it or take its address.  In
addition, calls to ‘setjmp’ are safe in only the following contexts:

   • As the test expression of a selection or iteration statement (such
     as ‘if’, ‘switch’, or ‘while’).

   • As one operand of an equality or comparison operator that appears
     as the test expression of a selection or iteration statement.  The
     other operand must be an integer constant expression.

   • As the operand of a unary ‘!’ operator, that appears as the test
     expression of a selection or iteration statement.

   • By itself as an expression statement.

   Return points are valid only during the dynamic extent of the
function that called ‘setjmp’ to establish them.  If you ‘longjmp’ to a
return point that was established in a function that has already
returned, unpredictable and disastrous things are likely to happen.

   You should use a nonzero VALUE argument to ‘longjmp’.  While
‘longjmp’ refuses to pass back a zero argument as the return value from
‘setjmp’, this is intended as a safety net against accidental misuse and
is not really good programming style.

   When you perform a non-local exit, accessible objects generally
retain whatever values they had at the time ‘longjmp’ was called.  The
exception is that the values of automatic variables local to the
function containing the ‘setjmp’ call that have been changed since the
call to ‘setjmp’ are indeterminate, unless you have declared them
‘volatile’.


File: libc.info,  Node: Non-Local Exits and Signals,  Next: System V contexts,  Prev: Non-Local Details,  Up: Non-Local Exits

23.3 Non-Local Exits and Signals
================================

In BSD Unix systems, ‘setjmp’ and ‘longjmp’ also save and restore the
set of blocked signals; see *note Blocking Signals::.  However, the
POSIX.1 standard requires ‘setjmp’ and ‘longjmp’ not to change the set
of blocked signals, and provides an additional pair of functions
(‘sigsetjmp’ and ‘siglongjmp’) to get the BSD behavior.

   The behavior of ‘setjmp’ and ‘longjmp’ in the GNU C Library is
controlled by feature test macros; see *note Feature Test Macros::.  The
default in the GNU C Library is the POSIX.1 behavior rather than the BSD
behavior.

   The facilities in this section are declared in the header file
‘setjmp.h’.

 -- Data Type: sigjmp_buf

     This is similar to ‘jmp_buf’, except that it can also store state
     information about the set of blocked signals.

 -- Function: int sigsetjmp (sigjmp_buf STATE, int SAVESIGS)

     Preliminary: | MT-Safe | AS-Unsafe lock/hurd | AC-Unsafe lock/hurd
     | *Note POSIX Safety Concepts::.

     This is similar to ‘setjmp’.  If SAVESIGS is nonzero, the set of
     blocked signals is saved in STATE and will be restored if a
     ‘siglongjmp’ is later performed with this STATE.

 -- Function: void siglongjmp (sigjmp_buf STATE, int VALUE)

     Preliminary: | MT-Safe | AS-Unsafe plugin corrupt lock/hurd |
     AC-Unsafe corrupt lock/hurd | *Note POSIX Safety Concepts::.

     This is similar to ‘longjmp’ except for the type of its STATE
     argument.  If the ‘sigsetjmp’ call that set this STATE used a
     nonzero SAVESIGS flag, ‘siglongjmp’ also restores the set of
     blocked signals.


File: libc.info,  Node: System V contexts,  Prev: Non-Local Exits and Signals,  Up: Non-Local Exits

23.4 Complete Context Control
=============================

The Unix standard provides one more set of functions to control the
execution path and these functions are more powerful than those
discussed in this chapter so far.  These functions were part of the
original System V API and by this route were added to the Unix API.
Besides on branded Unix implementations these interfaces are not widely
available.  Not all platforms and/or architectures the GNU C Library is
available on provide this interface.  Use ‘configure’ to detect the
availability.

   Similar to the ‘jmp_buf’ and ‘sigjmp_buf’ types used for the
variables to contain the state of the ‘longjmp’ functions the interfaces
of interest here have an appropriate type as well.  Objects of this type
are normally much larger since more information is contained.  The type
is also used in a few more places as we will see.  The types and
functions described in this section are all defined and declared
respectively in the ‘ucontext.h’ header file.

 -- Data Type: ucontext_t

     The ‘ucontext_t’ type is defined as a structure with at least the
     following elements:

     ‘ucontext_t *uc_link’
          This is a pointer to the next context structure which is used
          if the context described in the current structure returns.

     ‘sigset_t uc_sigmask’
          Set of signals which are blocked when this context is used.

     ‘stack_t uc_stack’
          Stack used for this context.  The value need not be (and
          normally is not) the stack pointer.  *Note Signal Stack::.

     ‘mcontext_t uc_mcontext’
          This element contains the actual state of the process.  The
          ‘mcontext_t’ type is also defined in this header but the
          definition should be treated as opaque.  Any use of knowledge
          of the type makes applications less portable.

   Objects of this type have to be created by the user.  The
initialization and modification happens through one of the following
functions:

 -- Function: int getcontext (ucontext_t *UCP)

     Preliminary: | MT-Safe race:ucp | AS-Safe | AC-Safe | *Note POSIX
     Safety Concepts::.

     The ‘getcontext’ function initializes the variable pointed to by
     UCP with the context of the calling thread.  The context contains
     the content of the registers, the signal mask, and the current
     stack.  Executing the contents would start at the point where the
     ‘getcontext’ call just returned.

     *Compatibility Note:* Depending on the operating system,
     information about the current context’s stack may be in the
     ‘uc_stack’ field of UCP, or it may instead be in
     architecture-specific subfields of the ‘uc_mcontext’ field.

     The function returns ‘0’ if successful.  Otherwise it returns ‘-1’
     and sets ‘errno’ accordingly.

   The ‘getcontext’ function is similar to ‘setjmp’ but it does not
provide an indication of whether ‘getcontext’ is returning for the first
time or whether an initialized context has just been restored.  If this
is necessary the user has to determine this herself.  This must be done
carefully since the context contains registers which might contain
register variables.  This is a good situation to define variables with
‘volatile’.

   Once the context variable is initialized it can be used as is or it
can be modified using the ‘makecontext’ function.  The latter is
normally done when implementing co-routines or similar constructs.

 -- Function: void makecontext (ucontext_t *UCP, void (*FUNC) (void),
          int ARGC, ...)

     Preliminary: | MT-Safe race:ucp | AS-Safe | AC-Safe | *Note POSIX
     Safety Concepts::.

     The UCP parameter passed to ‘makecontext’ shall be initialized by a
     call to ‘getcontext’.  The context will be modified in a way such
     that if the context is resumed it will start by calling the
     function ‘func’ which gets ARGC integer arguments passed.  The
     integer arguments which are to be passed should follow the ARGC
     parameter in the call to ‘makecontext’.

     Before the call to this function the ‘uc_stack’ and ‘uc_link’
     element of the UCP structure should be initialized.  The ‘uc_stack’
     element describes the stack which is used for this context.  No two
     contexts which are used at the same time should use the same memory
     region for a stack.

     The ‘uc_link’ element of the object pointed to by UCP should be a
     pointer to the context to be executed when the function FUNC
     returns or it should be a null pointer.  See ‘setcontext’ for more
     information about the exact use.

   While allocating the memory for the stack one has to be careful.
Most modern processors keep track of whether a certain memory region is
allowed to contain code which is executed or not.  Data segments and
heap memory are normally not tagged to allow this.  The result is that
programs would fail.  Examples for such code include the calling
sequences the GNU C compiler generates for calls to nested functions.
Safe ways to allocate stacks correctly include using memory on the
original thread’s stack or explicitly allocating memory tagged for
execution using (*note Memory-mapped I/O::).

   *Compatibility note*: The current Unix standard is very imprecise
about the way the stack is allocated.  All implementations seem to agree
that the ‘uc_stack’ element must be used but the values stored in the
elements of the ‘stack_t’ value are unclear.  The GNU C Library and most
other Unix implementations require the ‘ss_sp’ value of the ‘uc_stack’
element to point to the base of the memory region allocated for the
stack and the size of the memory region is stored in ‘ss_size’.  There
are implementations out there which require ‘ss_sp’ to be set to the
value the stack pointer will have (which can, depending on the direction
the stack grows, be different).  This difference makes the ‘makecontext’
function hard to use and it requires detection of the platform at
compile time.

 -- Function: int setcontext (const ucontext_t *UCP)

     Preliminary: | MT-Safe race:ucp | AS-Unsafe corrupt | AC-Unsafe
     corrupt | *Note POSIX Safety Concepts::.

     The ‘setcontext’ function restores the context described by UCP.
     The context is not modified and can be reused as often as wanted.

     If the context was created by ‘getcontext’ execution resumes with
     the registers filled with the same values and the same stack as if
     the ‘getcontext’ call just returned.

     If the context was modified with a call to ‘makecontext’ execution
     continues with the function passed to ‘makecontext’ which gets the
     specified parameters passed.  If this function returns execution is
     resumed in the context which was referenced by the ‘uc_link’
     element of the context structure passed to ‘makecontext’ at the
     time of the call.  If ‘uc_link’ was a null pointer the application
     terminates normally with an exit status value of ‘EXIT_SUCCESS’
     (*note Program Termination::).

     If the context was created by a call to a signal handler or from
     any other source then the behaviour of ‘setcontext’ is unspecified.

     Since the context contains information about the stack no two
     threads should use the same context at the same time.  The result
     in most cases would be disastrous.

     The ‘setcontext’ function does not return unless an error occurred
     in which case it returns ‘-1’.

   The ‘setcontext’ function simply replaces the current context with
the one described by the UCP parameter.  This is often useful but there
are situations where the current context has to be preserved.

 -- Function: int swapcontext (ucontext_t *restrict OUCP, const
          ucontext_t *restrict UCP)

     Preliminary: | MT-Safe race:oucp race:ucp | AS-Unsafe corrupt |
     AC-Unsafe corrupt | *Note POSIX Safety Concepts::.

     The ‘swapcontext’ function is similar to ‘setcontext’ but instead
     of just replacing the current context the latter is first saved in
     the object pointed to by OUCP as if this was a call to
     ‘getcontext’.  The saved context would resume after the call to
     ‘swapcontext’.

     Once the current context is saved the context described in UCP is
     installed and execution continues as described in this context.

     If ‘swapcontext’ succeeds the function does not return unless the
     context OUCP is used without prior modification by ‘makecontext’.
     The return value in this case is ‘0’.  If the function fails it
     returns ‘-1’ and sets ‘errno’ accordingly.

Example for SVID Context Handling
=================================

The easiest way to use the context handling functions is as a
replacement for ‘setjmp’ and ‘longjmp’.  The context contains on most
platforms more information which may lead to fewer surprises but this
also means using these functions is more expensive (besides being less
portable).

     int
     random_search (int n, int (*fp) (int, ucontext_t *))
     {
       volatile int cnt = 0;
       ucontext_t uc;

       /* Safe current context.  */
       if (getcontext (&uc) < 0)
         return -1;

       /* If we have not tried N times try again.  */
       if (cnt++ < n)
         /* Call the function with a new random number
            and the context.  */
         if (fp (rand (), &uc) != 0)
           /* We found what we were looking for.  */
           return 1;

       /* Not found.  */
       return 0;
     }

   Using contexts in such a way enables emulating exception handling.
The search functions passed in the FP parameter could be very large,
nested, and complex which would make it complicated (or at least would
require a lot of code) to leave the function with an error value which
has to be passed down to the caller.  By using the context it is
possible to leave the search function in one step and allow restarting
the search which also has the nice side effect that it can be
significantly faster.

   Something which is harder to implement with ‘setjmp’ and ‘longjmp’ is
to switch temporarily to a different execution path and then resume
where execution was stopped.


     #include <signal.h>
     #include <stdio.h>
     #include <stdlib.h>
     #include <ucontext.h>
     #include <sys/time.h>

     /* Set by the signal handler. */
     static volatile int expired;

     /* The contexts. */
     static ucontext_t uc[3];

     /* We do only a certain number of switches. */
     static int switches;


     /* This is the function doing the work.  It is just a
        skeleton, real code has to be filled in. */
     static void
     f (int n)
     {
       int m = 0;
       while (1)
         {
           /* This is where the work would be done. */
           if (++m % 100 == 0)
             {
               putchar ('.');
               fflush (stdout);
             }

           /* Regularly the EXPIRE variable must be checked. */
           if (expired)
             {
               /* We do not want the program to run forever. */
               if (++switches == 20)
                 return;

               printf ("\nswitching from %d to %d\n", n, 3 - n);
               expired = 0;
               /* Switch to the other context, saving the current one. */
               swapcontext (&uc[n], &uc[3 - n]);
             }
         }
     }

     /* This is the signal handler which simply set the variable. */
     void
     handler (int signal)
     {
       expired = 1;
     }


     int
     main (void)
     {
       struct sigaction sa;
       struct itimerval it;
       char st1[8192];
       char st2[8192];

       /* Initialize the data structures for the interval timer. */
       sa.sa_flags = SA_RESTART;
       sigfillset (&sa.sa_mask);
       sa.sa_handler = handler;
       it.it_interval.tv_sec = 0;
       it.it_interval.tv_usec = 1;
       it.it_value = it.it_interval;

       /* Install the timer and get the context we can manipulate. */
       if (sigaction (SIGPROF, &sa, NULL) < 0
           || setitimer (ITIMER_PROF, &it, NULL) < 0
           || getcontext (&uc[1]) == -1
           || getcontext (&uc[2]) == -1)
         abort ();

       /* Create a context with a separate stack which causes the
          function ‘f’ to be call with the parameter ‘1’.
          Note that the ‘uc_link’ points to the main context
          which will cause the program to terminate once the function
          return. */
       uc[1].uc_link = &uc[0];
       uc[1].uc_stack.ss_sp = st1;
       uc[1].uc_stack.ss_size = sizeof st1;
       makecontext (&uc[1], (void (*) (void)) f, 1, 1);

       /* Similarly, but ‘2’ is passed as the parameter to ‘f’. */
       uc[2].uc_link = &uc[0];
       uc[2].uc_stack.ss_sp = st2;
       uc[2].uc_stack.ss_size = sizeof st2;
       makecontext (&uc[2], (void (*) (void)) f, 1, 2);

       /* Start running. */
       swapcontext (&uc[0], &uc[1]);
       putchar ('\n');

       return 0;
     }

   This an example how the context functions can be used to implement
co-routines or cooperative multi-threading.  All that has to be done is
to call every once in a while ‘swapcontext’ to continue running a
different context.  It is not recommended to do the context switching
from the signal handler directly since leaving the signal handler via
‘setcontext’ if the signal was delivered during code that was not
asynchronous signal safe could lead to problems.  Setting a variable in
the signal handler and checking it in the body of the functions which
are executed is a safer approach.  Since ‘swapcontext’ is saving the
current context it is possible to have multiple different scheduling
points in the code.  Execution will always resume where it was left.


File: libc.info,  Node: Signal Handling,  Next: Program Basics,  Prev: Non-Local Exits,  Up: Top

24 Signal Handling
******************

A “signal” is a software interrupt delivered to a process.  The
operating system uses signals to report exceptional situations to an
executing program.  Some signals report errors such as references to
invalid memory addresses; others report asynchronous events, such as
disconnection of a phone line.

   The GNU C Library defines a variety of signal types, each for a
particular kind of event.  Some kinds of events make it inadvisable or
impossible for the program to proceed as usual, and the corresponding
signals normally abort the program.  Other kinds of signals that report
harmless events are ignored by default.

   If you anticipate an event that causes signals, you can define a
handler function and tell the operating system to run it when that
particular type of signal arrives.

   Finally, one process can send a signal to another process; this
allows a parent process to abort a child, or two related processes to
communicate and synchronize.

* Menu:

* Concepts of Signals::         Introduction to the signal facilities.
* Standard Signals::            Particular kinds of signals with
                                 standard names and meanings.
* Signal Actions::              Specifying what happens when a
                                 particular signal is delivered.
* Defining Handlers::           How to write a signal handler function.
* Interrupted Primitives::	Signal handlers affect use of ‘open’,
				 ‘read’, ‘write’ and other functions.
* Generating Signals::          How to send a signal to a process.
* Blocking Signals::            Making the system hold signals temporarily.
* Waiting for a Signal::        Suspending your program until a signal
                                 arrives.
* Signal Stack::                Using a Separate Signal Stack.
* BSD Signal Handling::         Additional functions for backward
			         compatibility with BSD.


File: libc.info,  Node: Concepts of Signals,  Next: Standard Signals,  Up: Signal Handling

24.1 Basic Concepts of Signals
==============================

This section explains basic concepts of how signals are generated, what
happens after a signal is delivered, and how programs can handle
signals.

* Menu:

* Kinds of Signals::            Some examples of what can cause a signal.
* Signal Generation::           Concepts of why and how signals occur.
* Delivery of Signal::          Concepts of what a signal does to the
                                 process.


File: libc.info,  Node: Kinds of Signals,  Next: Signal Generation,  Up: Concepts of Signals

24.1.1 Some Kinds of Signals
----------------------------

A signal reports the occurrence of an exceptional event.  These are some
of the events that can cause (or “generate”, or “raise”) a signal:

   • A program error such as dividing by zero or issuing an address
     outside the valid range.

   • A user request to interrupt or terminate the program.  Most
     environments are set up to let a user suspend the program by typing
     ‘C-z’, or terminate it with ‘C-c’.  Whatever key sequence is used,
     the operating system sends the proper signal to interrupt the
     process.

   • The termination of a child process.

   • Expiration of a timer or alarm.

   • A call to ‘kill’ or ‘raise’ by the same process.

   • A call to ‘kill’ from another process.  Signals are a limited but
     useful form of interprocess communication.

   • An attempt to perform an I/O operation that cannot be done.
     Examples are reading from a pipe that has no writer (*note Pipes
     and FIFOs::), and reading or writing to a terminal in certain
     situations (*note Job Control::).

   Each of these kinds of events (excepting explicit calls to ‘kill’ and
‘raise’) generates its own particular kind of signal.  The various kinds
of signals are listed and described in detail in *note Standard
Signals::.


File: libc.info,  Node: Signal Generation,  Next: Delivery of Signal,  Prev: Kinds of Signals,  Up: Concepts of Signals

24.1.2 Concepts of Signal Generation
------------------------------------

In general, the events that generate signals fall into three major
categories: errors, external events, and explicit requests.

   An error means that a program has done something invalid and cannot
continue execution.  But not all kinds of errors generate signals—in
fact, most do not.  For example, opening a nonexistent file is an error,
but it does not raise a signal; instead, ‘open’ returns ‘-1’.  In
general, errors that are necessarily associated with certain library
functions are reported by returning a value that indicates an error.
The errors which raise signals are those which can happen anywhere in
the program, not just in library calls.  These include division by zero
and invalid memory addresses.

   An external event generally has to do with I/O or other processes.
These include the arrival of input, the expiration of a timer, and the
termination of a child process.

   An explicit request means the use of a library function such as
‘kill’ whose purpose is specifically to generate a signal.

   Signals may be generated “synchronously” or “asynchronously”.  A
synchronous signal pertains to a specific action in the program, and is
delivered (unless blocked) during that action.  Most errors generate
signals synchronously, and so do explicit requests by a process to
generate a signal for that same process.  On some machines, certain
kinds of hardware errors (usually floating-point exceptions) are not
reported completely synchronously, but may arrive a few instructions
later.

   Asynchronous signals are generated by events outside the control of
the process that receives them.  These signals arrive at unpredictable
times during execution.  External events generate signals
asynchronously, and so do explicit requests that apply to some other
process.

   A given type of signal is either typically synchronous or typically
asynchronous.  For example, signals for errors are typically synchronous
because errors generate signals synchronously.  But any type of signal
can be generated synchronously or asynchronously with an explicit
request.


File: libc.info,  Node: Delivery of Signal,  Prev: Signal Generation,  Up: Concepts of Signals

24.1.3 How Signals Are Delivered
--------------------------------

When a signal is generated, it becomes “pending”.  Normally it remains
pending for just a short period of time and then is “delivered” to the
process that was signaled.  However, if that kind of signal is currently
“blocked”, it may remain pending indefinitely—until signals of that kind
are “unblocked”.  Once unblocked, it will be delivered immediately.
*Note Blocking Signals::.

   When the signal is delivered, whether right away or after a long
delay, the “specified action” for that signal is taken.  For certain
signals, such as ‘SIGKILL’ and ‘SIGSTOP’, the action is fixed, but for
most signals, the program has a choice: ignore the signal, specify a
“handler function”, or accept the “default action” for that kind of
signal.  The program specifies its choice using functions such as
‘signal’ or ‘sigaction’ (*note Signal Actions::).  We sometimes say that
a handler “catches” the signal.  While the handler is running, that
particular signal is normally blocked.

   If the specified action for a kind of signal is to ignore it, then
any such signal which is generated is discarded immediately.  This
happens even if the signal is also blocked at the time.  A signal
discarded in this way will never be delivered, not even if the program
subsequently specifies a different action for that kind of signal and
then unblocks it.

   If a signal arrives which the program has neither handled nor
ignored, its “default action” takes place.  Each kind of signal has its
own default action, documented below (*note Standard Signals::).  For
most kinds of signals, the default action is to terminate the process.
For certain kinds of signals that represent “harmless” events, the
default action is to do nothing.

   When a signal terminates a process, its parent process can determine
the cause of termination by examining the termination status code
reported by the ‘wait’ or ‘waitpid’ functions.  (This is discussed in
more detail in *note Process Completion::.)  The information it can get
includes the fact that termination was due to a signal and the kind of
signal involved.  If a program you run from a shell is terminated by a
signal, the shell typically prints some kind of error message.

   The signals that normally represent program errors have a special
property: when one of these signals terminates the process, it also
writes a “core dump file” which records the state of the process at the
time of termination.  You can examine the core dump with a debugger to
investigate what caused the error.

   If you raise a “program error” signal by explicit request, and this
terminates the process, it makes a core dump file just as if the signal
had been due directly to an error.


File: libc.info,  Node: Standard Signals,  Next: Signal Actions,  Prev: Concepts of Signals,  Up: Signal Handling

24.2 Standard Signals
=====================

This section lists the names for various standard kinds of signals and
describes what kind of event they mean.  Each signal name is a macro
which stands for a positive integer—the “signal number” for that kind of
signal.  Your programs should never make assumptions about the numeric
code for a particular kind of signal, but rather refer to them always by
the names defined here.  This is because the number for a given kind of
signal can vary from system to system, but the meanings of the names are
standardized and fairly uniform.

   The signal names are defined in the header file ‘signal.h’.

 -- Macro: int NSIG

     The value of this symbolic constant is the total number of signals
     defined.  Since the signal numbers are allocated consecutively,
     ‘NSIG’ is also one greater than the largest defined signal number.

* Menu:

* Program Error Signals::       Used to report serious program errors.
* Termination Signals::         Used to interrupt and/or terminate the
                                 program.
* Alarm Signals::               Used to indicate expiration of timers.
* Asynchronous I/O Signals::    Used to indicate input is available.
* Job Control Signals::         Signals used to support job control.
* Operation Error Signals::     Used to report operational system errors.
* Miscellaneous Signals::       Miscellaneous Signals.
* Signal Messages::             Printing a message describing a signal.


File: libc.info,  Node: Program Error Signals,  Next: Termination Signals,  Up: Standard Signals

24.2.1 Program Error Signals
----------------------------

The following signals are generated when a serious program error is
detected by the operating system or the computer itself.  In general,
all of these signals are indications that your program is seriously
broken in some way, and there’s usually no way to continue the
computation which encountered the error.

   Some programs handle program error signals in order to tidy up before
terminating; for example, programs that turn off echoing of terminal
input should handle program error signals in order to turn echoing back
on.  The handler should end by specifying the default action for the
signal that happened and then reraising it; this will cause the program
to terminate with that signal, as if it had not had a handler.  (*Note
Termination in Handler::.)

   Termination is the sensible ultimate outcome from a program error in
most programs.  However, programming systems such as Lisp that can load
compiled user programs might need to keep executing even if a user
program incurs an error.  These programs have handlers which use
‘longjmp’ to return control to the command level.

   The default action for all of these signals is to cause the process
to terminate.  If you block or ignore these signals or establish
handlers for them that return normally, your program will probably break
horribly when such signals happen, unless they are generated by ‘raise’
or ‘kill’ instead of a real error.

   When one of these program error signals terminates a process, it also
writes a “core dump file” which records the state of the process at the
time of termination.  The core dump file is named ‘core’ and is written
in whichever directory is current in the process at the time.  (On
GNU/Hurd systems, you can specify the file name for core dumps with the
environment variable ‘COREFILE’.)  The purpose of core dump files is so
that you can examine them with a debugger to investigate what caused the
error.

 -- Macro: int SIGFPE

     The ‘SIGFPE’ signal reports a fatal arithmetic error.  Although the
     name is derived from “floating-point exception”, this signal
     actually covers all arithmetic errors, including division by zero
     and overflow.  If a program stores integer data in a location which
     is then used in a floating-point operation, this often causes an
     “invalid operation” exception, because the processor cannot
     recognize the data as a floating-point number.

     Actual floating-point exceptions are a complicated subject because
     there are many types of exceptions with subtly different meanings,
     and the ‘SIGFPE’ signal doesn’t distinguish between them.  The
     ‘IEEE Standard for Binary Floating-Point Arithmetic (ANSI/IEEE Std
     754-1985 and ANSI/IEEE Std 854-1987)’ defines various
     floating-point exceptions and requires conforming computer systems
     to report their occurrences.  However, this standard does not
     specify how the exceptions are reported, or what kinds of handling
     and control the operating system can offer to the programmer.

   BSD systems provide the ‘SIGFPE’ handler with an extra argument that
distinguishes various causes of the exception.  In order to access this
argument, you must define the handler to accept two arguments, which
means you must cast it to a one-argument function type in order to
establish the handler.  The GNU C Library does provide this extra
argument, but the value is meaningful only on operating systems that
provide the information (BSD systems and GNU systems).

‘FPE_INTOVF_TRAP’

     Integer overflow (impossible in a C program unless you enable
     overflow trapping in a hardware-specific fashion).
‘FPE_INTDIV_TRAP’

     Integer division by zero.
‘FPE_SUBRNG_TRAP’

     Subscript-range (something that C programs never check for).
‘FPE_FLTOVF_TRAP’

     Floating overflow trap.
‘FPE_FLTDIV_TRAP’

     Floating/decimal division by zero.
‘FPE_FLTUND_TRAP’

     Floating underflow trap.  (Trapping on floating underflow is not
     normally enabled.)
‘FPE_DECOVF_TRAP’

     Decimal overflow trap.  (Only a few machines have decimal
     arithmetic and C never uses it.)

 -- Macro: int SIGILL

     The name of this signal is derived from “illegal instruction”; it
     usually means your program is trying to execute garbage or a
     privileged instruction.  Since the C compiler generates only valid
     instructions, ‘SIGILL’ typically indicates that the executable file
     is corrupted, or that you are trying to execute data.  Some common
     ways of getting into the latter situation are by passing an invalid
     object where a pointer to a function was expected, or by writing
     past the end of an automatic array (or similar problems with
     pointers to automatic variables) and corrupting other data on the
     stack such as the return address of a stack frame.

     ‘SIGILL’ can also be generated when the stack overflows, or when
     the system has trouble running the handler for a signal.

 -- Macro: int SIGSEGV

     This signal is generated when a program tries to read or write
     outside the memory that is allocated for it, or to write memory
     that can only be read.  (Actually, the signals only occur when the
     program goes far enough outside to be detected by the system’s
     memory protection mechanism.)  The name is an abbreviation for
     “segmentation violation”.

     Common ways of getting a ‘SIGSEGV’ condition include dereferencing
     a null or uninitialized pointer, or when you use a pointer to step
     through an array, but fail to check for the end of the array.  It
     varies among systems whether dereferencing a null pointer generates
     ‘SIGSEGV’ or ‘SIGBUS’.

 -- Macro: int SIGBUS

     This signal is generated when an invalid pointer is dereferenced.
     Like ‘SIGSEGV’, this signal is typically the result of
     dereferencing an uninitialized pointer.  The difference between the
     two is that ‘SIGSEGV’ indicates an invalid access to valid memory,
     while ‘SIGBUS’ indicates an access to an invalid address.  In
     particular, ‘SIGBUS’ signals often result from dereferencing a
     misaligned pointer, such as referring to a four-word integer at an
     address not divisible by four.  (Each kind of computer has its own
     requirements for address alignment.)

     The name of this signal is an abbreviation for “bus error”.

 -- Macro: int SIGABRT

     This signal indicates an error detected by the program itself and
     reported by calling ‘abort’.  *Note Aborting a Program::.

 -- Macro: int SIGIOT

     Generated by the PDP-11 “iot” instruction.  On most machines, this
     is just another name for ‘SIGABRT’.

 -- Macro: int SIGTRAP

     Generated by the machine’s breakpoint instruction, and possibly
     other trap instructions.  This signal is used by debuggers.  Your
     program will probably only see ‘SIGTRAP’ if it is somehow executing
     bad instructions.

 -- Macro: int SIGEMT

     Emulator trap; this results from certain unimplemented instructions
     which might be emulated in software, or the operating system’s
     failure to properly emulate them.

 -- Macro: int SIGSYS

     Bad system call; that is to say, the instruction to trap to the
     operating system was executed, but the code number for the system
     call to perform was invalid.


File: libc.info,  Node: Termination Signals,  Next: Alarm Signals,  Prev: Program Error Signals,  Up: Standard Signals

24.2.2 Termination Signals
--------------------------

These signals are all used to tell a process to terminate, in one way or
another.  They have different names because they’re used for slightly
different purposes, and programs might want to handle them differently.

   The reason for handling these signals is usually so your program can
tidy up as appropriate before actually terminating.  For example, you
might want to save state information, delete temporary files, or restore
the previous terminal modes.  Such a handler should end by specifying
the default action for the signal that happened and then reraising it;
this will cause the program to terminate with that signal, as if it had
not had a handler.  (*Note Termination in Handler::.)

   The (obvious) default action for all of these signals is to cause the
process to terminate.

 -- Macro: int SIGTERM

     The ‘SIGTERM’ signal is a generic signal used to cause program
     termination.  Unlike ‘SIGKILL’, this signal can be blocked,
     handled, and ignored.  It is the normal way to politely ask a
     program to terminate.

     The shell command ‘kill’ generates ‘SIGTERM’ by default.

 -- Macro: int SIGINT

     The ‘SIGINT’ (“program interrupt”) signal is sent when the user
     types the INTR character (normally ‘C-c’).  *Note Special
     Characters::, for information about terminal driver support for
     ‘C-c’.

 -- Macro: int SIGQUIT

     The ‘SIGQUIT’ signal is similar to ‘SIGINT’, except that it’s
     controlled by a different key—the QUIT character, usually ‘C-\’—and
     produces a core dump when it terminates the process, just like a
     program error signal.  You can think of this as a program error
     condition “detected” by the user.

     *Note Program Error Signals::, for information about core dumps.
     *Note Special Characters::, for information about terminal driver
     support.

     Certain kinds of cleanups are best omitted in handling ‘SIGQUIT’.
     For example, if the program creates temporary files, it should
     handle the other termination requests by deleting the temporary
     files.  But it is better for ‘SIGQUIT’ not to delete them, so that
     the user can examine them in conjunction with the core dump.

 -- Macro: int SIGKILL

     The ‘SIGKILL’ signal is used to cause immediate program
     termination.  It cannot be handled or ignored, and is therefore
     always fatal.  It is also not possible to block this signal.

     This signal is usually generated only by explicit request.  Since
     it cannot be handled, you should generate it only as a last resort,
     after first trying a less drastic method such as ‘C-c’ or
     ‘SIGTERM’.  If a process does not respond to any other termination
     signals, sending it a ‘SIGKILL’ signal will almost always cause it
     to go away.

     In fact, if ‘SIGKILL’ fails to terminate a process, that by itself
     constitutes an operating system bug which you should report.

     The system will generate ‘SIGKILL’ for a process itself under some
     unusual conditions where the program cannot possibly continue to
     run (even to run a signal handler).

 -- Macro: int SIGHUP

     The ‘SIGHUP’ (“hang-up”) signal is used to report that the user’s
     terminal is disconnected, perhaps because a network or telephone
     connection was broken.  For more information about this, see *note
     Control Modes::.

     This signal is also used to report the termination of the
     controlling process on a terminal to jobs associated with that
     session; this termination effectively disconnects all processes in
     the session from the controlling terminal.  For more information,
     see *note Termination Internals::.


File: libc.info,  Node: Alarm Signals,  Next: Asynchronous I/O Signals,  Prev: Termination Signals,  Up: Standard Signals

24.2.3 Alarm Signals
--------------------

These signals are used to indicate the expiration of timers.  *Note
Setting an Alarm::, for information about functions that cause these
signals to be sent.

   The default behavior for these signals is to cause program
termination.  This default is rarely useful, but no other default would
be useful; most of the ways of using these signals would require handler
functions in any case.

 -- Macro: int SIGALRM

     This signal typically indicates expiration of a timer that measures
     real or clock time.  It is used by the ‘alarm’ function, for
     example.

 -- Macro: int SIGVTALRM

     This signal typically indicates expiration of a timer that measures
     CPU time used by the current process.  The name is an abbreviation
     for “virtual time alarm”.

 -- Macro: int SIGPROF

     This signal typically indicates expiration of a timer that measures
     both CPU time used by the current process, and CPU time expended on
     behalf of the process by the system.  Such a timer is used to
     implement code profiling facilities, hence the name of this signal.


File: libc.info,  Node: Asynchronous I/O Signals,  Next: Job Control Signals,  Prev: Alarm Signals,  Up: Standard Signals

24.2.4 Asynchronous I/O Signals
-------------------------------

The signals listed in this section are used in conjunction with
asynchronous I/O facilities.  You have to take explicit action by
calling ‘fcntl’ to enable a particular file descriptor to generate these
signals (*note Interrupt Input::).  The default action for these signals
is to ignore them.

 -- Macro: int SIGIO

     This signal is sent when a file descriptor is ready to perform
     input or output.

     On most operating systems, terminals and sockets are the only kinds
     of files that can generate ‘SIGIO’; other kinds, including ordinary
     files, never generate ‘SIGIO’ even if you ask them to.

     On GNU systems ‘SIGIO’ will always be generated properly if you
     successfully set asynchronous mode with ‘fcntl’.

 -- Macro: int SIGURG

     This signal is sent when “urgent” or out-of-band data arrives on a
     socket.  *Note Out-of-Band Data::.

 -- Macro: int SIGPOLL

     This is a System V signal name, more or less similar to ‘SIGIO’.
     It is defined only for compatibility.


File: libc.info,  Node: Job Control Signals,  Next: Operation Error Signals,  Prev: Asynchronous I/O Signals,  Up: Standard Signals

24.2.5 Job Control Signals
--------------------------

These signals are used to support job control.  If your system doesn’t
support job control, then these macros are defined but the signals
themselves can’t be raised or handled.

   You should generally leave these signals alone unless you really
understand how job control works.  *Note Job Control::.

 -- Macro: int SIGCHLD

     This signal is sent to a parent process whenever one of its child
     processes terminates or stops.

     The default action for this signal is to ignore it.  If you
     establish a handler for this signal while there are child processes
     that have terminated but not reported their status via ‘wait’ or
     ‘waitpid’ (*note Process Completion::), whether your new handler
     applies to those processes or not depends on the particular
     operating system.

 -- Macro: int SIGCLD

     This is an obsolete name for ‘SIGCHLD’.

 -- Macro: int SIGCONT

     You can send a ‘SIGCONT’ signal to a process to make it continue.
     This signal is special—it always makes the process continue if it
     is stopped, before the signal is delivered.  The default behavior
     is to do nothing else.  You cannot block this signal.  You can set
     a handler, but ‘SIGCONT’ always makes the process continue
     regardless.

     Most programs have no reason to handle ‘SIGCONT’; they simply
     resume execution without realizing they were ever stopped.  You can
     use a handler for ‘SIGCONT’ to make a program do something special
     when it is stopped and continued—for example, to reprint a prompt
     when it is suspended while waiting for input.

 -- Macro: int SIGSTOP

     The ‘SIGSTOP’ signal stops the process.  It cannot be handled,
     ignored, or blocked.

 -- Macro: int SIGTSTP

     The ‘SIGTSTP’ signal is an interactive stop signal.  Unlike
     ‘SIGSTOP’, this signal can be handled and ignored.

     Your program should handle this signal if you have a special need
     to leave files or system tables in a secure state when a process is
     stopped.  For example, programs that turn off echoing should handle
     ‘SIGTSTP’ so they can turn echoing back on before stopping.

     This signal is generated when the user types the SUSP character
     (normally ‘C-z’).  For more information about terminal driver
     support, see *note Special Characters::.

 -- Macro: int SIGTTIN

     A process cannot read from the user’s terminal while it is running
     as a background job.  When any process in a background job tries to
     read from the terminal, all of the processes in the job are sent a
     ‘SIGTTIN’ signal.  The default action for this signal is to stop
     the process.  For more information about how this interacts with
     the terminal driver, see *note Access to the Terminal::.

 -- Macro: int SIGTTOU

     This is similar to ‘SIGTTIN’, but is generated when a process in a
     background job attempts to write to the terminal or set its modes.
     Again, the default action is to stop the process.  ‘SIGTTOU’ is
     only generated for an attempt to write to the terminal if the
     ‘TOSTOP’ output mode is set; *note Output Modes::.

   While a process is stopped, no more signals can be delivered to it
until it is continued, except ‘SIGKILL’ signals and (obviously)
‘SIGCONT’ signals.  The signals are marked as pending, but not delivered
until the process is continued.  The ‘SIGKILL’ signal always causes
termination of the process and can’t be blocked, handled or ignored.
You can ignore ‘SIGCONT’, but it always causes the process to be
continued anyway if it is stopped.  Sending a ‘SIGCONT’ signal to a
process causes any pending stop signals for that process to be
discarded.  Likewise, any pending ‘SIGCONT’ signals for a process are
discarded when it receives a stop signal.

   When a process in an orphaned process group (*note Orphaned Process
Groups::) receives a ‘SIGTSTP’, ‘SIGTTIN’, or ‘SIGTTOU’ signal and does
not handle it, the process does not stop.  Stopping the process would
probably not be very useful, since there is no shell program that will
notice it stop and allow the user to continue it.  What happens instead
depends on the operating system you are using.  Some systems may do
nothing; others may deliver another signal instead, such as ‘SIGKILL’ or
‘SIGHUP’.  On GNU/Hurd systems, the process dies with ‘SIGKILL’; this
avoids the problem of many stopped, orphaned processes lying around the
system.


File: libc.info,  Node: Operation Error Signals,  Next: Miscellaneous Signals,  Prev: Job Control Signals,  Up: Standard Signals

24.2.6 Operation Error Signals
------------------------------

These signals are used to report various errors generated by an
operation done by the program.  They do not necessarily indicate a
programming error in the program, but an error that prevents an
operating system call from completing.  The default action for all of
them is to cause the process to terminate.

 -- Macro: int SIGPIPE

     Broken pipe.  If you use pipes or FIFOs, you have to design your
     application so that one process opens the pipe for reading before
     another starts writing.  If the reading process never starts, or
     terminates unexpectedly, writing to the pipe or FIFO raises a
     ‘SIGPIPE’ signal.  If ‘SIGPIPE’ is blocked, handled or ignored, the
     offending call fails with ‘EPIPE’ instead.

     Pipes and FIFO special files are discussed in more detail in *note
     Pipes and FIFOs::.

     Another cause of ‘SIGPIPE’ is when you try to output to a socket
     that isn’t connected.  *Note Sending Data::.

 -- Macro: int SIGLOST

     Resource lost.  This signal is generated when you have an advisory
     lock on an NFS file, and the NFS server reboots and forgets about
     your lock.

     On GNU/Hurd systems, ‘SIGLOST’ is generated when any server program
     dies unexpectedly.  It is usually fine to ignore the signal;
     whatever call was made to the server that died just returns an
     error.

 -- Macro: int SIGXCPU

     CPU time limit exceeded.  This signal is generated when the process
     exceeds its soft resource limit on CPU time.  *Note Limits on
     Resources::.

 -- Macro: int SIGXFSZ

     File size limit exceeded.  This signal is generated when the
     process attempts to extend a file so it exceeds the process’s soft
     resource limit on file size.  *Note Limits on Resources::.


File: libc.info,  Node: Miscellaneous Signals,  Next: Signal Messages,  Prev: Operation Error Signals,  Up: Standard Signals

24.2.7 Miscellaneous Signals
----------------------------

These signals are used for various other purposes.  In general, they
will not affect your program unless it explicitly uses them for
something.

 -- Macro: int SIGUSR1
 -- Macro: int SIGUSR2

     The ‘SIGUSR1’ and ‘SIGUSR2’ signals are set aside for you to use
     any way you want.  They’re useful for simple interprocess
     communication, if you write a signal handler for them in the
     program that receives the signal.

     There is an example showing the use of ‘SIGUSR1’ and ‘SIGUSR2’ in
     *note Signaling Another Process::.

     The default action is to terminate the process.

 -- Macro: int SIGWINCH

     Window size change.  This is generated on some systems (including
     GNU) when the terminal driver’s record of the number of rows and
     columns on the screen is changed.  The default action is to ignore
     it.

     If a program does full-screen display, it should handle ‘SIGWINCH’.
     When the signal arrives, it should fetch the new screen size and
     reformat its display accordingly.

 -- Macro: int SIGINFO

     Information request.  On 4.4 BSD and GNU/Hurd systems, this signal
     is sent to all the processes in the foreground process group of the
     controlling terminal when the user types the STATUS character in
     canonical mode; *note Signal Characters::.

     If the process is the leader of the process group, the default
     action is to print some status information about the system and
     what the process is doing.  Otherwise the default is to do nothing.


File: libc.info,  Node: Signal Messages,  Prev: Miscellaneous Signals,  Up: Standard Signals

24.2.8 Signal Messages
----------------------

We mentioned above that the shell prints a message describing the signal
that terminated a child process.  The clean way to print a message
describing a signal is to use the functions ‘strsignal’ and ‘psignal’.
These functions use a signal number to specify which kind of signal to
describe.  The signal number may come from the termination status of a
child process (*note Process Completion::) or it may come from a signal
handler in the same process.

 -- Function: char * strsignal (int SIGNUM)

     Preliminary: | MT-Unsafe race:strsignal locale | AS-Unsafe init
     i18n corrupt heap | AC-Unsafe init corrupt mem | *Note POSIX Safety
     Concepts::.

     This function returns a pointer to a statically-allocated string
     containing a message describing the signal SIGNUM.  You should not
     modify the contents of this string; and, since it can be rewritten
     on subsequent calls, you should save a copy of it if you need to
     reference it later.

     This function is a GNU extension, declared in the header file
     ‘string.h’.

 -- Function: void psignal (int SIGNUM, const char *MESSAGE)

     Preliminary: | MT-Safe locale | AS-Unsafe corrupt i18n heap |
     AC-Unsafe lock corrupt mem | *Note POSIX Safety Concepts::.

     This function prints a message describing the signal SIGNUM to the
     standard error output stream ‘stderr’; see *note Standard
     Streams::.

     If you call ‘psignal’ with a MESSAGE that is either a null pointer
     or an empty string, ‘psignal’ just prints the message corresponding
     to SIGNUM, adding a trailing newline.

     If you supply a non-null MESSAGE argument, then ‘psignal’ prefixes
     its output with this string.  It adds a colon and a space character
     to separate the MESSAGE from the string corresponding to SIGNUM.

     This function is a BSD feature, declared in the header file
     ‘signal.h’.

 -- Function: const char * sigdescr_np (int SIGNUM)

     | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety Concepts::.

     This function returns the message describing the signal SIGNUM or
     ‘NULL’ for invalid signal number (e.g "Hangup" for ‘SIGHUP’).
     Different than ‘strsignal’ the returned description is not
     translated.  The message points to a static storage whose lifetime
     is the whole lifetime of the program.

     This function is a GNU extension, declared in the header file
     ‘string.h’.

 -- Function: const char * sigabbrev_np (int SIGNUM)

     | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety Concepts::.

     This function returns the abbreviation describing the signal SIGNUM
     or ‘NULL’ for invalid signal number.  The message points to a
     static storage whose lifetime is the whole lifetime of the program.

     This function is a GNU extension, declared in the header file
     ‘string.h’.


File: libc.info,  Node: Signal Actions,  Next: Defining Handlers,  Prev: Standard Signals,  Up: Signal Handling

24.3 Specifying Signal Actions
==============================

The simplest way to change the action for a signal is to use the
‘signal’ function.  You can specify a built-in action (such as to ignore
the signal), or you can “establish a handler”.

   The GNU C Library also implements the more versatile ‘sigaction’
facility.  This section describes both facilities and gives suggestions
on which to use when.

* Menu:

* Basic Signal Handling::       The simple ‘signal’ function.
* Advanced Signal Handling::    The more powerful ‘sigaction’ function.
* Signal and Sigaction::        How those two functions interact.
* Sigaction Function Example::  An example of using the sigaction function.
* Flags for Sigaction::         Specifying options for signal handling.
* Initial Signal Actions::      How programs inherit signal actions.


File: libc.info,  Node: Basic Signal Handling,  Next: Advanced Signal Handling,  Up: Signal Actions

24.3.1 Basic Signal Handling
----------------------------

The ‘signal’ function provides a simple interface for establishing an
action for a particular signal.  The function and associated macros are
declared in the header file ‘signal.h’.

 -- Data Type: sighandler_t

     This is the type of signal handler functions.  Signal handlers take
     one integer argument specifying the signal number, and have return
     type ‘void’.  So, you should define handler functions like this:

          void HANDLER (int signum) { ... }

     The name ‘sighandler_t’ for this data type is a GNU extension.

 -- Function: sighandler_t signal (int SIGNUM, sighandler_t ACTION)

     Preliminary: | MT-Safe sigintr | AS-Safe | AC-Safe | *Note POSIX
     Safety Concepts::.

     The ‘signal’ function establishes ACTION as the action for the
     signal SIGNUM.

     The first argument, SIGNUM, identifies the signal whose behavior
     you want to control, and should be a signal number.  The proper way
     to specify a signal number is with one of the symbolic signal names
     (*note Standard Signals::)—don’t use an explicit number, because
     the numerical code for a given kind of signal may vary from
     operating system to operating system.

     The second argument, ACTION, specifies the action to use for the
     signal SIGNUM.  This can be one of the following:

     ‘SIG_DFL’
          ‘SIG_DFL’ specifies the default action for the particular
          signal.  The default actions for various kinds of signals are
          stated in *note Standard Signals::.

     ‘SIG_IGN’
          ‘SIG_IGN’ specifies that the signal should be ignored.

          Your program generally should not ignore signals that
          represent serious events or that are normally used to request
          termination.  You cannot ignore the ‘SIGKILL’ or ‘SIGSTOP’
          signals at all.  You can ignore program error signals like
          ‘SIGSEGV’, but ignoring the error won’t enable the program to
          continue executing meaningfully.  Ignoring user requests such
          as ‘SIGINT’, ‘SIGQUIT’, and ‘SIGTSTP’ is unfriendly.

          When you do not wish signals to be delivered during a certain
          part of the program, the thing to do is to block them, not
          ignore them.  *Note Blocking Signals::.

     ‘HANDLER’
          Supply the address of a handler function in your program, to
          specify running this handler as the way to deliver the signal.

          For more information about defining signal handler functions,
          see *note Defining Handlers::.

     If you set the action for a signal to ‘SIG_IGN’, or if you set it
     to ‘SIG_DFL’ and the default action is to ignore that signal, then
     any pending signals of that type are discarded (even if they are
     blocked).  Discarding the pending signals means that they will
     never be delivered, not even if you subsequently specify another
     action and unblock this kind of signal.

     The ‘signal’ function returns the action that was previously in
     effect for the specified SIGNUM.  You can save this value and
     restore it later by calling ‘signal’ again.

     If ‘signal’ can’t honor the request, it returns ‘SIG_ERR’ instead.
     The following ‘errno’ error conditions are defined for this
     function:

     ‘EINVAL’
          You specified an invalid SIGNUM; or you tried to ignore or
          provide a handler for ‘SIGKILL’ or ‘SIGSTOP’.

   *Compatibility Note:* A problem encountered when working with the
‘signal’ function is that it has different semantics on BSD and SVID
systems.  The difference is that on SVID systems the signal handler is
deinstalled after signal delivery.  On BSD systems the handler must be
explicitly deinstalled.  In the GNU C Library we use the BSD version by
default.  To use the SVID version you can either use the function
‘sysv_signal’ (see below) or use the ‘_XOPEN_SOURCE’ feature select
macro (*note Feature Test Macros::).  In general, use of these functions
should be avoided because of compatibility problems.  It is better to
use ‘sigaction’ if it is available since the results are much more
reliable.

   Here is a simple example of setting up a handler to delete temporary
files when certain fatal signals happen:

     #include <signal.h>

     void
     termination_handler (int signum)
     {
       struct temp_file *p;

       for (p = temp_file_list; p; p = p->next)
         unlink (p->name);
     }

     int
     main (void)
     {
       ...
       if (signal (SIGINT, termination_handler) == SIG_IGN)
         signal (SIGINT, SIG_IGN);
       if (signal (SIGHUP, termination_handler) == SIG_IGN)
         signal (SIGHUP, SIG_IGN);
       if (signal (SIGTERM, termination_handler) == SIG_IGN)
         signal (SIGTERM, SIG_IGN);
       ...
     }

Note that if a given signal was previously set to be ignored, this code
avoids altering that setting.  This is because non-job-control shells
often ignore certain signals when starting children, and it is important
for the children to respect this.

   We do not handle ‘SIGQUIT’ or the program error signals in this
example because these are designed to provide information for debugging
(a core dump), and the temporary files may give useful information.

 -- Function: sighandler_t sysv_signal (int SIGNUM, sighandler_t ACTION)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     The ‘sysv_signal’ implements the behavior of the standard ‘signal’
     function as found on SVID systems.  The difference to BSD systems
     is that the handler is deinstalled after a delivery of a signal.

     *Compatibility Note:* As said above for ‘signal’, this function
     should be avoided when possible.  ‘sigaction’ is the preferred
     method.

 -- Function: sighandler_t ssignal (int SIGNUM, sighandler_t ACTION)

     Preliminary: | MT-Safe sigintr | AS-Safe | AC-Safe | *Note POSIX
     Safety Concepts::.

     The ‘ssignal’ function does the same thing as ‘signal’; it is
     provided only for compatibility with SVID.

 -- Macro: sighandler_t SIG_ERR

     The value of this macro is used as the return value from ‘signal’
     to indicate an error.


File: libc.info,  Node: Advanced Signal Handling,  Next: Signal and Sigaction,  Prev: Basic Signal Handling,  Up: Signal Actions

24.3.2 Advanced Signal Handling
-------------------------------

The ‘sigaction’ function has the same basic effect as ‘signal’: to
specify how a signal should be handled by the process.  However,
‘sigaction’ offers more control, at the expense of more complexity.  In
particular, ‘sigaction’ allows you to specify additional flags to
control when the signal is generated and how the handler is invoked.

   The ‘sigaction’ function is declared in ‘signal.h’.

 -- Data Type: struct sigaction

     Structures of type ‘struct sigaction’ are used in the ‘sigaction’
     function to specify all the information about how to handle a
     particular signal.  This structure contains at least the following
     members:

     ‘sighandler_t sa_handler’
          This is used in the same way as the ACTION argument to the
          ‘signal’ function.  The value can be ‘SIG_DFL’, ‘SIG_IGN’, or
          a function pointer.  *Note Basic Signal Handling::.

     ‘sigset_t sa_mask’
          This specifies a set of signals to be blocked while the
          handler runs.  Blocking is explained in *note Blocking for
          Handler::.  Note that the signal that was delivered is
          automatically blocked by default before its handler is
          started; this is true regardless of the value in ‘sa_mask’.
          If you want that signal not to be blocked within its handler,
          you must write code in the handler to unblock it.

     ‘int sa_flags’
          This specifies various flags which can affect the behavior of
          the signal.  These are described in more detail in *note Flags
          for Sigaction::.

 -- Function: int sigaction (int SIGNUM, const struct sigaction
          *restrict ACTION, struct sigaction *restrict OLD-ACTION)

     Preliminary: | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety
     Concepts::.

     The ACTION argument is used to set up a new action for the signal
     SIGNUM, while the OLD-ACTION argument is used to return information
     about the action previously associated with this signal.  (In other
     words, OLD-ACTION has the same purpose as the ‘signal’ function’s
     return value—you can check to see what the old action in effect for
     the signal was, and restore it later if you want.)

     Either ACTION or OLD-ACTION can be a null pointer.  If OLD-ACTION
     is a null pointer, this simply suppresses the return of information
     about the old action.  If ACTION is a null pointer, the action
     associated with the signal SIGNUM is unchanged; this allows you to
     inquire about how a signal is being handled without changing that
     handling.

     The return value from ‘sigaction’ is zero if it succeeds, and ‘-1’
     on failure.  The following ‘errno’ error conditions are defined for
     this function:

     ‘EINVAL’
          The SIGNUM argument is not valid, or you are trying to trap or
          ignore ‘SIGKILL’ or ‘SIGSTOP’.


File: libc.info,  Node: Signal and Sigaction,  Next: Sigaction Function Example,  Prev: Advanced Signal Handling,  Up: Signal Actions

24.3.3 Interaction of ‘signal’ and ‘sigaction’
----------------------------------------------

It’s possible to use both the ‘signal’ and ‘sigaction’ functions within
a single program, but you have to be careful because they can interact
in slightly strange ways.

   The ‘sigaction’ function specifies more information than the ‘signal’
function, so the return value from ‘signal’ cannot express the full
range of ‘sigaction’ possibilities.  Therefore, if you use ‘signal’ to
save and later reestablish an action, it may not be able to reestablish
properly a handler that was established with ‘sigaction’.

   To avoid having problems as a result, always use ‘sigaction’ to save
and restore a handler if your program uses ‘sigaction’ at all.  Since
‘sigaction’ is more general, it can properly save and reestablish any
action, regardless of whether it was established originally with
‘signal’ or ‘sigaction’.

   On some systems if you establish an action with ‘signal’ and then
examine it with ‘sigaction’, the handler address that you get may not be
the same as what you specified with ‘signal’.  It may not even be
suitable for use as an action argument with ‘signal’.  But you can rely
on using it as an argument to ‘sigaction’.  This problem never happens
on GNU systems.

   So, you’re better off using one or the other of the mechanisms
consistently within a single program.

   *Portability Note:* The basic ‘signal’ function is a feature of
ISO C, while ‘sigaction’ is part of the POSIX.1 standard.  If you are
concerned about portability to non-POSIX systems, then you should use
the ‘signal’ function instead.


File: libc.info,  Node: Sigaction Function Example,  Next: Flags for Sigaction,  Prev: Signal and Sigaction,  Up: Signal Actions

24.3.4 ‘sigaction’ Function Example
-----------------------------------

In *note Basic Signal Handling::, we gave an example of establishing a
simple handler for termination signals using ‘signal’.  Here is an
equivalent example using ‘sigaction’:

     #include <signal.h>

     void
     termination_handler (int signum)
     {
       struct temp_file *p;

       for (p = temp_file_list; p; p = p->next)
         unlink (p->name);
     }

     int
     main (void)
     {
       ...
       struct sigaction new_action, old_action;

       /* Set up the structure to specify the new action. */
       new_action.sa_handler = termination_handler;
       sigemptyset (&new_action.sa_mask);
       new_action.sa_flags = 0;

       sigaction (SIGINT, NULL, &old_action);
       if (old_action.sa_handler != SIG_IGN)
         sigaction (SIGINT, &new_action, NULL);
       sigaction (SIGHUP, NULL, &old_action);
       if (old_action.sa_handler != SIG_IGN)
         sigaction (SIGHUP, &new_action, NULL);
       sigaction (SIGTERM, NULL, &old_action);
       if (old_action.sa_handler != SIG_IGN)
         sigaction (SIGTERM, &new_action, NULL);
       ...
     }

   The program just loads the ‘new_action’ structure with the desired
parameters and passes it in the ‘sigaction’ call.  The usage of
‘sigemptyset’ is described later; see *note Blocking Signals::.

   As in the example using ‘signal’, we avoid handling signals
previously set to be ignored.  Here we can avoid altering the signal
handler even momentarily, by using the feature of ‘sigaction’ that lets
us examine the current action without specifying a new one.

   Here is another example.  It retrieves information about the current
action for ‘SIGINT’ without changing that action.

     struct sigaction query_action;

     if (sigaction (SIGINT, NULL, &query_action) < 0)
       /* ‘sigaction’ returns -1 in case of error. */
     else if (query_action.sa_handler == SIG_DFL)
       /* ‘SIGINT’ is handled in the default, fatal manner. */
     else if (query_action.sa_handler == SIG_IGN)
       /* ‘SIGINT’ is ignored. */
     else
       /* A programmer-defined signal handler is in effect. */


File: libc.info,  Node: Flags for Sigaction,  Next: Initial Signal Actions,  Prev: Sigaction Function Example,  Up: Signal Actions

24.3.5 Flags for ‘sigaction’
----------------------------

The ‘sa_flags’ member of the ‘sigaction’ structure is a catch-all for
special features.  Most of the time, ‘SA_RESTART’ is a good value to use
for this field.

   The value of ‘sa_flags’ is interpreted as a bit mask.  Thus, you
should choose the flags you want to set, OR those flags together, and
store the result in the ‘sa_flags’ member of your ‘sigaction’ structure.

   Each signal number has its own set of flags.  Each call to
‘sigaction’ affects one particular signal number, and the flags that you
specify apply only to that particular signal.

   In the GNU C Library, establishing a handler with ‘signal’ sets all
the flags to zero except for ‘SA_RESTART’, whose value depends on the
settings you have made with ‘siginterrupt’.  *Note Interrupted
Primitives::, to see what this is about.

   These macros are defined in the header file ‘signal.h’.

 -- Macro: int SA_NOCLDSTOP

     This flag is meaningful only for the ‘SIGCHLD’ signal.  When the
     flag is set, the system delivers the signal for a terminated child
     process but not for one that is stopped.  By default, ‘SIGCHLD’ is
     delivered for both terminated children and stopped children.

     Setting this flag for a signal other than ‘SIGCHLD’ has no effect.

 -- Macro: int SA_ONSTACK

     If this flag is set for a particular signal number, the system uses
     the signal stack when delivering that kind of signal.  *Note Signal
     Stack::.  If a signal with this flag arrives and you have not set a
     signal stack, the system terminates the program with ‘SIGILL’.

 -- Macro: int SA_RESTART

     This flag controls what happens when a signal is delivered during
     certain primitives (such as ‘open’, ‘read’ or ‘write’), and the
     signal handler returns normally.  There are two alternatives: the
     library function can resume, or it can return failure with error
     code ‘EINTR’.

     The choice is controlled by the ‘SA_RESTART’ flag for the
     particular kind of signal that was delivered.  If the flag is set,
     returning from a handler resumes the library function.  If the flag
     is clear, returning from a handler makes the function fail.  *Note
     Interrupted Primitives::.


File: libc.info,  Node: Initial Signal Actions,  Prev: Flags for Sigaction,  Up: Signal Actions

24.3.6 Initial Signal Actions
-----------------------------

When a new process is created (*note Creating a Process::), it inherits
handling of signals from its parent process.  However, when you load a
new process image using the ‘exec’ function (*note Executing a File::),
any signals that you’ve defined your own handlers for revert to their
‘SIG_DFL’ handling.  (If you think about it a little, this makes sense;
the handler functions from the old program are specific to that program,
and aren’t even present in the address space of the new program image.)
Of course, the new program can establish its own handlers.

   When a program is run by a shell, the shell normally sets the initial
actions for the child process to ‘SIG_DFL’ or ‘SIG_IGN’, as appropriate.
It’s a good idea to check to make sure that the shell has not set up an
initial action of ‘SIG_IGN’ before you establish your own signal
handlers.

   Here is an example of how to establish a handler for ‘SIGHUP’, but
not if ‘SIGHUP’ is currently ignored:

     ...
     struct sigaction temp;

     sigaction (SIGHUP, NULL, &temp);

     if (temp.sa_handler != SIG_IGN)
       {
         temp.sa_handler = handle_sighup;
         sigemptyset (&temp.sa_mask);
         sigaction (SIGHUP, &temp, NULL);
       }


File: libc.info,  Node: Defining Handlers,  Next: Interrupted Primitives,  Prev: Signal Actions,  Up: Signal Handling

24.4 Defining Signal Handlers
=============================

This section describes how to write a signal handler function that can
be established with the ‘signal’ or ‘sigaction’ functions.

   A signal handler is just a function that you compile together with
the rest of the program.  Instead of directly invoking the function, you
use ‘signal’ or ‘sigaction’ to tell the operating system to call it when
a signal arrives.  This is known as “establishing” the handler.  *Note
Signal Actions::.

   There are two basic strategies you can use in signal handler
functions:

   • You can have the handler function note that the signal arrived by
     tweaking some global data structures, and then return normally.

   • You can have the handler function terminate the program or transfer
     control to a point where it can recover from the situation that
     caused the signal.

   You need to take special care in writing handler functions because
they can be called asynchronously.  That is, a handler might be called
at any point in the program, unpredictably.  If two signals arrive
during a very short interval, one handler can run within another.  This
section describes what your handler should do, and what you should
avoid.

* Menu:

* Handler Returns::             Handlers that return normally, and what
                                 this means.
* Termination in Handler::      How handler functions terminate a program.
* Longjmp in Handler::          Nonlocal transfer of control out of a
                                 signal handler.
* Signals in Handler::          What happens when signals arrive while
                                 the handler is already occupied.
* Merged Signals::		When a second signal arrives before the
				 first is handled.
* Nonreentrancy::               Do not call any functions unless you know they
                                 are reentrant with respect to signals.
* Atomic Data Access::          A single handler can run in the middle of
                                 reading or writing a single object.

