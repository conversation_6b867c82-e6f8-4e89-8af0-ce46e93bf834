This is libc.info, produced by makeinfo version 6.8 from libc.texinfo.

This is ‘The GNU C Library Reference Manual’, for version 2.33.

   Copyright © 1993–2021 Free Software Foundation, Inc.

   Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being “Free Software Needs Free Documentation” and
“GNU Lesser General Public License”, the Front-Cover texts being “A GNU
Manual”, and with the Back-Cover Texts as in (a) below.  A copy of the
license is included in the section entitled "GNU Free Documentation
License".

   (a) The FSF’s Back-Cover Text is: “You have the freedom to copy and
modify this GNU manual.  Buying copies from the FSF supports it in
developing GNU and promoting software freedom.”
INFO-DIR-SECTION Software libraries
START-INFO-DIR-ENTRY
* Libc: (libc).                 C library.
END-INFO-DIR-ENTRY

INFO-DIR-SECTION GNU C library functions and macros
START-INFO-DIR-ENTRY
* ALTWERASE: (libc)Local Modes.
* ARGP_ERR_UNKNOWN: (libc)Argp Parser Functions.
* ARG_MAX: (libc)General Limits.
* BC_BASE_MAX: (libc)Utility Limits.
* BC_DIM_MAX: (libc)Utility Limits.
* BC_SCALE_MAX: (libc)Utility Limits.
* BC_STRING_MAX: (libc)Utility Limits.
* BRKINT: (libc)Input Modes.
* BUFSIZ: (libc)Controlling Buffering.
* CCTS_OFLOW: (libc)Control Modes.
* CHAR_BIT: (libc)Width of Type.
* CHILD_MAX: (libc)General Limits.
* CIGNORE: (libc)Control Modes.
* CLK_TCK: (libc)Processor Time.
* CLOCAL: (libc)Control Modes.
* CLOCKS_PER_SEC: (libc)CPU Time.
* CLOCK_MONOTONIC: (libc)Getting the Time.
* CLOCK_REALTIME: (libc)Getting the Time.
* COLL_WEIGHTS_MAX: (libc)Utility Limits.
* CPU_CLR: (libc)CPU Affinity.
* CPU_FEATURE_USABLE: (libc)X86.
* CPU_ISSET: (libc)CPU Affinity.
* CPU_SET: (libc)CPU Affinity.
* CPU_SETSIZE: (libc)CPU Affinity.
* CPU_ZERO: (libc)CPU Affinity.
* CREAD: (libc)Control Modes.
* CRTS_IFLOW: (libc)Control Modes.
* CS5: (libc)Control Modes.
* CS6: (libc)Control Modes.
* CS7: (libc)Control Modes.
* CS8: (libc)Control Modes.
* CSIZE: (libc)Control Modes.
* CSTOPB: (libc)Control Modes.
* DTTOIF: (libc)Directory Entries.
* E2BIG: (libc)Error Codes.
* EACCES: (libc)Error Codes.
* EADDRINUSE: (libc)Error Codes.
* EADDRNOTAVAIL: (libc)Error Codes.
* EADV: (libc)Error Codes.
* EAFNOSUPPORT: (libc)Error Codes.
* EAGAIN: (libc)Error Codes.
* EALREADY: (libc)Error Codes.
* EAUTH: (libc)Error Codes.
* EBACKGROUND: (libc)Error Codes.
* EBADE: (libc)Error Codes.
* EBADF: (libc)Error Codes.
* EBADFD: (libc)Error Codes.
* EBADMSG: (libc)Error Codes.
* EBADR: (libc)Error Codes.
* EBADRPC: (libc)Error Codes.
* EBADRQC: (libc)Error Codes.
* EBADSLT: (libc)Error Codes.
* EBFONT: (libc)Error Codes.
* EBUSY: (libc)Error Codes.
* ECANCELED: (libc)Error Codes.
* ECHILD: (libc)Error Codes.
* ECHO: (libc)Local Modes.
* ECHOCTL: (libc)Local Modes.
* ECHOE: (libc)Local Modes.
* ECHOK: (libc)Local Modes.
* ECHOKE: (libc)Local Modes.
* ECHONL: (libc)Local Modes.
* ECHOPRT: (libc)Local Modes.
* ECHRNG: (libc)Error Codes.
* ECOMM: (libc)Error Codes.
* ECONNABORTED: (libc)Error Codes.
* ECONNREFUSED: (libc)Error Codes.
* ECONNRESET: (libc)Error Codes.
* ED: (libc)Error Codes.
* EDEADLK: (libc)Error Codes.
* EDEADLOCK: (libc)Error Codes.
* EDESTADDRREQ: (libc)Error Codes.
* EDIED: (libc)Error Codes.
* EDOM: (libc)Error Codes.
* EDOTDOT: (libc)Error Codes.
* EDQUOT: (libc)Error Codes.
* EEXIST: (libc)Error Codes.
* EFAULT: (libc)Error Codes.
* EFBIG: (libc)Error Codes.
* EFTYPE: (libc)Error Codes.
* EGRATUITOUS: (libc)Error Codes.
* EGREGIOUS: (libc)Error Codes.
* EHOSTDOWN: (libc)Error Codes.
* EHOSTUNREACH: (libc)Error Codes.
* EHWPOISON: (libc)Error Codes.
* EIDRM: (libc)Error Codes.
* EIEIO: (libc)Error Codes.
* EILSEQ: (libc)Error Codes.
* EINPROGRESS: (libc)Error Codes.
* EINTR: (libc)Error Codes.
* EINVAL: (libc)Error Codes.
* EIO: (libc)Error Codes.
* EISCONN: (libc)Error Codes.
* EISDIR: (libc)Error Codes.
* EISNAM: (libc)Error Codes.
* EKEYEXPIRED: (libc)Error Codes.
* EKEYREJECTED: (libc)Error Codes.
* EKEYREVOKED: (libc)Error Codes.
* EL2HLT: (libc)Error Codes.
* EL2NSYNC: (libc)Error Codes.
* EL3HLT: (libc)Error Codes.
* EL3RST: (libc)Error Codes.
* ELIBACC: (libc)Error Codes.
* ELIBBAD: (libc)Error Codes.
* ELIBEXEC: (libc)Error Codes.
* ELIBMAX: (libc)Error Codes.
* ELIBSCN: (libc)Error Codes.
* ELNRNG: (libc)Error Codes.
* ELOOP: (libc)Error Codes.
* EMEDIUMTYPE: (libc)Error Codes.
* EMFILE: (libc)Error Codes.
* EMLINK: (libc)Error Codes.
* EMSGSIZE: (libc)Error Codes.
* EMULTIHOP: (libc)Error Codes.
* ENAMETOOLONG: (libc)Error Codes.
* ENAVAIL: (libc)Error Codes.
* ENEEDAUTH: (libc)Error Codes.
* ENETDOWN: (libc)Error Codes.
* ENETRESET: (libc)Error Codes.
* ENETUNREACH: (libc)Error Codes.
* ENFILE: (libc)Error Codes.
* ENOANO: (libc)Error Codes.
* ENOBUFS: (libc)Error Codes.
* ENOCSI: (libc)Error Codes.
* ENODATA: (libc)Error Codes.
* ENODEV: (libc)Error Codes.
* ENOENT: (libc)Error Codes.
* ENOEXEC: (libc)Error Codes.
* ENOKEY: (libc)Error Codes.
* ENOLCK: (libc)Error Codes.
* ENOLINK: (libc)Error Codes.
* ENOMEDIUM: (libc)Error Codes.
* ENOMEM: (libc)Error Codes.
* ENOMSG: (libc)Error Codes.
* ENONET: (libc)Error Codes.
* ENOPKG: (libc)Error Codes.
* ENOPROTOOPT: (libc)Error Codes.
* ENOSPC: (libc)Error Codes.
* ENOSR: (libc)Error Codes.
* ENOSTR: (libc)Error Codes.
* ENOSYS: (libc)Error Codes.
* ENOTBLK: (libc)Error Codes.
* ENOTCONN: (libc)Error Codes.
* ENOTDIR: (libc)Error Codes.
* ENOTEMPTY: (libc)Error Codes.
* ENOTNAM: (libc)Error Codes.
* ENOTRECOVERABLE: (libc)Error Codes.
* ENOTSOCK: (libc)Error Codes.
* ENOTSUP: (libc)Error Codes.
* ENOTTY: (libc)Error Codes.
* ENOTUNIQ: (libc)Error Codes.
* ENXIO: (libc)Error Codes.
* EOF: (libc)EOF and Errors.
* EOPNOTSUPP: (libc)Error Codes.
* EOVERFLOW: (libc)Error Codes.
* EOWNERDEAD: (libc)Error Codes.
* EPERM: (libc)Error Codes.
* EPFNOSUPPORT: (libc)Error Codes.
* EPIPE: (libc)Error Codes.
* EPROCLIM: (libc)Error Codes.
* EPROCUNAVAIL: (libc)Error Codes.
* EPROGMISMATCH: (libc)Error Codes.
* EPROGUNAVAIL: (libc)Error Codes.
* EPROTO: (libc)Error Codes.
* EPROTONOSUPPORT: (libc)Error Codes.
* EPROTOTYPE: (libc)Error Codes.
* EQUIV_CLASS_MAX: (libc)Utility Limits.
* ERANGE: (libc)Error Codes.
* EREMCHG: (libc)Error Codes.
* EREMOTE: (libc)Error Codes.
* EREMOTEIO: (libc)Error Codes.
* ERESTART: (libc)Error Codes.
* ERFKILL: (libc)Error Codes.
* EROFS: (libc)Error Codes.
* ERPCMISMATCH: (libc)Error Codes.
* ESHUTDOWN: (libc)Error Codes.
* ESOCKTNOSUPPORT: (libc)Error Codes.
* ESPIPE: (libc)Error Codes.
* ESRCH: (libc)Error Codes.
* ESRMNT: (libc)Error Codes.
* ESTALE: (libc)Error Codes.
* ESTRPIPE: (libc)Error Codes.
* ETIME: (libc)Error Codes.
* ETIMEDOUT: (libc)Error Codes.
* ETOOMANYREFS: (libc)Error Codes.
* ETXTBSY: (libc)Error Codes.
* EUCLEAN: (libc)Error Codes.
* EUNATCH: (libc)Error Codes.
* EUSERS: (libc)Error Codes.
* EWOULDBLOCK: (libc)Error Codes.
* EXDEV: (libc)Error Codes.
* EXFULL: (libc)Error Codes.
* EXIT_FAILURE: (libc)Exit Status.
* EXIT_SUCCESS: (libc)Exit Status.
* EXPR_NEST_MAX: (libc)Utility Limits.
* FD_CLOEXEC: (libc)Descriptor Flags.
* FD_CLR: (libc)Waiting for I/O.
* FD_ISSET: (libc)Waiting for I/O.
* FD_SET: (libc)Waiting for I/O.
* FD_SETSIZE: (libc)Waiting for I/O.
* FD_ZERO: (libc)Waiting for I/O.
* FE_SNANS_ALWAYS_SIGNAL: (libc)Infinity and NaN.
* FILENAME_MAX: (libc)Limits for Files.
* FLUSHO: (libc)Local Modes.
* FOPEN_MAX: (libc)Opening Streams.
* FP_ILOGB0: (libc)Exponents and Logarithms.
* FP_ILOGBNAN: (libc)Exponents and Logarithms.
* FP_LLOGB0: (libc)Exponents and Logarithms.
* FP_LLOGBNAN: (libc)Exponents and Logarithms.
* F_DUPFD: (libc)Duplicating Descriptors.
* F_GETFD: (libc)Descriptor Flags.
* F_GETFL: (libc)Getting File Status Flags.
* F_GETLK: (libc)File Locks.
* F_GETOWN: (libc)Interrupt Input.
* F_OFD_GETLK: (libc)Open File Description Locks.
* F_OFD_SETLK: (libc)Open File Description Locks.
* F_OFD_SETLKW: (libc)Open File Description Locks.
* F_OK: (libc)Testing File Access.
* F_SETFD: (libc)Descriptor Flags.
* F_SETFL: (libc)Getting File Status Flags.
* F_SETLK: (libc)File Locks.
* F_SETLKW: (libc)File Locks.
* F_SETOWN: (libc)Interrupt Input.
* HAS_CPU_FEATURE: (libc)X86.
* HUGE_VAL: (libc)Math Error Reporting.
* HUGE_VALF: (libc)Math Error Reporting.
* HUGE_VALL: (libc)Math Error Reporting.
* HUGE_VAL_FN: (libc)Math Error Reporting.
* HUGE_VAL_FNx: (libc)Math Error Reporting.
* HUPCL: (libc)Control Modes.
* I: (libc)Complex Numbers.
* ICANON: (libc)Local Modes.
* ICRNL: (libc)Input Modes.
* IEXTEN: (libc)Local Modes.
* IFNAMSIZ: (libc)Interface Naming.
* IFTODT: (libc)Directory Entries.
* IGNBRK: (libc)Input Modes.
* IGNCR: (libc)Input Modes.
* IGNPAR: (libc)Input Modes.
* IMAXBEL: (libc)Input Modes.
* INADDR_ANY: (libc)Host Address Data Type.
* INADDR_BROADCAST: (libc)Host Address Data Type.
* INADDR_LOOPBACK: (libc)Host Address Data Type.
* INADDR_NONE: (libc)Host Address Data Type.
* INFINITY: (libc)Infinity and NaN.
* INLCR: (libc)Input Modes.
* INPCK: (libc)Input Modes.
* IPPORT_RESERVED: (libc)Ports.
* IPPORT_USERRESERVED: (libc)Ports.
* ISIG: (libc)Local Modes.
* ISTRIP: (libc)Input Modes.
* IXANY: (libc)Input Modes.
* IXOFF: (libc)Input Modes.
* IXON: (libc)Input Modes.
* LINE_MAX: (libc)Utility Limits.
* LINK_MAX: (libc)Limits for Files.
* L_ctermid: (libc)Identifying the Terminal.
* L_cuserid: (libc)Who Logged In.
* L_tmpnam: (libc)Temporary Files.
* MAXNAMLEN: (libc)Limits for Files.
* MAXSYMLINKS: (libc)Symbolic Links.
* MAX_CANON: (libc)Limits for Files.
* MAX_INPUT: (libc)Limits for Files.
* MB_CUR_MAX: (libc)Selecting the Conversion.
* MB_LEN_MAX: (libc)Selecting the Conversion.
* MDMBUF: (libc)Control Modes.
* MSG_DONTROUTE: (libc)Socket Data Options.
* MSG_OOB: (libc)Socket Data Options.
* MSG_PEEK: (libc)Socket Data Options.
* NAME_MAX: (libc)Limits for Files.
* NAN: (libc)Infinity and NaN.
* NCCS: (libc)Mode Data Types.
* NGROUPS_MAX: (libc)General Limits.
* NOFLSH: (libc)Local Modes.
* NOKERNINFO: (libc)Local Modes.
* NSIG: (libc)Standard Signals.
* NULL: (libc)Null Pointer Constant.
* ONLCR: (libc)Output Modes.
* ONOEOT: (libc)Output Modes.
* OPEN_MAX: (libc)General Limits.
* OPOST: (libc)Output Modes.
* OXTABS: (libc)Output Modes.
* O_ACCMODE: (libc)Access Modes.
* O_APPEND: (libc)Operating Modes.
* O_ASYNC: (libc)Operating Modes.
* O_CREAT: (libc)Open-time Flags.
* O_DIRECTORY: (libc)Open-time Flags.
* O_EXCL: (libc)Open-time Flags.
* O_EXEC: (libc)Access Modes.
* O_EXLOCK: (libc)Open-time Flags.
* O_FSYNC: (libc)Operating Modes.
* O_IGNORE_CTTY: (libc)Open-time Flags.
* O_NDELAY: (libc)Operating Modes.
* O_NOATIME: (libc)Operating Modes.
* O_NOCTTY: (libc)Open-time Flags.
* O_NOFOLLOW: (libc)Open-time Flags.
* O_NOLINK: (libc)Open-time Flags.
* O_NONBLOCK: (libc)Open-time Flags.
* O_NONBLOCK: (libc)Operating Modes.
* O_NOTRANS: (libc)Open-time Flags.
* O_PATH: (libc)Access Modes.
* O_RDONLY: (libc)Access Modes.
* O_RDWR: (libc)Access Modes.
* O_READ: (libc)Access Modes.
* O_SHLOCK: (libc)Open-time Flags.
* O_SYNC: (libc)Operating Modes.
* O_TMPFILE: (libc)Open-time Flags.
* O_TRUNC: (libc)Open-time Flags.
* O_WRITE: (libc)Access Modes.
* O_WRONLY: (libc)Access Modes.
* PARENB: (libc)Control Modes.
* PARMRK: (libc)Input Modes.
* PARODD: (libc)Control Modes.
* PATH_MAX: (libc)Limits for Files.
* PA_FLAG_MASK: (libc)Parsing a Template String.
* PENDIN: (libc)Local Modes.
* PF_FILE: (libc)Local Namespace Details.
* PF_INET6: (libc)Internet Namespace.
* PF_INET: (libc)Internet Namespace.
* PF_LOCAL: (libc)Local Namespace Details.
* PF_UNIX: (libc)Local Namespace Details.
* PIPE_BUF: (libc)Limits for Files.
* PTHREAD_ATTR_NO_SIGMASK_NP: (libc)Initial Thread Signal Mask.
* P_tmpdir: (libc)Temporary Files.
* RAND_MAX: (libc)ISO Random.
* RE_DUP_MAX: (libc)General Limits.
* RLIM_INFINITY: (libc)Limits on Resources.
* R_OK: (libc)Testing File Access.
* SA_NOCLDSTOP: (libc)Flags for Sigaction.
* SA_ONSTACK: (libc)Flags for Sigaction.
* SA_RESTART: (libc)Flags for Sigaction.
* SEEK_CUR: (libc)File Positioning.
* SEEK_END: (libc)File Positioning.
* SEEK_SET: (libc)File Positioning.
* SIGABRT: (libc)Program Error Signals.
* SIGALRM: (libc)Alarm Signals.
* SIGBUS: (libc)Program Error Signals.
* SIGCHLD: (libc)Job Control Signals.
* SIGCLD: (libc)Job Control Signals.
* SIGCONT: (libc)Job Control Signals.
* SIGEMT: (libc)Program Error Signals.
* SIGFPE: (libc)Program Error Signals.
* SIGHUP: (libc)Termination Signals.
* SIGILL: (libc)Program Error Signals.
* SIGINFO: (libc)Miscellaneous Signals.
* SIGINT: (libc)Termination Signals.
* SIGIO: (libc)Asynchronous I/O Signals.
* SIGIOT: (libc)Program Error Signals.
* SIGKILL: (libc)Termination Signals.
* SIGLOST: (libc)Operation Error Signals.
* SIGPIPE: (libc)Operation Error Signals.
* SIGPOLL: (libc)Asynchronous I/O Signals.
* SIGPROF: (libc)Alarm Signals.
* SIGQUIT: (libc)Termination Signals.
* SIGSEGV: (libc)Program Error Signals.
* SIGSTOP: (libc)Job Control Signals.
* SIGSYS: (libc)Program Error Signals.
* SIGTERM: (libc)Termination Signals.
* SIGTRAP: (libc)Program Error Signals.
* SIGTSTP: (libc)Job Control Signals.
* SIGTTIN: (libc)Job Control Signals.
* SIGTTOU: (libc)Job Control Signals.
* SIGURG: (libc)Asynchronous I/O Signals.
* SIGUSR1: (libc)Miscellaneous Signals.
* SIGUSR2: (libc)Miscellaneous Signals.
* SIGVTALRM: (libc)Alarm Signals.
* SIGWINCH: (libc)Miscellaneous Signals.
* SIGXCPU: (libc)Operation Error Signals.
* SIGXFSZ: (libc)Operation Error Signals.
* SIG_ERR: (libc)Basic Signal Handling.
* SNAN: (libc)Infinity and NaN.
* SNANF: (libc)Infinity and NaN.
* SNANFN: (libc)Infinity and NaN.
* SNANFNx: (libc)Infinity and NaN.
* SNANL: (libc)Infinity and NaN.
* SOCK_DGRAM: (libc)Communication Styles.
* SOCK_RAW: (libc)Communication Styles.
* SOCK_RDM: (libc)Communication Styles.
* SOCK_SEQPACKET: (libc)Communication Styles.
* SOCK_STREAM: (libc)Communication Styles.
* SOL_SOCKET: (libc)Socket-Level Options.
* SSIZE_MAX: (libc)General Limits.
* STREAM_MAX: (libc)General Limits.
* SUN_LEN: (libc)Local Namespace Details.
* S_IFMT: (libc)Testing File Type.
* S_ISBLK: (libc)Testing File Type.
* S_ISCHR: (libc)Testing File Type.
* S_ISDIR: (libc)Testing File Type.
* S_ISFIFO: (libc)Testing File Type.
* S_ISLNK: (libc)Testing File Type.
* S_ISREG: (libc)Testing File Type.
* S_ISSOCK: (libc)Testing File Type.
* S_TYPEISMQ: (libc)Testing File Type.
* S_TYPEISSEM: (libc)Testing File Type.
* S_TYPEISSHM: (libc)Testing File Type.
* TMP_MAX: (libc)Temporary Files.
* TOSTOP: (libc)Local Modes.
* TZNAME_MAX: (libc)General Limits.
* VDISCARD: (libc)Other Special.
* VDSUSP: (libc)Signal Characters.
* VEOF: (libc)Editing Characters.
* VEOL2: (libc)Editing Characters.
* VEOL: (libc)Editing Characters.
* VERASE: (libc)Editing Characters.
* VINTR: (libc)Signal Characters.
* VKILL: (libc)Editing Characters.
* VLNEXT: (libc)Other Special.
* VMIN: (libc)Noncanonical Input.
* VQUIT: (libc)Signal Characters.
* VREPRINT: (libc)Editing Characters.
* VSTART: (libc)Start/Stop Characters.
* VSTATUS: (libc)Other Special.
* VSTOP: (libc)Start/Stop Characters.
* VSUSP: (libc)Signal Characters.
* VTIME: (libc)Noncanonical Input.
* VWERASE: (libc)Editing Characters.
* WCHAR_MAX: (libc)Extended Char Intro.
* WCHAR_MIN: (libc)Extended Char Intro.
* WCOREDUMP: (libc)Process Completion Status.
* WEOF: (libc)EOF and Errors.
* WEOF: (libc)Extended Char Intro.
* WEXITSTATUS: (libc)Process Completion Status.
* WIFEXITED: (libc)Process Completion Status.
* WIFSIGNALED: (libc)Process Completion Status.
* WIFSTOPPED: (libc)Process Completion Status.
* WSTOPSIG: (libc)Process Completion Status.
* WTERMSIG: (libc)Process Completion Status.
* W_OK: (libc)Testing File Access.
* X_OK: (libc)Testing File Access.
* _Complex_I: (libc)Complex Numbers.
* _Exit: (libc)Termination Internals.
* _IOFBF: (libc)Controlling Buffering.
* _IOLBF: (libc)Controlling Buffering.
* _IONBF: (libc)Controlling Buffering.
* _Imaginary_I: (libc)Complex Numbers.
* _PATH_UTMP: (libc)Manipulating the Database.
* _PATH_WTMP: (libc)Manipulating the Database.
* _POSIX2_C_DEV: (libc)System Options.
* _POSIX2_C_VERSION: (libc)Version Supported.
* _POSIX2_FORT_DEV: (libc)System Options.
* _POSIX2_FORT_RUN: (libc)System Options.
* _POSIX2_LOCALEDEF: (libc)System Options.
* _POSIX2_SW_DEV: (libc)System Options.
* _POSIX_CHOWN_RESTRICTED: (libc)Options for Files.
* _POSIX_JOB_CONTROL: (libc)System Options.
* _POSIX_NO_TRUNC: (libc)Options for Files.
* _POSIX_SAVED_IDS: (libc)System Options.
* _POSIX_VDISABLE: (libc)Options for Files.
* _POSIX_VERSION: (libc)Version Supported.
* __fbufsize: (libc)Controlling Buffering.
* __flbf: (libc)Controlling Buffering.
* __fpending: (libc)Controlling Buffering.
* __fpurge: (libc)Flushing Buffers.
* __freadable: (libc)Opening Streams.
* __freading: (libc)Opening Streams.
* __fsetlocking: (libc)Streams and Threads.
* __fwritable: (libc)Opening Streams.
* __fwriting: (libc)Opening Streams.
* __gconv_end_fct: (libc)glibc iconv Implementation.
* __gconv_fct: (libc)glibc iconv Implementation.
* __gconv_init_fct: (libc)glibc iconv Implementation.
* __ppc_get_timebase: (libc)PowerPC.
* __ppc_get_timebase_freq: (libc)PowerPC.
* __ppc_mdoio: (libc)PowerPC.
* __ppc_mdoom: (libc)PowerPC.
* __ppc_set_ppr_low: (libc)PowerPC.
* __ppc_set_ppr_med: (libc)PowerPC.
* __ppc_set_ppr_med_high: (libc)PowerPC.
* __ppc_set_ppr_med_low: (libc)PowerPC.
* __ppc_set_ppr_very_low: (libc)PowerPC.
* __ppc_yield: (libc)PowerPC.
* __riscv_flush_icache: (libc)RISC-V.
* __va_copy: (libc)Argument Macros.
* __x86_get_cpuid_feature_leaf: (libc)X86.
* _exit: (libc)Termination Internals.
* _flushlbf: (libc)Flushing Buffers.
* _tolower: (libc)Case Conversion.
* _toupper: (libc)Case Conversion.
* a64l: (libc)Encode Binary Data.
* abort: (libc)Aborting a Program.
* abs: (libc)Absolute Value.
* accept: (libc)Accepting Connections.
* access: (libc)Testing File Access.
* acos: (libc)Inverse Trig Functions.
* acosf: (libc)Inverse Trig Functions.
* acosfN: (libc)Inverse Trig Functions.
* acosfNx: (libc)Inverse Trig Functions.
* acosh: (libc)Hyperbolic Functions.
* acoshf: (libc)Hyperbolic Functions.
* acoshfN: (libc)Hyperbolic Functions.
* acoshfNx: (libc)Hyperbolic Functions.
* acoshl: (libc)Hyperbolic Functions.
* acosl: (libc)Inverse Trig Functions.
* addmntent: (libc)mtab.
* addseverity: (libc)Adding Severity Classes.
* adjtime: (libc)Setting and Adjusting the Time.
* adjtimex: (libc)Setting and Adjusting the Time.
* aio_cancel64: (libc)Cancel AIO Operations.
* aio_cancel: (libc)Cancel AIO Operations.
* aio_error64: (libc)Status of AIO Operations.
* aio_error: (libc)Status of AIO Operations.
* aio_fsync64: (libc)Synchronizing AIO Operations.
* aio_fsync: (libc)Synchronizing AIO Operations.
* aio_init: (libc)Configuration of AIO.
* aio_read64: (libc)Asynchronous Reads/Writes.
* aio_read: (libc)Asynchronous Reads/Writes.
* aio_return64: (libc)Status of AIO Operations.
* aio_return: (libc)Status of AIO Operations.
* aio_suspend64: (libc)Synchronizing AIO Operations.
* aio_suspend: (libc)Synchronizing AIO Operations.
* aio_write64: (libc)Asynchronous Reads/Writes.
* aio_write: (libc)Asynchronous Reads/Writes.
* alarm: (libc)Setting an Alarm.
* aligned_alloc: (libc)Aligned Memory Blocks.
* alloca: (libc)Variable Size Automatic.
* alphasort64: (libc)Scanning Directory Content.
* alphasort: (libc)Scanning Directory Content.
* argp_error: (libc)Argp Helper Functions.
* argp_failure: (libc)Argp Helper Functions.
* argp_help: (libc)Argp Help.
* argp_parse: (libc)Argp.
* argp_state_help: (libc)Argp Helper Functions.
* argp_usage: (libc)Argp Helper Functions.
* argz_add: (libc)Argz Functions.
* argz_add_sep: (libc)Argz Functions.
* argz_append: (libc)Argz Functions.
* argz_count: (libc)Argz Functions.
* argz_create: (libc)Argz Functions.
* argz_create_sep: (libc)Argz Functions.
* argz_delete: (libc)Argz Functions.
* argz_extract: (libc)Argz Functions.
* argz_insert: (libc)Argz Functions.
* argz_next: (libc)Argz Functions.
* argz_replace: (libc)Argz Functions.
* argz_stringify: (libc)Argz Functions.
* asctime: (libc)Formatting Calendar Time.
* asctime_r: (libc)Formatting Calendar Time.
* asin: (libc)Inverse Trig Functions.
* asinf: (libc)Inverse Trig Functions.
* asinfN: (libc)Inverse Trig Functions.
* asinfNx: (libc)Inverse Trig Functions.
* asinh: (libc)Hyperbolic Functions.
* asinhf: (libc)Hyperbolic Functions.
* asinhfN: (libc)Hyperbolic Functions.
* asinhfNx: (libc)Hyperbolic Functions.
* asinhl: (libc)Hyperbolic Functions.
* asinl: (libc)Inverse Trig Functions.
* asprintf: (libc)Dynamic Output.
* assert: (libc)Consistency Checking.
* assert_perror: (libc)Consistency Checking.
* atan2: (libc)Inverse Trig Functions.
* atan2f: (libc)Inverse Trig Functions.
* atan2fN: (libc)Inverse Trig Functions.
* atan2fNx: (libc)Inverse Trig Functions.
* atan2l: (libc)Inverse Trig Functions.
* atan: (libc)Inverse Trig Functions.
* atanf: (libc)Inverse Trig Functions.
* atanfN: (libc)Inverse Trig Functions.
* atanfNx: (libc)Inverse Trig Functions.
* atanh: (libc)Hyperbolic Functions.
* atanhf: (libc)Hyperbolic Functions.
* atanhfN: (libc)Hyperbolic Functions.
* atanhfNx: (libc)Hyperbolic Functions.
* atanhl: (libc)Hyperbolic Functions.
* atanl: (libc)Inverse Trig Functions.
* atexit: (libc)Cleanups on Exit.
* atof: (libc)Parsing of Floats.
* atoi: (libc)Parsing of Integers.
* atol: (libc)Parsing of Integers.
* atoll: (libc)Parsing of Integers.
* backtrace: (libc)Backtraces.
* backtrace_symbols: (libc)Backtraces.
* backtrace_symbols_fd: (libc)Backtraces.
* basename: (libc)Finding Tokens in a String.
* basename: (libc)Finding Tokens in a String.
* bcmp: (libc)String/Array Comparison.
* bcopy: (libc)Copying Strings and Arrays.
* bind: (libc)Setting Address.
* bind_textdomain_codeset: (libc)Charset conversion in gettext.
* bindtextdomain: (libc)Locating gettext catalog.
* brk: (libc)Resizing the Data Segment.
* bsearch: (libc)Array Search Function.
* btowc: (libc)Converting a Character.
* bzero: (libc)Copying Strings and Arrays.
* cabs: (libc)Absolute Value.
* cabsf: (libc)Absolute Value.
* cabsfN: (libc)Absolute Value.
* cabsfNx: (libc)Absolute Value.
* cabsl: (libc)Absolute Value.
* cacos: (libc)Inverse Trig Functions.
* cacosf: (libc)Inverse Trig Functions.
* cacosfN: (libc)Inverse Trig Functions.
* cacosfNx: (libc)Inverse Trig Functions.
* cacosh: (libc)Hyperbolic Functions.
* cacoshf: (libc)Hyperbolic Functions.
* cacoshfN: (libc)Hyperbolic Functions.
* cacoshfNx: (libc)Hyperbolic Functions.
* cacoshl: (libc)Hyperbolic Functions.
* cacosl: (libc)Inverse Trig Functions.
* call_once: (libc)Call Once.
* calloc: (libc)Allocating Cleared Space.
* canonicalize: (libc)FP Bit Twiddling.
* canonicalize_file_name: (libc)Symbolic Links.
* canonicalizef: (libc)FP Bit Twiddling.
* canonicalizefN: (libc)FP Bit Twiddling.
* canonicalizefNx: (libc)FP Bit Twiddling.
* canonicalizel: (libc)FP Bit Twiddling.
* carg: (libc)Operations on Complex.
* cargf: (libc)Operations on Complex.
* cargfN: (libc)Operations on Complex.
* cargfNx: (libc)Operations on Complex.
* cargl: (libc)Operations on Complex.
* casin: (libc)Inverse Trig Functions.
* casinf: (libc)Inverse Trig Functions.
* casinfN: (libc)Inverse Trig Functions.
* casinfNx: (libc)Inverse Trig Functions.
* casinh: (libc)Hyperbolic Functions.
* casinhf: (libc)Hyperbolic Functions.
* casinhfN: (libc)Hyperbolic Functions.
* casinhfNx: (libc)Hyperbolic Functions.
* casinhl: (libc)Hyperbolic Functions.
* casinl: (libc)Inverse Trig Functions.
* catan: (libc)Inverse Trig Functions.
* catanf: (libc)Inverse Trig Functions.
* catanfN: (libc)Inverse Trig Functions.
* catanfNx: (libc)Inverse Trig Functions.
* catanh: (libc)Hyperbolic Functions.
* catanhf: (libc)Hyperbolic Functions.
* catanhfN: (libc)Hyperbolic Functions.
* catanhfNx: (libc)Hyperbolic Functions.
* catanhl: (libc)Hyperbolic Functions.
* catanl: (libc)Inverse Trig Functions.
* catclose: (libc)The catgets Functions.
* catgets: (libc)The catgets Functions.
* catopen: (libc)The catgets Functions.
* cbrt: (libc)Exponents and Logarithms.
* cbrtf: (libc)Exponents and Logarithms.
* cbrtfN: (libc)Exponents and Logarithms.
* cbrtfNx: (libc)Exponents and Logarithms.
* cbrtl: (libc)Exponents and Logarithms.
* ccos: (libc)Trig Functions.
* ccosf: (libc)Trig Functions.
* ccosfN: (libc)Trig Functions.
* ccosfNx: (libc)Trig Functions.
* ccosh: (libc)Hyperbolic Functions.
* ccoshf: (libc)Hyperbolic Functions.
* ccoshfN: (libc)Hyperbolic Functions.
* ccoshfNx: (libc)Hyperbolic Functions.
* ccoshl: (libc)Hyperbolic Functions.
* ccosl: (libc)Trig Functions.
* ceil: (libc)Rounding Functions.
* ceilf: (libc)Rounding Functions.
* ceilfN: (libc)Rounding Functions.
* ceilfNx: (libc)Rounding Functions.
* ceill: (libc)Rounding Functions.
* cexp: (libc)Exponents and Logarithms.
* cexpf: (libc)Exponents and Logarithms.
* cexpfN: (libc)Exponents and Logarithms.
* cexpfNx: (libc)Exponents and Logarithms.
* cexpl: (libc)Exponents and Logarithms.
* cfgetispeed: (libc)Line Speed.
* cfgetospeed: (libc)Line Speed.
* cfmakeraw: (libc)Noncanonical Input.
* cfsetispeed: (libc)Line Speed.
* cfsetospeed: (libc)Line Speed.
* cfsetspeed: (libc)Line Speed.
* chdir: (libc)Working Directory.
* chmod: (libc)Setting Permissions.
* chown: (libc)File Owner.
* cimag: (libc)Operations on Complex.
* cimagf: (libc)Operations on Complex.
* cimagfN: (libc)Operations on Complex.
* cimagfNx: (libc)Operations on Complex.
* cimagl: (libc)Operations on Complex.
* clearenv: (libc)Environment Access.
* clearerr: (libc)Error Recovery.
* clearerr_unlocked: (libc)Error Recovery.
* clock: (libc)CPU Time.
* clock_getres: (libc)Getting the Time.
* clock_gettime: (libc)Getting the Time.
* clock_settime: (libc)Setting and Adjusting the Time.
* clog10: (libc)Exponents and Logarithms.
* clog10f: (libc)Exponents and Logarithms.
* clog10fN: (libc)Exponents and Logarithms.
* clog10fNx: (libc)Exponents and Logarithms.
* clog10l: (libc)Exponents and Logarithms.
* clog: (libc)Exponents and Logarithms.
* clogf: (libc)Exponents and Logarithms.
* clogfN: (libc)Exponents and Logarithms.
* clogfNx: (libc)Exponents and Logarithms.
* clogl: (libc)Exponents and Logarithms.
* close: (libc)Opening and Closing Files.
* closedir: (libc)Reading/Closing Directory.
* closelog: (libc)closelog.
* cnd_broadcast: (libc)ISO C Condition Variables.
* cnd_destroy: (libc)ISO C Condition Variables.
* cnd_init: (libc)ISO C Condition Variables.
* cnd_signal: (libc)ISO C Condition Variables.
* cnd_timedwait: (libc)ISO C Condition Variables.
* cnd_wait: (libc)ISO C Condition Variables.
* confstr: (libc)String Parameters.
* conj: (libc)Operations on Complex.
* conjf: (libc)Operations on Complex.
* conjfN: (libc)Operations on Complex.
* conjfNx: (libc)Operations on Complex.
* conjl: (libc)Operations on Complex.
* connect: (libc)Connecting.
* copy_file_range: (libc)Copying File Data.
* copysign: (libc)FP Bit Twiddling.
* copysignf: (libc)FP Bit Twiddling.
* copysignfN: (libc)FP Bit Twiddling.
* copysignfNx: (libc)FP Bit Twiddling.
* copysignl: (libc)FP Bit Twiddling.
* cos: (libc)Trig Functions.
* cosf: (libc)Trig Functions.
* cosfN: (libc)Trig Functions.
* cosfNx: (libc)Trig Functions.
* cosh: (libc)Hyperbolic Functions.
* coshf: (libc)Hyperbolic Functions.
* coshfN: (libc)Hyperbolic Functions.
* coshfNx: (libc)Hyperbolic Functions.
* coshl: (libc)Hyperbolic Functions.
* cosl: (libc)Trig Functions.
* cpow: (libc)Exponents and Logarithms.
* cpowf: (libc)Exponents and Logarithms.
* cpowfN: (libc)Exponents and Logarithms.
* cpowfNx: (libc)Exponents and Logarithms.
* cpowl: (libc)Exponents and Logarithms.
* cproj: (libc)Operations on Complex.
* cprojf: (libc)Operations on Complex.
* cprojfN: (libc)Operations on Complex.
* cprojfNx: (libc)Operations on Complex.
* cprojl: (libc)Operations on Complex.
* creal: (libc)Operations on Complex.
* crealf: (libc)Operations on Complex.
* crealfN: (libc)Operations on Complex.
* crealfNx: (libc)Operations on Complex.
* creall: (libc)Operations on Complex.
* creat64: (libc)Opening and Closing Files.
* creat: (libc)Opening and Closing Files.
* crypt: (libc)Passphrase Storage.
* crypt_r: (libc)Passphrase Storage.
* csin: (libc)Trig Functions.
* csinf: (libc)Trig Functions.
* csinfN: (libc)Trig Functions.
* csinfNx: (libc)Trig Functions.
* csinh: (libc)Hyperbolic Functions.
* csinhf: (libc)Hyperbolic Functions.
* csinhfN: (libc)Hyperbolic Functions.
* csinhfNx: (libc)Hyperbolic Functions.
* csinhl: (libc)Hyperbolic Functions.
* csinl: (libc)Trig Functions.
* csqrt: (libc)Exponents and Logarithms.
* csqrtf: (libc)Exponents and Logarithms.
* csqrtfN: (libc)Exponents and Logarithms.
* csqrtfNx: (libc)Exponents and Logarithms.
* csqrtl: (libc)Exponents and Logarithms.
* ctan: (libc)Trig Functions.
* ctanf: (libc)Trig Functions.
* ctanfN: (libc)Trig Functions.
* ctanfNx: (libc)Trig Functions.
* ctanh: (libc)Hyperbolic Functions.
* ctanhf: (libc)Hyperbolic Functions.
* ctanhfN: (libc)Hyperbolic Functions.
* ctanhfNx: (libc)Hyperbolic Functions.
* ctanhl: (libc)Hyperbolic Functions.
* ctanl: (libc)Trig Functions.
* ctermid: (libc)Identifying the Terminal.
* ctime: (libc)Formatting Calendar Time.
* ctime_r: (libc)Formatting Calendar Time.
* cuserid: (libc)Who Logged In.
* daddl: (libc)Misc FP Arithmetic.
* dcgettext: (libc)Translation with gettext.
* dcngettext: (libc)Advanced gettext functions.
* ddivl: (libc)Misc FP Arithmetic.
* dgettext: (libc)Translation with gettext.
* difftime: (libc)Calculating Elapsed Time.
* dirfd: (libc)Opening a Directory.
* dirname: (libc)Finding Tokens in a String.
* div: (libc)Integer Division.
* dmull: (libc)Misc FP Arithmetic.
* dngettext: (libc)Advanced gettext functions.
* drand48: (libc)SVID Random.
* drand48_r: (libc)SVID Random.
* drem: (libc)Remainder Functions.
* dremf: (libc)Remainder Functions.
* dreml: (libc)Remainder Functions.
* dsubl: (libc)Misc FP Arithmetic.
* dup2: (libc)Duplicating Descriptors.
* dup: (libc)Duplicating Descriptors.
* ecvt: (libc)System V Number Conversion.
* ecvt_r: (libc)System V Number Conversion.
* endfsent: (libc)fstab.
* endgrent: (libc)Scanning All Groups.
* endhostent: (libc)Host Names.
* endmntent: (libc)mtab.
* endnetent: (libc)Networks Database.
* endnetgrent: (libc)Lookup Netgroup.
* endprotoent: (libc)Protocols Database.
* endpwent: (libc)Scanning All Users.
* endservent: (libc)Services Database.
* endutent: (libc)Manipulating the Database.
* endutxent: (libc)XPG Functions.
* envz_add: (libc)Envz Functions.
* envz_entry: (libc)Envz Functions.
* envz_get: (libc)Envz Functions.
* envz_merge: (libc)Envz Functions.
* envz_remove: (libc)Envz Functions.
* envz_strip: (libc)Envz Functions.
* erand48: (libc)SVID Random.
* erand48_r: (libc)SVID Random.
* erf: (libc)Special Functions.
* erfc: (libc)Special Functions.
* erfcf: (libc)Special Functions.
* erfcfN: (libc)Special Functions.
* erfcfNx: (libc)Special Functions.
* erfcl: (libc)Special Functions.
* erff: (libc)Special Functions.
* erffN: (libc)Special Functions.
* erffNx: (libc)Special Functions.
* erfl: (libc)Special Functions.
* err: (libc)Error Messages.
* errno: (libc)Checking for Errors.
* error: (libc)Error Messages.
* error_at_line: (libc)Error Messages.
* errx: (libc)Error Messages.
* execl: (libc)Executing a File.
* execle: (libc)Executing a File.
* execlp: (libc)Executing a File.
* execv: (libc)Executing a File.
* execve: (libc)Executing a File.
* execvp: (libc)Executing a File.
* exit: (libc)Normal Termination.
* exp10: (libc)Exponents and Logarithms.
* exp10f: (libc)Exponents and Logarithms.
* exp10fN: (libc)Exponents and Logarithms.
* exp10fNx: (libc)Exponents and Logarithms.
* exp10l: (libc)Exponents and Logarithms.
* exp2: (libc)Exponents and Logarithms.
* exp2f: (libc)Exponents and Logarithms.
* exp2fN: (libc)Exponents and Logarithms.
* exp2fNx: (libc)Exponents and Logarithms.
* exp2l: (libc)Exponents and Logarithms.
* exp: (libc)Exponents and Logarithms.
* expf: (libc)Exponents and Logarithms.
* expfN: (libc)Exponents and Logarithms.
* expfNx: (libc)Exponents and Logarithms.
* expl: (libc)Exponents and Logarithms.
* explicit_bzero: (libc)Erasing Sensitive Data.
* expm1: (libc)Exponents and Logarithms.
* expm1f: (libc)Exponents and Logarithms.
* expm1fN: (libc)Exponents and Logarithms.
* expm1fNx: (libc)Exponents and Logarithms.
* expm1l: (libc)Exponents and Logarithms.
* fMaddfN: (libc)Misc FP Arithmetic.
* fMaddfNx: (libc)Misc FP Arithmetic.
* fMdivfN: (libc)Misc FP Arithmetic.
* fMdivfNx: (libc)Misc FP Arithmetic.
* fMmulfN: (libc)Misc FP Arithmetic.
* fMmulfNx: (libc)Misc FP Arithmetic.
* fMsubfN: (libc)Misc FP Arithmetic.
* fMsubfNx: (libc)Misc FP Arithmetic.
* fMxaddfN: (libc)Misc FP Arithmetic.
* fMxaddfNx: (libc)Misc FP Arithmetic.
* fMxdivfN: (libc)Misc FP Arithmetic.
* fMxdivfNx: (libc)Misc FP Arithmetic.
* fMxmulfN: (libc)Misc FP Arithmetic.
* fMxmulfNx: (libc)Misc FP Arithmetic.
* fMxsubfN: (libc)Misc FP Arithmetic.
* fMxsubfNx: (libc)Misc FP Arithmetic.
* fabs: (libc)Absolute Value.
* fabsf: (libc)Absolute Value.
* fabsfN: (libc)Absolute Value.
* fabsfNx: (libc)Absolute Value.
* fabsl: (libc)Absolute Value.
* fadd: (libc)Misc FP Arithmetic.
* faddl: (libc)Misc FP Arithmetic.
* fchdir: (libc)Working Directory.
* fchmod: (libc)Setting Permissions.
* fchown: (libc)File Owner.
* fclose: (libc)Closing Streams.
* fcloseall: (libc)Closing Streams.
* fcntl: (libc)Control Operations.
* fcvt: (libc)System V Number Conversion.
* fcvt_r: (libc)System V Number Conversion.
* fdatasync: (libc)Synchronizing I/O.
* fdim: (libc)Misc FP Arithmetic.
* fdimf: (libc)Misc FP Arithmetic.
* fdimfN: (libc)Misc FP Arithmetic.
* fdimfNx: (libc)Misc FP Arithmetic.
* fdiml: (libc)Misc FP Arithmetic.
* fdiv: (libc)Misc FP Arithmetic.
* fdivl: (libc)Misc FP Arithmetic.
* fdopen: (libc)Descriptors and Streams.
* fdopendir: (libc)Opening a Directory.
* feclearexcept: (libc)Status bit operations.
* fedisableexcept: (libc)Control Functions.
* feenableexcept: (libc)Control Functions.
* fegetenv: (libc)Control Functions.
* fegetexcept: (libc)Control Functions.
* fegetexceptflag: (libc)Status bit operations.
* fegetmode: (libc)Control Functions.
* fegetround: (libc)Rounding.
* feholdexcept: (libc)Control Functions.
* feof: (libc)EOF and Errors.
* feof_unlocked: (libc)EOF and Errors.
* feraiseexcept: (libc)Status bit operations.
* ferror: (libc)EOF and Errors.
* ferror_unlocked: (libc)EOF and Errors.
* fesetenv: (libc)Control Functions.
* fesetexcept: (libc)Status bit operations.
* fesetexceptflag: (libc)Status bit operations.
* fesetmode: (libc)Control Functions.
* fesetround: (libc)Rounding.
* fetestexcept: (libc)Status bit operations.
* fetestexceptflag: (libc)Status bit operations.
* feupdateenv: (libc)Control Functions.
* fexecve: (libc)Executing a File.
* fflush: (libc)Flushing Buffers.
* fflush_unlocked: (libc)Flushing Buffers.
* fgetc: (libc)Character Input.
* fgetc_unlocked: (libc)Character Input.
* fgetgrent: (libc)Scanning All Groups.
* fgetgrent_r: (libc)Scanning All Groups.
* fgetpos64: (libc)Portable Positioning.
* fgetpos: (libc)Portable Positioning.
* fgetpwent: (libc)Scanning All Users.
* fgetpwent_r: (libc)Scanning All Users.
* fgets: (libc)Line Input.
* fgets_unlocked: (libc)Line Input.
* fgetwc: (libc)Character Input.
* fgetwc_unlocked: (libc)Character Input.
* fgetws: (libc)Line Input.
* fgetws_unlocked: (libc)Line Input.
* fileno: (libc)Descriptors and Streams.
* fileno_unlocked: (libc)Descriptors and Streams.
* finite: (libc)Floating Point Classes.
* finitef: (libc)Floating Point Classes.
* finitel: (libc)Floating Point Classes.
* flockfile: (libc)Streams and Threads.
* floor: (libc)Rounding Functions.
* floorf: (libc)Rounding Functions.
* floorfN: (libc)Rounding Functions.
* floorfNx: (libc)Rounding Functions.
* floorl: (libc)Rounding Functions.
* fma: (libc)Misc FP Arithmetic.
* fmaf: (libc)Misc FP Arithmetic.
* fmafN: (libc)Misc FP Arithmetic.
* fmafNx: (libc)Misc FP Arithmetic.
* fmal: (libc)Misc FP Arithmetic.
* fmax: (libc)Misc FP Arithmetic.
* fmaxf: (libc)Misc FP Arithmetic.
* fmaxfN: (libc)Misc FP Arithmetic.
* fmaxfNx: (libc)Misc FP Arithmetic.
* fmaxl: (libc)Misc FP Arithmetic.
* fmaxmag: (libc)Misc FP Arithmetic.
* fmaxmagf: (libc)Misc FP Arithmetic.
* fmaxmagfN: (libc)Misc FP Arithmetic.
* fmaxmagfNx: (libc)Misc FP Arithmetic.
* fmaxmagl: (libc)Misc FP Arithmetic.
* fmemopen: (libc)String Streams.
* fmin: (libc)Misc FP Arithmetic.
* fminf: (libc)Misc FP Arithmetic.
* fminfN: (libc)Misc FP Arithmetic.
* fminfNx: (libc)Misc FP Arithmetic.
* fminl: (libc)Misc FP Arithmetic.
* fminmag: (libc)Misc FP Arithmetic.
* fminmagf: (libc)Misc FP Arithmetic.
* fminmagfN: (libc)Misc FP Arithmetic.
* fminmagfNx: (libc)Misc FP Arithmetic.
* fminmagl: (libc)Misc FP Arithmetic.
* fmod: (libc)Remainder Functions.
* fmodf: (libc)Remainder Functions.
* fmodfN: (libc)Remainder Functions.
* fmodfNx: (libc)Remainder Functions.
* fmodl: (libc)Remainder Functions.
* fmtmsg: (libc)Printing Formatted Messages.
* fmul: (libc)Misc FP Arithmetic.
* fmull: (libc)Misc FP Arithmetic.
* fnmatch: (libc)Wildcard Matching.
* fopen64: (libc)Opening Streams.
* fopen: (libc)Opening Streams.
* fopencookie: (libc)Streams and Cookies.
* fork: (libc)Creating a Process.
* forkpty: (libc)Pseudo-Terminal Pairs.
* fpathconf: (libc)Pathconf.
* fpclassify: (libc)Floating Point Classes.
* fprintf: (libc)Formatted Output Functions.
* fputc: (libc)Simple Output.
* fputc_unlocked: (libc)Simple Output.
* fputs: (libc)Simple Output.
* fputs_unlocked: (libc)Simple Output.
* fputwc: (libc)Simple Output.
* fputwc_unlocked: (libc)Simple Output.
* fputws: (libc)Simple Output.
* fputws_unlocked: (libc)Simple Output.
* fread: (libc)Block Input/Output.
* fread_unlocked: (libc)Block Input/Output.
* free: (libc)Freeing after Malloc.
* freopen64: (libc)Opening Streams.
* freopen: (libc)Opening Streams.
* frexp: (libc)Normalization Functions.
* frexpf: (libc)Normalization Functions.
* frexpfN: (libc)Normalization Functions.
* frexpfNx: (libc)Normalization Functions.
* frexpl: (libc)Normalization Functions.
* fromfp: (libc)Rounding Functions.
* fromfpf: (libc)Rounding Functions.
* fromfpfN: (libc)Rounding Functions.
* fromfpfNx: (libc)Rounding Functions.
* fromfpl: (libc)Rounding Functions.
* fromfpx: (libc)Rounding Functions.
* fromfpxf: (libc)Rounding Functions.
* fromfpxfN: (libc)Rounding Functions.
* fromfpxfNx: (libc)Rounding Functions.
* fromfpxl: (libc)Rounding Functions.
* fscanf: (libc)Formatted Input Functions.
* fseek: (libc)File Positioning.
* fseeko64: (libc)File Positioning.
* fseeko: (libc)File Positioning.
* fsetpos64: (libc)Portable Positioning.
* fsetpos: (libc)Portable Positioning.
* fstat64: (libc)Reading Attributes.
* fstat: (libc)Reading Attributes.
* fsub: (libc)Misc FP Arithmetic.
* fsubl: (libc)Misc FP Arithmetic.
* fsync: (libc)Synchronizing I/O.
* ftell: (libc)File Positioning.
* ftello64: (libc)File Positioning.
* ftello: (libc)File Positioning.
* ftruncate64: (libc)File Size.
* ftruncate: (libc)File Size.
* ftrylockfile: (libc)Streams and Threads.
* ftw64: (libc)Working with Directory Trees.
* ftw: (libc)Working with Directory Trees.
* funlockfile: (libc)Streams and Threads.
* futimes: (libc)File Times.
* fwide: (libc)Streams and I18N.
* fwprintf: (libc)Formatted Output Functions.
* fwrite: (libc)Block Input/Output.
* fwrite_unlocked: (libc)Block Input/Output.
* fwscanf: (libc)Formatted Input Functions.
* gamma: (libc)Special Functions.
* gammaf: (libc)Special Functions.
* gammal: (libc)Special Functions.
* gcvt: (libc)System V Number Conversion.
* get_avphys_pages: (libc)Query Memory Parameters.
* get_current_dir_name: (libc)Working Directory.
* get_nprocs: (libc)Processor Resources.
* get_nprocs_conf: (libc)Processor Resources.
* get_phys_pages: (libc)Query Memory Parameters.
* getauxval: (libc)Auxiliary Vector.
* getc: (libc)Character Input.
* getc_unlocked: (libc)Character Input.
* getchar: (libc)Character Input.
* getchar_unlocked: (libc)Character Input.
* getcontext: (libc)System V contexts.
* getcpu: (libc)CPU Affinity.
* getcwd: (libc)Working Directory.
* getdate: (libc)General Time String Parsing.
* getdate_r: (libc)General Time String Parsing.
* getdelim: (libc)Line Input.
* getdents64: (libc)Low-level Directory Access.
* getdomainnname: (libc)Host Identification.
* getegid: (libc)Reading Persona.
* getentropy: (libc)Unpredictable Bytes.
* getenv: (libc)Environment Access.
* geteuid: (libc)Reading Persona.
* getfsent: (libc)fstab.
* getfsfile: (libc)fstab.
* getfsspec: (libc)fstab.
* getgid: (libc)Reading Persona.
* getgrent: (libc)Scanning All Groups.
* getgrent_r: (libc)Scanning All Groups.
* getgrgid: (libc)Lookup Group.
* getgrgid_r: (libc)Lookup Group.
* getgrnam: (libc)Lookup Group.
* getgrnam_r: (libc)Lookup Group.
* getgrouplist: (libc)Setting Groups.
* getgroups: (libc)Reading Persona.
* gethostbyaddr: (libc)Host Names.
* gethostbyaddr_r: (libc)Host Names.
* gethostbyname2: (libc)Host Names.
* gethostbyname2_r: (libc)Host Names.
* gethostbyname: (libc)Host Names.
* gethostbyname_r: (libc)Host Names.
* gethostent: (libc)Host Names.
* gethostid: (libc)Host Identification.
* gethostname: (libc)Host Identification.
* getitimer: (libc)Setting an Alarm.
* getline: (libc)Line Input.
* getloadavg: (libc)Processor Resources.
* getlogin: (libc)Who Logged In.
* getmntent: (libc)mtab.
* getmntent_r: (libc)mtab.
* getnetbyaddr: (libc)Networks Database.
* getnetbyname: (libc)Networks Database.
* getnetent: (libc)Networks Database.
* getnetgrent: (libc)Lookup Netgroup.
* getnetgrent_r: (libc)Lookup Netgroup.
* getopt: (libc)Using Getopt.
* getopt_long: (libc)Getopt Long Options.
* getopt_long_only: (libc)Getopt Long Options.
* getpagesize: (libc)Query Memory Parameters.
* getpass: (libc)getpass.
* getpayload: (libc)FP Bit Twiddling.
* getpayloadf: (libc)FP Bit Twiddling.
* getpayloadfN: (libc)FP Bit Twiddling.
* getpayloadfNx: (libc)FP Bit Twiddling.
* getpayloadl: (libc)FP Bit Twiddling.
* getpeername: (libc)Who is Connected.
* getpgid: (libc)Process Group Functions.
* getpgrp: (libc)Process Group Functions.
* getpid: (libc)Process Identification.
* getppid: (libc)Process Identification.
* getpriority: (libc)Traditional Scheduling Functions.
* getprotobyname: (libc)Protocols Database.
* getprotobynumber: (libc)Protocols Database.
* getprotoent: (libc)Protocols Database.
* getpt: (libc)Allocation.
* getpwent: (libc)Scanning All Users.
* getpwent_r: (libc)Scanning All Users.
* getpwnam: (libc)Lookup User.
* getpwnam_r: (libc)Lookup User.
* getpwuid: (libc)Lookup User.
* getpwuid_r: (libc)Lookup User.
* getrandom: (libc)Unpredictable Bytes.
* getrlimit64: (libc)Limits on Resources.
* getrlimit: (libc)Limits on Resources.
* getrusage: (libc)Resource Usage.
* gets: (libc)Line Input.
* getservbyname: (libc)Services Database.
* getservbyport: (libc)Services Database.
* getservent: (libc)Services Database.
* getsid: (libc)Process Group Functions.
* getsockname: (libc)Reading Address.
* getsockopt: (libc)Socket Option Functions.
* getsubopt: (libc)Suboptions.
* gettext: (libc)Translation with gettext.
* gettid: (libc)Process Identification.
* gettimeofday: (libc)Getting the Time.
* getuid: (libc)Reading Persona.
* getumask: (libc)Setting Permissions.
* getutent: (libc)Manipulating the Database.
* getutent_r: (libc)Manipulating the Database.
* getutid: (libc)Manipulating the Database.
* getutid_r: (libc)Manipulating the Database.
* getutline: (libc)Manipulating the Database.
* getutline_r: (libc)Manipulating the Database.
* getutmp: (libc)XPG Functions.
* getutmpx: (libc)XPG Functions.
* getutxent: (libc)XPG Functions.
* getutxid: (libc)XPG Functions.
* getutxline: (libc)XPG Functions.
* getw: (libc)Character Input.
* getwc: (libc)Character Input.
* getwc_unlocked: (libc)Character Input.
* getwchar: (libc)Character Input.
* getwchar_unlocked: (libc)Character Input.
* getwd: (libc)Working Directory.
* glob64: (libc)Calling Glob.
* glob: (libc)Calling Glob.
* globfree64: (libc)More Flags for Globbing.
* globfree: (libc)More Flags for Globbing.
* gmtime: (libc)Broken-down Time.
* gmtime_r: (libc)Broken-down Time.
* grantpt: (libc)Allocation.
* gsignal: (libc)Signaling Yourself.
* gtty: (libc)BSD Terminal Modes.
* hasmntopt: (libc)mtab.
* hcreate: (libc)Hash Search Function.
* hcreate_r: (libc)Hash Search Function.
* hdestroy: (libc)Hash Search Function.
* hdestroy_r: (libc)Hash Search Function.
* hsearch: (libc)Hash Search Function.
* hsearch_r: (libc)Hash Search Function.
* htonl: (libc)Byte Order.
* htons: (libc)Byte Order.
* hypot: (libc)Exponents and Logarithms.
* hypotf: (libc)Exponents and Logarithms.
* hypotfN: (libc)Exponents and Logarithms.
* hypotfNx: (libc)Exponents and Logarithms.
* hypotl: (libc)Exponents and Logarithms.
* iconv: (libc)Generic Conversion Interface.
* iconv_close: (libc)Generic Conversion Interface.
* iconv_open: (libc)Generic Conversion Interface.
* if_freenameindex: (libc)Interface Naming.
* if_indextoname: (libc)Interface Naming.
* if_nameindex: (libc)Interface Naming.
* if_nametoindex: (libc)Interface Naming.
* ilogb: (libc)Exponents and Logarithms.
* ilogbf: (libc)Exponents and Logarithms.
* ilogbfN: (libc)Exponents and Logarithms.
* ilogbfNx: (libc)Exponents and Logarithms.
* ilogbl: (libc)Exponents and Logarithms.
* imaxabs: (libc)Absolute Value.
* imaxdiv: (libc)Integer Division.
* in6addr_any: (libc)Host Address Data Type.
* in6addr_loopback: (libc)Host Address Data Type.
* index: (libc)Search Functions.
* inet_addr: (libc)Host Address Functions.
* inet_aton: (libc)Host Address Functions.
* inet_lnaof: (libc)Host Address Functions.
* inet_makeaddr: (libc)Host Address Functions.
* inet_netof: (libc)Host Address Functions.
* inet_network: (libc)Host Address Functions.
* inet_ntoa: (libc)Host Address Functions.
* inet_ntop: (libc)Host Address Functions.
* inet_pton: (libc)Host Address Functions.
* initgroups: (libc)Setting Groups.
* initstate: (libc)BSD Random.
* initstate_r: (libc)BSD Random.
* innetgr: (libc)Netgroup Membership.
* ioctl: (libc)IOCTLs.
* isalnum: (libc)Classification of Characters.
* isalpha: (libc)Classification of Characters.
* isascii: (libc)Classification of Characters.
* isatty: (libc)Is It a Terminal.
* isblank: (libc)Classification of Characters.
* iscanonical: (libc)Floating Point Classes.
* iscntrl: (libc)Classification of Characters.
* isdigit: (libc)Classification of Characters.
* iseqsig: (libc)FP Comparison Functions.
* isfinite: (libc)Floating Point Classes.
* isgraph: (libc)Classification of Characters.
* isgreater: (libc)FP Comparison Functions.
* isgreaterequal: (libc)FP Comparison Functions.
* isinf: (libc)Floating Point Classes.
* isinff: (libc)Floating Point Classes.
* isinfl: (libc)Floating Point Classes.
* isless: (libc)FP Comparison Functions.
* islessequal: (libc)FP Comparison Functions.
* islessgreater: (libc)FP Comparison Functions.
* islower: (libc)Classification of Characters.
* isnan: (libc)Floating Point Classes.
* isnan: (libc)Floating Point Classes.
* isnanf: (libc)Floating Point Classes.
* isnanl: (libc)Floating Point Classes.
* isnormal: (libc)Floating Point Classes.
* isprint: (libc)Classification of Characters.
* ispunct: (libc)Classification of Characters.
* issignaling: (libc)Floating Point Classes.
* isspace: (libc)Classification of Characters.
* issubnormal: (libc)Floating Point Classes.
* isunordered: (libc)FP Comparison Functions.
* isupper: (libc)Classification of Characters.
* iswalnum: (libc)Classification of Wide Characters.
* iswalpha: (libc)Classification of Wide Characters.
* iswblank: (libc)Classification of Wide Characters.
* iswcntrl: (libc)Classification of Wide Characters.
* iswctype: (libc)Classification of Wide Characters.
* iswdigit: (libc)Classification of Wide Characters.
* iswgraph: (libc)Classification of Wide Characters.
* iswlower: (libc)Classification of Wide Characters.
* iswprint: (libc)Classification of Wide Characters.
* iswpunct: (libc)Classification of Wide Characters.
* iswspace: (libc)Classification of Wide Characters.
* iswupper: (libc)Classification of Wide Characters.
* iswxdigit: (libc)Classification of Wide Characters.
* isxdigit: (libc)Classification of Characters.
* iszero: (libc)Floating Point Classes.
* j0: (libc)Special Functions.
* j0f: (libc)Special Functions.
* j0fN: (libc)Special Functions.
* j0fNx: (libc)Special Functions.
* j0l: (libc)Special Functions.
* j1: (libc)Special Functions.
* j1f: (libc)Special Functions.
* j1fN: (libc)Special Functions.
* j1fNx: (libc)Special Functions.
* j1l: (libc)Special Functions.
* jn: (libc)Special Functions.
* jnf: (libc)Special Functions.
* jnfN: (libc)Special Functions.
* jnfNx: (libc)Special Functions.
* jnl: (libc)Special Functions.
* jrand48: (libc)SVID Random.
* jrand48_r: (libc)SVID Random.
* kill: (libc)Signaling Another Process.
* killpg: (libc)Signaling Another Process.
* l64a: (libc)Encode Binary Data.
* labs: (libc)Absolute Value.
* lcong48: (libc)SVID Random.
* lcong48_r: (libc)SVID Random.
* ldexp: (libc)Normalization Functions.
* ldexpf: (libc)Normalization Functions.
* ldexpfN: (libc)Normalization Functions.
* ldexpfNx: (libc)Normalization Functions.
* ldexpl: (libc)Normalization Functions.
* ldiv: (libc)Integer Division.
* lfind: (libc)Array Search Function.
* lgamma: (libc)Special Functions.
* lgamma_r: (libc)Special Functions.
* lgammaf: (libc)Special Functions.
* lgammafN: (libc)Special Functions.
* lgammafN_r: (libc)Special Functions.
* lgammafNx: (libc)Special Functions.
* lgammafNx_r: (libc)Special Functions.
* lgammaf_r: (libc)Special Functions.
* lgammal: (libc)Special Functions.
* lgammal_r: (libc)Special Functions.
* link: (libc)Hard Links.
* linkat: (libc)Hard Links.
* lio_listio64: (libc)Asynchronous Reads/Writes.
* lio_listio: (libc)Asynchronous Reads/Writes.
* listen: (libc)Listening.
* llabs: (libc)Absolute Value.
* lldiv: (libc)Integer Division.
* llogb: (libc)Exponents and Logarithms.
* llogbf: (libc)Exponents and Logarithms.
* llogbfN: (libc)Exponents and Logarithms.
* llogbfNx: (libc)Exponents and Logarithms.
* llogbl: (libc)Exponents and Logarithms.
* llrint: (libc)Rounding Functions.
* llrintf: (libc)Rounding Functions.
* llrintfN: (libc)Rounding Functions.
* llrintfNx: (libc)Rounding Functions.
* llrintl: (libc)Rounding Functions.
* llround: (libc)Rounding Functions.
* llroundf: (libc)Rounding Functions.
* llroundfN: (libc)Rounding Functions.
* llroundfNx: (libc)Rounding Functions.
* llroundl: (libc)Rounding Functions.
* localeconv: (libc)The Lame Way to Locale Data.
* localtime: (libc)Broken-down Time.
* localtime_r: (libc)Broken-down Time.
* log10: (libc)Exponents and Logarithms.
* log10f: (libc)Exponents and Logarithms.
* log10fN: (libc)Exponents and Logarithms.
* log10fNx: (libc)Exponents and Logarithms.
* log10l: (libc)Exponents and Logarithms.
* log1p: (libc)Exponents and Logarithms.
* log1pf: (libc)Exponents and Logarithms.
* log1pfN: (libc)Exponents and Logarithms.
* log1pfNx: (libc)Exponents and Logarithms.
* log1pl: (libc)Exponents and Logarithms.
* log2: (libc)Exponents and Logarithms.
* log2f: (libc)Exponents and Logarithms.
* log2fN: (libc)Exponents and Logarithms.
* log2fNx: (libc)Exponents and Logarithms.
* log2l: (libc)Exponents and Logarithms.
* log: (libc)Exponents and Logarithms.
* logb: (libc)Exponents and Logarithms.
* logbf: (libc)Exponents and Logarithms.
* logbfN: (libc)Exponents and Logarithms.
* logbfNx: (libc)Exponents and Logarithms.
* logbl: (libc)Exponents and Logarithms.
* logf: (libc)Exponents and Logarithms.
* logfN: (libc)Exponents and Logarithms.
* logfNx: (libc)Exponents and Logarithms.
* login: (libc)Logging In and Out.
* login_tty: (libc)Logging In and Out.
* logl: (libc)Exponents and Logarithms.
* logout: (libc)Logging In and Out.
* logwtmp: (libc)Logging In and Out.
* longjmp: (libc)Non-Local Details.
* lrand48: (libc)SVID Random.
* lrand48_r: (libc)SVID Random.
* lrint: (libc)Rounding Functions.
* lrintf: (libc)Rounding Functions.
* lrintfN: (libc)Rounding Functions.
* lrintfNx: (libc)Rounding Functions.
* lrintl: (libc)Rounding Functions.
* lround: (libc)Rounding Functions.
* lroundf: (libc)Rounding Functions.
* lroundfN: (libc)Rounding Functions.
* lroundfNx: (libc)Rounding Functions.
* lroundl: (libc)Rounding Functions.
* lsearch: (libc)Array Search Function.
* lseek64: (libc)File Position Primitive.
* lseek: (libc)File Position Primitive.
* lstat64: (libc)Reading Attributes.
* lstat: (libc)Reading Attributes.
* lutimes: (libc)File Times.
* madvise: (libc)Memory-mapped I/O.
* makecontext: (libc)System V contexts.
* mallinfo2: (libc)Statistics of Malloc.
* malloc: (libc)Basic Allocation.
* mallopt: (libc)Malloc Tunable Parameters.
* mblen: (libc)Non-reentrant Character Conversion.
* mbrlen: (libc)Converting a Character.
* mbrtowc: (libc)Converting a Character.
* mbsinit: (libc)Keeping the state.
* mbsnrtowcs: (libc)Converting Strings.
* mbsrtowcs: (libc)Converting Strings.
* mbstowcs: (libc)Non-reentrant String Conversion.
* mbtowc: (libc)Non-reentrant Character Conversion.
* mcheck: (libc)Heap Consistency Checking.
* memalign: (libc)Aligned Memory Blocks.
* memccpy: (libc)Copying Strings and Arrays.
* memchr: (libc)Search Functions.
* memcmp: (libc)String/Array Comparison.
* memcpy: (libc)Copying Strings and Arrays.
* memfd_create: (libc)Memory-mapped I/O.
* memfrob: (libc)Obfuscating Data.
* memmem: (libc)Search Functions.
* memmove: (libc)Copying Strings and Arrays.
* mempcpy: (libc)Copying Strings and Arrays.
* memrchr: (libc)Search Functions.
* memset: (libc)Copying Strings and Arrays.
* mkdir: (libc)Creating Directories.
* mkdtemp: (libc)Temporary Files.
* mkfifo: (libc)FIFO Special Files.
* mknod: (libc)Making Special Files.
* mkstemp: (libc)Temporary Files.
* mktemp: (libc)Temporary Files.
* mktime: (libc)Broken-down Time.
* mlock2: (libc)Page Lock Functions.
* mlock: (libc)Page Lock Functions.
* mlockall: (libc)Page Lock Functions.
* mmap64: (libc)Memory-mapped I/O.
* mmap: (libc)Memory-mapped I/O.
* modf: (libc)Rounding Functions.
* modff: (libc)Rounding Functions.
* modffN: (libc)Rounding Functions.
* modffNx: (libc)Rounding Functions.
* modfl: (libc)Rounding Functions.
* mount: (libc)Mount-Unmount-Remount.
* mprobe: (libc)Heap Consistency Checking.
* mprotect: (libc)Memory Protection.
* mrand48: (libc)SVID Random.
* mrand48_r: (libc)SVID Random.
* mremap: (libc)Memory-mapped I/O.
* msync: (libc)Memory-mapped I/O.
* mtrace: (libc)Tracing malloc.
* mtx_destroy: (libc)ISO C Mutexes.
* mtx_init: (libc)ISO C Mutexes.
* mtx_lock: (libc)ISO C Mutexes.
* mtx_timedlock: (libc)ISO C Mutexes.
* mtx_trylock: (libc)ISO C Mutexes.
* mtx_unlock: (libc)ISO C Mutexes.
* munlock: (libc)Page Lock Functions.
* munlockall: (libc)Page Lock Functions.
* munmap: (libc)Memory-mapped I/O.
* muntrace: (libc)Tracing malloc.
* nan: (libc)FP Bit Twiddling.
* nanf: (libc)FP Bit Twiddling.
* nanfN: (libc)FP Bit Twiddling.
* nanfNx: (libc)FP Bit Twiddling.
* nanl: (libc)FP Bit Twiddling.
* nanosleep: (libc)Sleeping.
* nearbyint: (libc)Rounding Functions.
* nearbyintf: (libc)Rounding Functions.
* nearbyintfN: (libc)Rounding Functions.
* nearbyintfNx: (libc)Rounding Functions.
* nearbyintl: (libc)Rounding Functions.
* nextafter: (libc)FP Bit Twiddling.
* nextafterf: (libc)FP Bit Twiddling.
* nextafterfN: (libc)FP Bit Twiddling.
* nextafterfNx: (libc)FP Bit Twiddling.
* nextafterl: (libc)FP Bit Twiddling.
* nextdown: (libc)FP Bit Twiddling.
* nextdownf: (libc)FP Bit Twiddling.
* nextdownfN: (libc)FP Bit Twiddling.
* nextdownfNx: (libc)FP Bit Twiddling.
* nextdownl: (libc)FP Bit Twiddling.
* nexttoward: (libc)FP Bit Twiddling.
* nexttowardf: (libc)FP Bit Twiddling.
* nexttowardl: (libc)FP Bit Twiddling.
* nextup: (libc)FP Bit Twiddling.
* nextupf: (libc)FP Bit Twiddling.
* nextupfN: (libc)FP Bit Twiddling.
* nextupfNx: (libc)FP Bit Twiddling.
* nextupl: (libc)FP Bit Twiddling.
* nftw64: (libc)Working with Directory Trees.
* nftw: (libc)Working with Directory Trees.
* ngettext: (libc)Advanced gettext functions.
* nice: (libc)Traditional Scheduling Functions.
* nl_langinfo: (libc)The Elegant and Fast Way.
* nrand48: (libc)SVID Random.
* nrand48_r: (libc)SVID Random.
* ntohl: (libc)Byte Order.
* ntohs: (libc)Byte Order.
* ntp_adjtime: (libc)Setting and Adjusting the Time.
* ntp_gettime: (libc)Setting and Adjusting the Time.
* obstack_1grow: (libc)Growing Objects.
* obstack_1grow_fast: (libc)Extra Fast Growing.
* obstack_alignment_mask: (libc)Obstacks Data Alignment.
* obstack_alloc: (libc)Allocation in an Obstack.
* obstack_base: (libc)Status of an Obstack.
* obstack_blank: (libc)Growing Objects.
* obstack_blank_fast: (libc)Extra Fast Growing.
* obstack_chunk_size: (libc)Obstack Chunks.
* obstack_copy0: (libc)Allocation in an Obstack.
* obstack_copy: (libc)Allocation in an Obstack.
* obstack_finish: (libc)Growing Objects.
* obstack_free: (libc)Freeing Obstack Objects.
* obstack_grow0: (libc)Growing Objects.
* obstack_grow: (libc)Growing Objects.
* obstack_init: (libc)Preparing for Obstacks.
* obstack_int_grow: (libc)Growing Objects.
* obstack_int_grow_fast: (libc)Extra Fast Growing.
* obstack_next_free: (libc)Status of an Obstack.
* obstack_object_size: (libc)Growing Objects.
* obstack_object_size: (libc)Status of an Obstack.
* obstack_printf: (libc)Dynamic Output.
* obstack_ptr_grow: (libc)Growing Objects.
* obstack_ptr_grow_fast: (libc)Extra Fast Growing.
* obstack_room: (libc)Extra Fast Growing.
* obstack_vprintf: (libc)Variable Arguments Output.
* offsetof: (libc)Structure Measurement.
* on_exit: (libc)Cleanups on Exit.
* open64: (libc)Opening and Closing Files.
* open: (libc)Opening and Closing Files.
* open_memstream: (libc)String Streams.
* opendir: (libc)Opening a Directory.
* openlog: (libc)openlog.
* openpty: (libc)Pseudo-Terminal Pairs.
* parse_printf_format: (libc)Parsing a Template String.
* pathconf: (libc)Pathconf.
* pause: (libc)Using Pause.
* pclose: (libc)Pipe to a Subprocess.
* perror: (libc)Error Messages.
* pipe: (libc)Creating a Pipe.
* pkey_alloc: (libc)Memory Protection.
* pkey_free: (libc)Memory Protection.
* pkey_get: (libc)Memory Protection.
* pkey_mprotect: (libc)Memory Protection.
* pkey_set: (libc)Memory Protection.
* popen: (libc)Pipe to a Subprocess.
* posix_fallocate64: (libc)Storage Allocation.
* posix_fallocate: (libc)Storage Allocation.
* posix_memalign: (libc)Aligned Memory Blocks.
* pow: (libc)Exponents and Logarithms.
* powf: (libc)Exponents and Logarithms.
* powfN: (libc)Exponents and Logarithms.
* powfNx: (libc)Exponents and Logarithms.
* powl: (libc)Exponents and Logarithms.
* pread64: (libc)I/O Primitives.
* pread: (libc)I/O Primitives.
* preadv2: (libc)Scatter-Gather.
* preadv64: (libc)Scatter-Gather.
* preadv64v2: (libc)Scatter-Gather.
* preadv: (libc)Scatter-Gather.
* printf: (libc)Formatted Output Functions.
* printf_size: (libc)Predefined Printf Handlers.
* printf_size_info: (libc)Predefined Printf Handlers.
* psignal: (libc)Signal Messages.
* pthread_attr_getsigmask_np: (libc)Initial Thread Signal Mask.
* pthread_attr_setsigmask_np: (libc)Initial Thread Signal Mask.
* pthread_clockjoin_np: (libc)Waiting with Explicit Clocks.
* pthread_cond_clockwait: (libc)Waiting with Explicit Clocks.
* pthread_getattr_default_np: (libc)Default Thread Attributes.
* pthread_getspecific: (libc)Thread-specific Data.
* pthread_key_create: (libc)Thread-specific Data.
* pthread_key_delete: (libc)Thread-specific Data.
* pthread_rwlock_clockrdlock: (libc)Waiting with Explicit Clocks.
* pthread_rwlock_clockwrlock: (libc)Waiting with Explicit Clocks.
* pthread_setattr_default_np: (libc)Default Thread Attributes.
* pthread_setspecific: (libc)Thread-specific Data.
* pthread_timedjoin_np: (libc)Waiting with Explicit Clocks.
* pthread_tryjoin_np: (libc)Waiting with Explicit Clocks.
* ptsname: (libc)Allocation.
* ptsname_r: (libc)Allocation.
* putc: (libc)Simple Output.
* putc_unlocked: (libc)Simple Output.
* putchar: (libc)Simple Output.
* putchar_unlocked: (libc)Simple Output.
* putenv: (libc)Environment Access.
* putpwent: (libc)Writing a User Entry.
* puts: (libc)Simple Output.
* pututline: (libc)Manipulating the Database.
* pututxline: (libc)XPG Functions.
* putw: (libc)Simple Output.
* putwc: (libc)Simple Output.
* putwc_unlocked: (libc)Simple Output.
* putwchar: (libc)Simple Output.
* putwchar_unlocked: (libc)Simple Output.
* pwrite64: (libc)I/O Primitives.
* pwrite: (libc)I/O Primitives.
* pwritev2: (libc)Scatter-Gather.
* pwritev64: (libc)Scatter-Gather.
* pwritev64v2: (libc)Scatter-Gather.
* pwritev: (libc)Scatter-Gather.
* qecvt: (libc)System V Number Conversion.
* qecvt_r: (libc)System V Number Conversion.
* qfcvt: (libc)System V Number Conversion.
* qfcvt_r: (libc)System V Number Conversion.
* qgcvt: (libc)System V Number Conversion.
* qsort: (libc)Array Sort Function.
* raise: (libc)Signaling Yourself.
* rand: (libc)ISO Random.
* rand_r: (libc)ISO Random.
* random: (libc)BSD Random.
* random_r: (libc)BSD Random.
* rawmemchr: (libc)Search Functions.
* read: (libc)I/O Primitives.
* readdir64: (libc)Reading/Closing Directory.
* readdir64_r: (libc)Reading/Closing Directory.
* readdir: (libc)Reading/Closing Directory.
* readdir_r: (libc)Reading/Closing Directory.
* readlink: (libc)Symbolic Links.
* readv: (libc)Scatter-Gather.
* realloc: (libc)Changing Block Size.
* reallocarray: (libc)Changing Block Size.
* realpath: (libc)Symbolic Links.
* recv: (libc)Receiving Data.
* recvfrom: (libc)Receiving Datagrams.
* recvmsg: (libc)Receiving Datagrams.
* regcomp: (libc)POSIX Regexp Compilation.
* regerror: (libc)Regexp Cleanup.
* regexec: (libc)Matching POSIX Regexps.
* regfree: (libc)Regexp Cleanup.
* register_printf_function: (libc)Registering New Conversions.
* remainder: (libc)Remainder Functions.
* remainderf: (libc)Remainder Functions.
* remainderfN: (libc)Remainder Functions.
* remainderfNx: (libc)Remainder Functions.
* remainderl: (libc)Remainder Functions.
* remove: (libc)Deleting Files.
* rename: (libc)Renaming Files.
* rewind: (libc)File Positioning.
* rewinddir: (libc)Random Access Directory.
* rindex: (libc)Search Functions.
* rint: (libc)Rounding Functions.
* rintf: (libc)Rounding Functions.
* rintfN: (libc)Rounding Functions.
* rintfNx: (libc)Rounding Functions.
* rintl: (libc)Rounding Functions.
* rmdir: (libc)Deleting Files.
* round: (libc)Rounding Functions.
* roundeven: (libc)Rounding Functions.
* roundevenf: (libc)Rounding Functions.
* roundevenfN: (libc)Rounding Functions.
* roundevenfNx: (libc)Rounding Functions.
* roundevenl: (libc)Rounding Functions.
* roundf: (libc)Rounding Functions.
* roundfN: (libc)Rounding Functions.
* roundfNx: (libc)Rounding Functions.
* roundl: (libc)Rounding Functions.
* rpmatch: (libc)Yes-or-No Questions.
* sbrk: (libc)Resizing the Data Segment.
* scalb: (libc)Normalization Functions.
* scalbf: (libc)Normalization Functions.
* scalbl: (libc)Normalization Functions.
* scalbln: (libc)Normalization Functions.
* scalblnf: (libc)Normalization Functions.
* scalblnfN: (libc)Normalization Functions.
* scalblnfNx: (libc)Normalization Functions.
* scalblnl: (libc)Normalization Functions.
* scalbn: (libc)Normalization Functions.
* scalbnf: (libc)Normalization Functions.
* scalbnfN: (libc)Normalization Functions.
* scalbnfNx: (libc)Normalization Functions.
* scalbnl: (libc)Normalization Functions.
* scandir64: (libc)Scanning Directory Content.
* scandir: (libc)Scanning Directory Content.
* scanf: (libc)Formatted Input Functions.
* sched_get_priority_max: (libc)Basic Scheduling Functions.
* sched_get_priority_min: (libc)Basic Scheduling Functions.
* sched_getaffinity: (libc)CPU Affinity.
* sched_getparam: (libc)Basic Scheduling Functions.
* sched_getscheduler: (libc)Basic Scheduling Functions.
* sched_rr_get_interval: (libc)Basic Scheduling Functions.
* sched_setaffinity: (libc)CPU Affinity.
* sched_setparam: (libc)Basic Scheduling Functions.
* sched_setscheduler: (libc)Basic Scheduling Functions.
* sched_yield: (libc)Basic Scheduling Functions.
* secure_getenv: (libc)Environment Access.
* seed48: (libc)SVID Random.
* seed48_r: (libc)SVID Random.
* seekdir: (libc)Random Access Directory.
* select: (libc)Waiting for I/O.
* sem_clockwait: (libc)Waiting with Explicit Clocks.
* sem_close: (libc)Semaphores.
* sem_destroy: (libc)Semaphores.
* sem_getvalue: (libc)Semaphores.
* sem_init: (libc)Semaphores.
* sem_open: (libc)Semaphores.
* sem_post: (libc)Semaphores.
* sem_timedwait: (libc)Semaphores.
* sem_trywait: (libc)Semaphores.
* sem_unlink: (libc)Semaphores.
* sem_wait: (libc)Semaphores.
* semctl: (libc)Semaphores.
* semget: (libc)Semaphores.
* semop: (libc)Semaphores.
* semtimedop: (libc)Semaphores.
* send: (libc)Sending Data.
* sendmsg: (libc)Receiving Datagrams.
* sendto: (libc)Sending Datagrams.
* setbuf: (libc)Controlling Buffering.
* setbuffer: (libc)Controlling Buffering.
* setcontext: (libc)System V contexts.
* setdomainname: (libc)Host Identification.
* setegid: (libc)Setting Groups.
* setenv: (libc)Environment Access.
* seteuid: (libc)Setting User ID.
* setfsent: (libc)fstab.
* setgid: (libc)Setting Groups.
* setgrent: (libc)Scanning All Groups.
* setgroups: (libc)Setting Groups.
* sethostent: (libc)Host Names.
* sethostid: (libc)Host Identification.
* sethostname: (libc)Host Identification.
* setitimer: (libc)Setting an Alarm.
* setjmp: (libc)Non-Local Details.
* setlinebuf: (libc)Controlling Buffering.
* setlocale: (libc)Setting the Locale.
* setlogmask: (libc)setlogmask.
* setmntent: (libc)mtab.
* setnetent: (libc)Networks Database.
* setnetgrent: (libc)Lookup Netgroup.
* setpayload: (libc)FP Bit Twiddling.
* setpayloadf: (libc)FP Bit Twiddling.
* setpayloadfN: (libc)FP Bit Twiddling.
* setpayloadfNx: (libc)FP Bit Twiddling.
* setpayloadl: (libc)FP Bit Twiddling.
* setpayloadsig: (libc)FP Bit Twiddling.
* setpayloadsigf: (libc)FP Bit Twiddling.
* setpayloadsigfN: (libc)FP Bit Twiddling.
* setpayloadsigfNx: (libc)FP Bit Twiddling.
* setpayloadsigl: (libc)FP Bit Twiddling.
* setpgid: (libc)Process Group Functions.
* setpgrp: (libc)Process Group Functions.
* setpriority: (libc)Traditional Scheduling Functions.
* setprotoent: (libc)Protocols Database.
* setpwent: (libc)Scanning All Users.
* setregid: (libc)Setting Groups.
* setreuid: (libc)Setting User ID.
* setrlimit64: (libc)Limits on Resources.
* setrlimit: (libc)Limits on Resources.
* setservent: (libc)Services Database.
* setsid: (libc)Process Group Functions.
* setsockopt: (libc)Socket Option Functions.
* setstate: (libc)BSD Random.
* setstate_r: (libc)BSD Random.
* settimeofday: (libc)Setting and Adjusting the Time.
* setuid: (libc)Setting User ID.
* setutent: (libc)Manipulating the Database.
* setutxent: (libc)XPG Functions.
* setvbuf: (libc)Controlling Buffering.
* shm_open: (libc)Memory-mapped I/O.
* shm_unlink: (libc)Memory-mapped I/O.
* shutdown: (libc)Closing a Socket.
* sigabbrev_np: (libc)Signal Messages.
* sigaction: (libc)Advanced Signal Handling.
* sigaddset: (libc)Signal Sets.
* sigaltstack: (libc)Signal Stack.
* sigblock: (libc)BSD Signal Handling.
* sigdelset: (libc)Signal Sets.
* sigdescr_np: (libc)Signal Messages.
* sigemptyset: (libc)Signal Sets.
* sigfillset: (libc)Signal Sets.
* siginterrupt: (libc)BSD Signal Handling.
* sigismember: (libc)Signal Sets.
* siglongjmp: (libc)Non-Local Exits and Signals.
* sigmask: (libc)BSD Signal Handling.
* signal: (libc)Basic Signal Handling.
* signbit: (libc)FP Bit Twiddling.
* significand: (libc)Normalization Functions.
* significandf: (libc)Normalization Functions.
* significandl: (libc)Normalization Functions.
* sigpause: (libc)BSD Signal Handling.
* sigpending: (libc)Checking for Pending Signals.
* sigprocmask: (libc)Process Signal Mask.
* sigsetjmp: (libc)Non-Local Exits and Signals.
* sigsetmask: (libc)BSD Signal Handling.
* sigstack: (libc)Signal Stack.
* sigsuspend: (libc)Sigsuspend.
* sin: (libc)Trig Functions.
* sincos: (libc)Trig Functions.
* sincosf: (libc)Trig Functions.
* sincosfN: (libc)Trig Functions.
* sincosfNx: (libc)Trig Functions.
* sincosl: (libc)Trig Functions.
* sinf: (libc)Trig Functions.
* sinfN: (libc)Trig Functions.
* sinfNx: (libc)Trig Functions.
* sinh: (libc)Hyperbolic Functions.
* sinhf: (libc)Hyperbolic Functions.
* sinhfN: (libc)Hyperbolic Functions.
* sinhfNx: (libc)Hyperbolic Functions.
* sinhl: (libc)Hyperbolic Functions.
* sinl: (libc)Trig Functions.
* sleep: (libc)Sleeping.
* snprintf: (libc)Formatted Output Functions.
* socket: (libc)Creating a Socket.
* socketpair: (libc)Socket Pairs.
* sprintf: (libc)Formatted Output Functions.
* sqrt: (libc)Exponents and Logarithms.
* sqrtf: (libc)Exponents and Logarithms.
* sqrtfN: (libc)Exponents and Logarithms.
* sqrtfNx: (libc)Exponents and Logarithms.
* sqrtl: (libc)Exponents and Logarithms.
* srand48: (libc)SVID Random.
* srand48_r: (libc)SVID Random.
* srand: (libc)ISO Random.
* srandom: (libc)BSD Random.
* srandom_r: (libc)BSD Random.
* sscanf: (libc)Formatted Input Functions.
* ssignal: (libc)Basic Signal Handling.
* stat64: (libc)Reading Attributes.
* stat: (libc)Reading Attributes.
* stime: (libc)Setting and Adjusting the Time.
* stpcpy: (libc)Copying Strings and Arrays.
* stpncpy: (libc)Truncating Strings.
* strcasecmp: (libc)String/Array Comparison.
* strcasestr: (libc)Search Functions.
* strcat: (libc)Concatenating Strings.
* strchr: (libc)Search Functions.
* strchrnul: (libc)Search Functions.
* strcmp: (libc)String/Array Comparison.
* strcoll: (libc)Collation Functions.
* strcpy: (libc)Copying Strings and Arrays.
* strcspn: (libc)Search Functions.
* strdup: (libc)Copying Strings and Arrays.
* strdupa: (libc)Copying Strings and Arrays.
* strerror: (libc)Error Messages.
* strerror_r: (libc)Error Messages.
* strerrordesc_np: (libc)Error Messages.
* strerrorname_np: (libc)Error Messages.
* strfmon: (libc)Formatting Numbers.
* strfromd: (libc)Printing of Floats.
* strfromf: (libc)Printing of Floats.
* strfromfN: (libc)Printing of Floats.
* strfromfNx: (libc)Printing of Floats.
* strfroml: (libc)Printing of Floats.
* strfry: (libc)Shuffling Bytes.
* strftime: (libc)Formatting Calendar Time.
* strlen: (libc)String Length.
* strncasecmp: (libc)String/Array Comparison.
* strncat: (libc)Truncating Strings.
* strncmp: (libc)String/Array Comparison.
* strncpy: (libc)Truncating Strings.
* strndup: (libc)Truncating Strings.
* strndupa: (libc)Truncating Strings.
* strnlen: (libc)String Length.
* strpbrk: (libc)Search Functions.
* strptime: (libc)Low-Level Time String Parsing.
* strrchr: (libc)Search Functions.
* strsep: (libc)Finding Tokens in a String.
* strsignal: (libc)Signal Messages.
* strspn: (libc)Search Functions.
* strstr: (libc)Search Functions.
* strtod: (libc)Parsing of Floats.
* strtof: (libc)Parsing of Floats.
* strtofN: (libc)Parsing of Floats.
* strtofNx: (libc)Parsing of Floats.
* strtoimax: (libc)Parsing of Integers.
* strtok: (libc)Finding Tokens in a String.
* strtok_r: (libc)Finding Tokens in a String.
* strtol: (libc)Parsing of Integers.
* strtold: (libc)Parsing of Floats.
* strtoll: (libc)Parsing of Integers.
* strtoq: (libc)Parsing of Integers.
* strtoul: (libc)Parsing of Integers.
* strtoull: (libc)Parsing of Integers.
* strtoumax: (libc)Parsing of Integers.
* strtouq: (libc)Parsing of Integers.
* strverscmp: (libc)String/Array Comparison.
* strxfrm: (libc)Collation Functions.
* stty: (libc)BSD Terminal Modes.
* swapcontext: (libc)System V contexts.
* swprintf: (libc)Formatted Output Functions.
* swscanf: (libc)Formatted Input Functions.
* symlink: (libc)Symbolic Links.
* sync: (libc)Synchronizing I/O.
* syscall: (libc)System Calls.
* sysconf: (libc)Sysconf Definition.
* syslog: (libc)syslog; vsyslog.
* system: (libc)Running a Command.
* sysv_signal: (libc)Basic Signal Handling.
* tan: (libc)Trig Functions.
* tanf: (libc)Trig Functions.
* tanfN: (libc)Trig Functions.
* tanfNx: (libc)Trig Functions.
* tanh: (libc)Hyperbolic Functions.
* tanhf: (libc)Hyperbolic Functions.
* tanhfN: (libc)Hyperbolic Functions.
* tanhfNx: (libc)Hyperbolic Functions.
* tanhl: (libc)Hyperbolic Functions.
* tanl: (libc)Trig Functions.
* tcdrain: (libc)Line Control.
* tcflow: (libc)Line Control.
* tcflush: (libc)Line Control.
* tcgetattr: (libc)Mode Functions.
* tcgetpgrp: (libc)Terminal Access Functions.
* tcgetsid: (libc)Terminal Access Functions.
* tcsendbreak: (libc)Line Control.
* tcsetattr: (libc)Mode Functions.
* tcsetpgrp: (libc)Terminal Access Functions.
* tdelete: (libc)Tree Search Function.
* tdestroy: (libc)Tree Search Function.
* telldir: (libc)Random Access Directory.
* tempnam: (libc)Temporary Files.
* textdomain: (libc)Locating gettext catalog.
* tfind: (libc)Tree Search Function.
* tgamma: (libc)Special Functions.
* tgammaf: (libc)Special Functions.
* tgammafN: (libc)Special Functions.
* tgammafNx: (libc)Special Functions.
* tgammal: (libc)Special Functions.
* tgkill: (libc)Signaling Another Process.
* thrd_create: (libc)ISO C Thread Management.
* thrd_current: (libc)ISO C Thread Management.
* thrd_detach: (libc)ISO C Thread Management.
* thrd_equal: (libc)ISO C Thread Management.
* thrd_exit: (libc)ISO C Thread Management.
* thrd_join: (libc)ISO C Thread Management.
* thrd_sleep: (libc)ISO C Thread Management.
* thrd_yield: (libc)ISO C Thread Management.
* time: (libc)Getting the Time.
* timegm: (libc)Broken-down Time.
* timelocal: (libc)Broken-down Time.
* times: (libc)Processor Time.
* tmpfile64: (libc)Temporary Files.
* tmpfile: (libc)Temporary Files.
* tmpnam: (libc)Temporary Files.
* tmpnam_r: (libc)Temporary Files.
* toascii: (libc)Case Conversion.
* tolower: (libc)Case Conversion.
* totalorder: (libc)FP Comparison Functions.
* totalorderf: (libc)FP Comparison Functions.
* totalorderfN: (libc)FP Comparison Functions.
* totalorderfNx: (libc)FP Comparison Functions.
* totalorderl: (libc)FP Comparison Functions.
* totalordermag: (libc)FP Comparison Functions.
* totalordermagf: (libc)FP Comparison Functions.
* totalordermagfN: (libc)FP Comparison Functions.
* totalordermagfNx: (libc)FP Comparison Functions.
* totalordermagl: (libc)FP Comparison Functions.
* toupper: (libc)Case Conversion.
* towctrans: (libc)Wide Character Case Conversion.
* towlower: (libc)Wide Character Case Conversion.
* towupper: (libc)Wide Character Case Conversion.
* trunc: (libc)Rounding Functions.
* truncate64: (libc)File Size.
* truncate: (libc)File Size.
* truncf: (libc)Rounding Functions.
* truncfN: (libc)Rounding Functions.
* truncfNx: (libc)Rounding Functions.
* truncl: (libc)Rounding Functions.
* tsearch: (libc)Tree Search Function.
* tss_create: (libc)ISO C Thread-local Storage.
* tss_delete: (libc)ISO C Thread-local Storage.
* tss_get: (libc)ISO C Thread-local Storage.
* tss_set: (libc)ISO C Thread-local Storage.
* ttyname: (libc)Is It a Terminal.
* ttyname_r: (libc)Is It a Terminal.
* twalk: (libc)Tree Search Function.
* twalk_r: (libc)Tree Search Function.
* tzset: (libc)Time Zone Functions.
* ufromfp: (libc)Rounding Functions.
* ufromfpf: (libc)Rounding Functions.
* ufromfpfN: (libc)Rounding Functions.
* ufromfpfNx: (libc)Rounding Functions.
* ufromfpl: (libc)Rounding Functions.
* ufromfpx: (libc)Rounding Functions.
* ufromfpxf: (libc)Rounding Functions.
* ufromfpxfN: (libc)Rounding Functions.
* ufromfpxfNx: (libc)Rounding Functions.
* ufromfpxl: (libc)Rounding Functions.
* ulimit: (libc)Limits on Resources.
* umask: (libc)Setting Permissions.
* umount2: (libc)Mount-Unmount-Remount.
* umount: (libc)Mount-Unmount-Remount.
* uname: (libc)Platform Type.
* ungetc: (libc)How Unread.
* ungetwc: (libc)How Unread.
* unlink: (libc)Deleting Files.
* unlockpt: (libc)Allocation.
* unsetenv: (libc)Environment Access.
* updwtmp: (libc)Manipulating the Database.
* utime: (libc)File Times.
* utimes: (libc)File Times.
* utmpname: (libc)Manipulating the Database.
* utmpxname: (libc)XPG Functions.
* va_arg: (libc)Argument Macros.
* va_copy: (libc)Argument Macros.
* va_end: (libc)Argument Macros.
* va_start: (libc)Argument Macros.
* valloc: (libc)Aligned Memory Blocks.
* vasprintf: (libc)Variable Arguments Output.
* verr: (libc)Error Messages.
* verrx: (libc)Error Messages.
* versionsort64: (libc)Scanning Directory Content.
* versionsort: (libc)Scanning Directory Content.
* vfork: (libc)Creating a Process.
* vfprintf: (libc)Variable Arguments Output.
* vfscanf: (libc)Variable Arguments Input.
* vfwprintf: (libc)Variable Arguments Output.
* vfwscanf: (libc)Variable Arguments Input.
* vlimit: (libc)Limits on Resources.
* vprintf: (libc)Variable Arguments Output.
* vscanf: (libc)Variable Arguments Input.
* vsnprintf: (libc)Variable Arguments Output.
* vsprintf: (libc)Variable Arguments Output.
* vsscanf: (libc)Variable Arguments Input.
* vswprintf: (libc)Variable Arguments Output.
* vswscanf: (libc)Variable Arguments Input.
* vsyslog: (libc)syslog; vsyslog.
* vwarn: (libc)Error Messages.
* vwarnx: (libc)Error Messages.
* vwprintf: (libc)Variable Arguments Output.
* vwscanf: (libc)Variable Arguments Input.
* wait3: (libc)BSD Wait Functions.
* wait4: (libc)Process Completion.
* wait: (libc)Process Completion.
* waitpid: (libc)Process Completion.
* warn: (libc)Error Messages.
* warnx: (libc)Error Messages.
* wcpcpy: (libc)Copying Strings and Arrays.
* wcpncpy: (libc)Truncating Strings.
* wcrtomb: (libc)Converting a Character.
* wcscasecmp: (libc)String/Array Comparison.
* wcscat: (libc)Concatenating Strings.
* wcschr: (libc)Search Functions.
* wcschrnul: (libc)Search Functions.
* wcscmp: (libc)String/Array Comparison.
* wcscoll: (libc)Collation Functions.
* wcscpy: (libc)Copying Strings and Arrays.
* wcscspn: (libc)Search Functions.
* wcsdup: (libc)Copying Strings and Arrays.
* wcsftime: (libc)Formatting Calendar Time.
* wcslen: (libc)String Length.
* wcsncasecmp: (libc)String/Array Comparison.
* wcsncat: (libc)Truncating Strings.
* wcsncmp: (libc)String/Array Comparison.
* wcsncpy: (libc)Truncating Strings.
* wcsnlen: (libc)String Length.
* wcsnrtombs: (libc)Converting Strings.
* wcspbrk: (libc)Search Functions.
* wcsrchr: (libc)Search Functions.
* wcsrtombs: (libc)Converting Strings.
* wcsspn: (libc)Search Functions.
* wcsstr: (libc)Search Functions.
* wcstod: (libc)Parsing of Floats.
* wcstof: (libc)Parsing of Floats.
* wcstofN: (libc)Parsing of Floats.
* wcstofNx: (libc)Parsing of Floats.
* wcstoimax: (libc)Parsing of Integers.
* wcstok: (libc)Finding Tokens in a String.
* wcstol: (libc)Parsing of Integers.
* wcstold: (libc)Parsing of Floats.
* wcstoll: (libc)Parsing of Integers.
* wcstombs: (libc)Non-reentrant String Conversion.
* wcstoq: (libc)Parsing of Integers.
* wcstoul: (libc)Parsing of Integers.
* wcstoull: (libc)Parsing of Integers.
* wcstoumax: (libc)Parsing of Integers.
* wcstouq: (libc)Parsing of Integers.
* wcswcs: (libc)Search Functions.
* wcsxfrm: (libc)Collation Functions.
* wctob: (libc)Converting a Character.
* wctomb: (libc)Non-reentrant Character Conversion.
* wctrans: (libc)Wide Character Case Conversion.
* wctype: (libc)Classification of Wide Characters.
* wmemchr: (libc)Search Functions.
* wmemcmp: (libc)String/Array Comparison.
* wmemcpy: (libc)Copying Strings and Arrays.
* wmemmove: (libc)Copying Strings and Arrays.
* wmempcpy: (libc)Copying Strings and Arrays.
* wmemset: (libc)Copying Strings and Arrays.
* wordexp: (libc)Calling Wordexp.
* wordfree: (libc)Calling Wordexp.
* wprintf: (libc)Formatted Output Functions.
* write: (libc)I/O Primitives.
* writev: (libc)Scatter-Gather.
* wscanf: (libc)Formatted Input Functions.
* y0: (libc)Special Functions.
* y0f: (libc)Special Functions.
* y0fN: (libc)Special Functions.
* y0fNx: (libc)Special Functions.
* y0l: (libc)Special Functions.
* y1: (libc)Special Functions.
* y1f: (libc)Special Functions.
* y1fN: (libc)Special Functions.
* y1fNx: (libc)Special Functions.
* y1l: (libc)Special Functions.
* yn: (libc)Special Functions.
* ynf: (libc)Special Functions.
* ynfN: (libc)Special Functions.
* ynfNx: (libc)Special Functions.
* ynl: (libc)Special Functions.
END-INFO-DIR-ENTRY


File: libc.info,  Node: Top,  Next: Introduction,  Prev: (dir),  Up: (dir)

Main Menu
*********

This is ‘The GNU C Library Reference Manual’, for version 2.33.

   Copyright © 1993–2021 Free Software Foundation, Inc.

   Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.3 or
any later version published by the Free Software Foundation; with the
Invariant Sections being “Free Software Needs Free Documentation” and
“GNU Lesser General Public License”, the Front-Cover texts being “A GNU
Manual”, and with the Back-Cover Texts as in (a) below.  A copy of the
license is included in the section entitled "GNU Free Documentation
License".

   (a) The FSF’s Back-Cover Text is: “You have the freedom to copy and
modify this GNU manual.  Buying copies from the FSF supports it in
developing GNU and promoting software freedom.”

* Menu:

* Introduction::                 Purpose of the GNU C Library.
* Error Reporting::              How library functions report errors.
* Memory::                       Allocating virtual memory and controlling
                                   paging.
* Character Handling::           Character testing and conversion functions.
* String and Array Utilities::   Utilities for copying and comparing strings
                                   and arrays.
* Character Set Handling::       Support for extended character sets.
* Locales::                      The country and language can affect the
                                   behavior of library functions.
* Message Translation::          How to make the program speak the user’s
                                   language.
* Searching and Sorting::        General searching and sorting functions.
* Pattern Matching::             Matching shell “globs” and regular
                                   expressions.
* I/O Overview::                 Introduction to the I/O facilities.
* I/O on Streams::               High-level, portable I/O facilities.
* Low-Level I/O::                Low-level, less portable I/O.
* File System Interface::        Functions for manipulating files.
* Pipes and FIFOs::              A simple interprocess communication
                                   mechanism.
* Sockets::                      A more complicated IPC mechanism, with
                                   networking support.
* Low-Level Terminal Interface:: How to change the characteristics of a
                                   terminal device.
* Syslog::                       System logging and messaging.
* Mathematics::                  Math functions, useful constants, random
                                   numbers.
* Arithmetic::                   Low level arithmetic functions.
* Date and Time::                Functions for getting the date and time and
                                   formatting them nicely.
* Resource Usage And Limitation:: Functions for examining resource usage and
                                   getting and setting limits.
* Non-Local Exits::              Jumping out of nested function calls.
* Signal Handling::              How to send, block, and handle signals.
* Program Basics::               Writing the beginning and end of your
                                   program.
* Processes::                    How to create processes and run other
                                   programs.
* Inter-Process Communication::  All about inter-process communication.
* Job Control::                  All about process groups and sessions.
* Name Service Switch::          Accessing system databases.
* Users and Groups::             How users are identified and classified.
* System Management::            Controlling the system and getting
                                   information about it.
* System Configuration::         Parameters describing operating system
                                   limits.
* Cryptographic Functions::      Passphrase storage and strongly
                                   unpredictable bytes..
* Debugging Support::            Functions to help debugging applications.
* Threads::                      Functions, constants, and data types for
                                   working with threads.
* Internal Probes::              Probes to monitor libc internal behavior.
* Tunables::                     Tunable switches to alter libc internal
                                   behavior.

Appendices

* Language Features::            C language features provided by the library.
* Library Summary::              A summary showing the syntax, header file,
                                   and derivation of each library feature.
* Installation::                 How to install the GNU C Library.
* Maintenance::                  How to enhance and port the GNU C Library.
* Platform::                     Describe all platform-specific facilities
                                   provided.
* Contributors::                 Who wrote what parts of the GNU C Library.
* Free Manuals::		 Free Software Needs Free Documentation.
* Copying::                      The GNU Lesser General Public License says
                                  how you can copy and share the GNU C Library.
* Documentation License::        This manual is under the GNU Free
                                  Documentation License.

Indices

* Concept Index::                Index of concepts and names.
* Type Index::                   Index of types and type qualifiers.
* Function Index::               Index of functions and function-like macros.
* Variable Index::               Index of variables and variable-like macros.
* File Index::                   Index of programs and files.

 — The Detailed Node Listing —

Introduction

* Getting Started::             What this manual is for and how to use it.
* Standards and Portability::   Standards and sources upon which the GNU
                                 C library is based.
* Using the Library::           Some practical uses for the library.
* Roadmap to the Manual::       Overview of the remaining chapters in
                                 this manual.

Standards and Portability

* ISO C::                       The international standard for the C
                                 programming language.
* POSIX::                       The ISO/IEC 9945 (aka IEEE 1003) standards
                                 for operating systems.
* Berkeley Unix::               BSD and SunOS.
* SVID::                        The System V Interface Description.
* XPG::                         The X/Open Portability Guide.

POSIX

* POSIX Safety Concepts::       Safety concepts from POSIX.
* Unsafe Features::             Features that make functions unsafe.
* Conditionally Safe Features:: Features that make functions unsafe
                                 in the absence of workarounds.
* Other Safety Remarks::        Additional safety features and remarks.

Using the Library

* Header Files::                How to include the header files in your
                                 programs.
* Macro Definitions::           Some functions in the library may really
                                 be implemented as macros.
* Reserved Names::              The C standard reserves some names for
                                 the library, and some for users.
* Feature Test Macros::         How to control what names are defined.

Error Reporting

* Checking for Errors::         How errors are reported by library functions.
* Error Codes::                 Error code macros; all of these expand
                                 into integer constant values.
* Error Messages::              Mapping error codes onto error messages.

Memory

* Memory Concepts::             An introduction to concepts and terminology.
* Memory Allocation::           Allocating storage for your program data
* Resizing the Data Segment::   ‘brk’, ‘sbrk’
* Memory Protection::           Controlling access to memory regions.
* Locking Pages::               Preventing page faults

Memory Allocation

* Memory Allocation and C::     How to get different kinds of allocation in C.
* The GNU Allocator::		An overview of the GNU ‘malloc’
				implementation.
* Unconstrained Allocation::    The ‘malloc’ facility allows fully general
		 		 dynamic allocation.
* Allocation Debugging::        Finding memory leaks and not freed memory.
* Replacing malloc::            Using your own ‘malloc’-style allocator.
* Obstacks::                    Obstacks are less general than malloc
				 but more efficient and convenient.
* Variable Size Automatic::     Allocation of variable-sized blocks
				 of automatic storage that are freed when the
				 calling function returns.

Unconstrained Allocation

* Basic Allocation::            Simple use of ‘malloc’.
* Malloc Examples::             Examples of ‘malloc’.  ‘xmalloc’.
* Freeing after Malloc::        Use ‘free’ to free a block you
				 got with ‘malloc’.
* Changing Block Size::         Use ‘realloc’ to make a block
				 bigger or smaller.
* Allocating Cleared Space::    Use ‘calloc’ to allocate a
				 block and clear it.
* Aligned Memory Blocks::       Allocating specially aligned memory.
* Malloc Tunable Parameters::   Use ‘mallopt’ to adjust allocation
                                 parameters.
* Heap Consistency Checking::   Automatic checking for errors.
* Hooks for Malloc::            You can use these hooks for debugging
				 programs that use ‘malloc’.
* Statistics of Malloc::        Getting information about how much
				 memory your program is using.
* Summary of Malloc::           Summary of ‘malloc’ and related functions.

Allocation Debugging

* Tracing malloc::               How to install the tracing functionality.
* Using the Memory Debugger::    Example programs excerpts.
* Tips for the Memory Debugger:: Some more or less clever ideas.
* Interpreting the traces::      What do all these lines mean?

Obstacks

* Creating Obstacks::		How to declare an obstack in your program.
* Preparing for Obstacks::	Preparations needed before you can
				 use obstacks.
* Allocation in an Obstack::    Allocating objects in an obstack.
* Freeing Obstack Objects::     Freeing objects in an obstack.
* Obstack Functions::		The obstack functions are both
				 functions and macros.
* Growing Objects::             Making an object bigger by stages.
* Extra Fast Growing::		Extra-high-efficiency (though more
				 complicated) growing objects.
* Status of an Obstack::        Inquiries about the status of an obstack.
* Obstacks Data Alignment::     Controlling alignment of objects in obstacks.
* Obstack Chunks::              How obstacks obtain and release chunks;
				 efficiency considerations.
* Summary of Obstacks::

Variable Size Automatic

* Alloca Example::              Example of using ‘alloca’.
* Advantages of Alloca::        Reasons to use ‘alloca’.
* Disadvantages of Alloca::     Reasons to avoid ‘alloca’.
* GNU C Variable-Size Arrays::  Only in GNU C, here is an alternative
				 method of allocating dynamically and
				 freeing automatically.

Locking Pages

* Why Lock Pages::                Reasons to read this section.
* Locked Memory Details::         Everything you need to know locked
                                    memory
* Page Lock Functions::           Here’s how to do it.

Character Handling

* Classification of Characters::       Testing whether characters are
			                letters, digits, punctuation, etc.

* Case Conversion::                    Case mapping, and the like.
* Classification of Wide Characters::  Character class determination for
                                        wide characters.
* Using Wide Char Classes::            Notes on using the wide character
                                        classes.
* Wide Character Case Conversion::     Mapping of wide characters.

String and Array Utilities

* Representation of Strings::   Introduction to basic concepts.
* String/Array Conventions::    Whether to use a string function or an
				 arbitrary array function.
* String Length::               Determining the length of a string.
* Copying Strings and Arrays::  Functions to copy strings and arrays.
* Concatenating Strings::       Functions to concatenate strings while copying.
* Truncating Strings::          Functions to truncate strings while copying.
* String/Array Comparison::     Functions for byte-wise and character-wise
				 comparison.
* Collation Functions::         Functions for collating strings.
* Search Functions::            Searching for a specific element or substring.
* Finding Tokens in a String::  Splitting a string into tokens by looking
				 for delimiters.
* Erasing Sensitive Data::      Clearing memory which contains sensitive
                                 data, after it’s no longer needed.
* Shuffling Bytes::             Or how to flash-cook a string.
* Obfuscating Data::            Reversibly obscuring data from casual view.
* Encode Binary Data::          Encoding and Decoding of Binary Data.
* Argz and Envz Vectors::       Null-separated string vectors.

Argz and Envz Vectors

* Argz Functions::              Operations on argz vectors.
* Envz Functions::              Additional operations on environment vectors.

Character Set Handling

* Extended Char Intro::              Introduction to Extended Characters.
* Charset Function Overview::        Overview about Character Handling
                                      Functions.
* Restartable multibyte conversion:: Restartable multibyte conversion
                                      Functions.
* Non-reentrant Conversion::         Non-reentrant Conversion Function.
* Generic Charset Conversion::       Generic Charset Conversion.

Restartable multibyte conversion

* Selecting the Conversion::     Selecting the conversion and its properties.
* Keeping the state::            Representing the state of the conversion.
* Converting a Character::       Converting Single Characters.
* Converting Strings::           Converting Multibyte and Wide Character
                                  Strings.
* Multibyte Conversion Example:: A Complete Multibyte Conversion Example.

Non-reentrant Conversion

* Non-reentrant Character Conversion::  Non-reentrant Conversion of Single
                                         Characters.
* Non-reentrant String Conversion::     Non-reentrant Conversion of Strings.
* Shift State::                         States in Non-reentrant Functions.

Generic Charset Conversion

* Generic Conversion Interface::    Generic Character Set Conversion Interface.
* iconv Examples::                  A complete ‘iconv’ example.
* Other iconv Implementations::     Some Details about other ‘iconv’
                                     Implementations.
* glibc iconv Implementation::      The ‘iconv’ Implementation in the GNU C
                                     library.

Locales

* Effects of Locale::           Actions affected by the choice of
                                 locale.
* Choosing Locale::             How the user specifies a locale.
* Locale Categories::           Different purposes for which you can
                                 select a locale.
* Setting the Locale::          How a program specifies the locale
                                 with library functions.
* Standard Locales::            Locale names available on all systems.
* Locale Names::                Format of system-specific locale names.
* Locale Information::          How to access the information for the locale.
* Formatting Numbers::          A dedicated function to format numbers.
* Yes-or-No Questions::         Check a Response against the locale.

Locale Information

* The Lame Way to Locale Data::   ISO C’s ‘localeconv’.
* The Elegant and Fast Way::      X/Open’s ‘nl_langinfo’.

The Lame Way to Locale Data

* General Numeric::             Parameters for formatting numbers and
                                 currency amounts.
* Currency Symbol::             How to print the symbol that identifies an
                                 amount of money (e.g. ‘$’).
* Sign of Money Amount::        How to print the (positive or negative) sign
                                 for a monetary amount, if one exists.

Message Translation

* Message catalogs a la X/Open::  The ‘catgets’ family of functions.
* The Uniforum approach::         The ‘gettext’ family of functions.

Message catalogs a la X/Open

* The catgets Functions::      The ‘catgets’ function family.
* The message catalog files::  Format of the message catalog files.
* The gencat program::         How to generate message catalogs files which
                                can be used by the functions.
* Common Usage::               How to use the ‘catgets’ interface.

The Uniforum approach

* Message catalogs with gettext::  The ‘gettext’ family of functions.
* Helper programs for gettext::    Programs to handle message catalogs
                                    for ‘gettext’.

Message catalogs with gettext

* Translation with gettext::       What has to be done to translate a message.
* Locating gettext catalog::       How to determine which catalog to be used.
* Advanced gettext functions::     Additional functions for more complicated
                                    situations.
* Charset conversion in gettext::  How to specify the output character set
                                    ‘gettext’ uses.
* GUI program problems::           How to use ‘gettext’ in GUI programs.
* Using gettextized software::     The possibilities of the user to influence
                                    the way ‘gettext’ works.

Searching and Sorting

* Comparison Functions::        Defining how to compare two objects.
				 Since the sort and search facilities
                                 are general, you have to specify the
                                 ordering.
* Array Search Function::       The ‘bsearch’ function.
* Array Sort Function::         The ‘qsort’ function.
* Search/Sort Example::         An example program.
* Hash Search Function::        The ‘hsearch’ function.
* Tree Search Function::        The ‘tsearch’ function.

Pattern Matching

* Wildcard Matching::    Matching a wildcard pattern against a single string.
* Globbing::             Finding the files that match a wildcard pattern.
* Regular Expressions::  Matching regular expressions against strings.
* Word Expansion::       Expanding shell variables, nested commands,
			    arithmetic, and wildcards.
			    This is what the shell does with shell commands.

Globbing

* Calling Glob::             Basic use of ‘glob’.
* Flags for Globbing::       Flags that enable various options in ‘glob’.
* More Flags for Globbing::  GNU specific extensions to ‘glob’.

Regular Expressions

* POSIX Regexp Compilation::    Using ‘regcomp’ to prepare to match.
* Flags for POSIX Regexps::     Syntax variations for ‘regcomp’.
* Matching POSIX Regexps::      Using ‘regexec’ to match the compiled
				   pattern that you get from ‘regcomp’.
* Regexp Subexpressions::       Finding which parts of the string were matched.
* Subexpression Complications:: Find points of which parts were matched.
* Regexp Cleanup::		Freeing storage; reporting errors.

Word Expansion

* Expansion Stages::            What word expansion does to a string.
* Calling Wordexp::             How to call ‘wordexp’.
* Flags for Wordexp::           Options you can enable in ‘wordexp’.
* Wordexp Example::             A sample program that does word expansion.
* Tilde Expansion::             Details of how tilde expansion works.
* Variable Substitution::       Different types of variable substitution.

I/O Overview

* I/O Concepts::       Some basic information and terminology.
* File Names::         How to refer to a file.

I/O Concepts

* Streams and File Descriptors::    The GNU C Library provides two ways
			             to access the contents of files.
* File Position::                   The number of bytes from the
                                     beginning of the file.

File Names

* Directories::                 Directories contain entries for files.
* File Name Resolution::        A file name specifies how to look up a file.
* File Name Errors::            Error conditions relating to file names.
* File Name Portability::       File name portability and syntax issues.

I/O on Streams

* Streams::                     About the data type representing a stream.
* Standard Streams::            Streams to the standard input and output
				 devices are created for you.
* Opening Streams::             How to create a stream to talk to a file.
* Closing Streams::             Close a stream when you are finished with it.
* Streams and Threads::         Issues with streams in threaded programs.
* Streams and I18N::            Streams in internationalized applications.
* Simple Output::               Unformatted output by characters and lines.
* Character Input::             Unformatted input by characters and words.
* Line Input::                  Reading a line or a record from a stream.
* Unreading::                   Peeking ahead/pushing back input just read.
* Block Input/Output::          Input and output operations on blocks of data.
* Formatted Output::            ‘printf’ and related functions.
* Customizing Printf::          You can define new conversion specifiers for
				 ‘printf’ and friends.
* Formatted Input::             ‘scanf’ and related functions.
* EOF and Errors::              How you can tell if an I/O error happens.
* Error Recovery::		What you can do about errors.
* Binary Streams::              Some systems distinguish between text files
				 and binary files.
* File Positioning::            About random-access streams.
* Portable Positioning::        Random access on peculiar ISO C systems.
* Stream Buffering::            How to control buffering of streams.
* Other Kinds of Streams::      Streams that do not necessarily correspond
				 to an open file.
* Formatted Messages::          Print strictly formatted messages.

Unreading

* Unreading Idea::              An explanation of unreading with pictures.
* How Unread::                  How to call ‘ungetc’ to do unreading.

Formatted Output

* Formatted Output Basics::     Some examples to get you started.
* Output Conversion Syntax::    General syntax of conversion
				 specifications.
* Table of Output Conversions:: Summary of output conversions and
				 what they do.
* Integer Conversions::         Details about formatting of integers.
* Floating-Point Conversions::  Details about formatting of
				 floating-point numbers.
* Other Output Conversions::    Details about formatting of strings,
				 characters, pointers, and the like.
* Formatted Output Functions::  Descriptions of the actual functions.
* Dynamic Output::		Functions that allocate memory for the output.
* Variable Arguments Output::   ‘vprintf’ and friends.
* Parsing a Template String::   What kinds of args does a given template
				 call for?
* Example of Parsing::          Sample program using ‘parse_printf_format’.

Customizing Printf

* Registering New Conversions::         Using ‘register_printf_function’
					 to register a new output conversion.
* Conversion Specifier Options::        The handler must be able to get
					 the options specified in the
					 template when it is called.
* Defining the Output Handler::         Defining the handler and arginfo
					 functions that are passed as arguments
					 to ‘register_printf_function’.
* Printf Extension Example::            How to define a ‘printf’
					 handler function.
* Predefined Printf Handlers::          Predefined ‘printf’ handlers.

Formatted Input

* Formatted Input Basics::      Some basics to get you started.
* Input Conversion Syntax::     Syntax of conversion specifications.
* Table of Input Conversions::  Summary of input conversions and what they do.
* Numeric Input Conversions::   Details of conversions for reading numbers.
* String Input Conversions::    Details of conversions for reading strings.
* Dynamic String Input::	String conversions that ‘malloc’ the buffer.
* Other Input Conversions::     Details of miscellaneous other conversions.
* Formatted Input Functions::   Descriptions of the actual functions.
* Variable Arguments Input::    ‘vscanf’ and friends.

Stream Buffering

* Buffering Concepts::          Terminology is defined here.
* Flushing Buffers::            How to ensure that output buffers are flushed.
* Controlling Buffering::       How to specify what kind of buffering to use.

Other Kinds of Streams

* String Streams::              Streams that get data from or put data in
				 a string or memory buffer.
* Custom Streams::              Defining your own streams with an arbitrary
				 input data source and/or output data sink.

Custom Streams

* Streams and Cookies::         The “cookie” records where to fetch or
				 store data that is read or written.
* Hook Functions::              How you should define the four “hook
				 functions” that a custom stream needs.

Formatted Messages

* Printing Formatted Messages::   The ‘fmtmsg’ function.
* Adding Severity Classes::       Add more severity classes.
* Example::                       How to use ‘fmtmsg’ and ‘addseverity’.

Low-Level I/O

* Opening and Closing Files::           How to open and close file
                                         descriptors.
* I/O Primitives::                      Reading and writing data.
* File Position Primitive::             Setting a descriptor’s file
                                         position.
* Descriptors and Streams::             Converting descriptor to stream
                                         or vice-versa.
* Stream/Descriptor Precautions::       Precautions needed if you use both
                                         descriptors and streams.
* Scatter-Gather::                      Fast I/O to discontinuous buffers.
* Copying File Data::                   Copying data between files.
* Memory-mapped I/O::                   Using files like memory.
* Waiting for I/O::                     How to check for input or output
					 on multiple file descriptors.
* Synchronizing I/O::                   Making sure all I/O actions completed.
* Asynchronous I/O::                    Perform I/O in parallel.
* Control Operations::                  Various other operations on file
					 descriptors.
* Duplicating Descriptors::             Fcntl commands for duplicating
                                         file descriptors.
* Descriptor Flags::                    Fcntl commands for manipulating
                                         flags associated with file
                                         descriptors.
* File Status Flags::                   Fcntl commands for manipulating
                                         flags associated with open files.
* File Locks::                          Fcntl commands for implementing
                                         file locking.
* Open File Description Locks::         Fcntl commands for implementing
                                         open file description locking.
* Open File Description Locks Example:: An example of open file description lock
                                         usage
* Interrupt Input::                     Getting an asynchronous signal when
                                         input arrives.
* IOCTLs::                              Generic I/O Control operations.

Stream/Descriptor Precautions

* Linked Channels::	   Dealing with channels sharing a file position.
* Independent Channels::   Dealing with separately opened, unlinked channels.
* Cleaning Streams::	   Cleaning a stream makes it safe to use
                            another channel.

Asynchronous I/O

* Asynchronous Reads/Writes::    Asynchronous Read and Write Operations.
* Status of AIO Operations::     Getting the Status of AIO Operations.
* Synchronizing AIO Operations:: Getting into a consistent state.
* Cancel AIO Operations::        Cancellation of AIO Operations.
* Configuration of AIO::         How to optimize the AIO implementation.

File Status Flags

* Access Modes::                Whether the descriptor can read or write.
* Open-time Flags::             Details of ‘open’.
* Operating Modes::             Special modes to control I/O operations.
* Getting File Status Flags::   Fetching and changing these flags.

File System Interface

* Working Directory::           This is used to resolve relative
				 file names.
* Accessing Directories::       Finding out what files a directory
				 contains.
* Working with Directory Trees:: Apply actions to all files or a selectable
                                 subset of a directory hierarchy.
* Hard Links::                  Adding alternate names to a file.
* Symbolic Links::              A file that “points to” a file name.
* Deleting Files::              How to delete a file, and what that means.
* Renaming Files::              Changing a file’s name.
* Creating Directories::        A system call just for creating a directory.
* File Attributes::             Attributes of individual files.
* Making Special Files::        How to create special files.
* Temporary Files::             Naming and creating temporary files.

Accessing Directories

* Directory Entries::           Format of one directory entry.
* Opening a Directory::         How to open a directory stream.
* Reading/Closing Directory::   How to read directory entries from the stream.
* Simple Directory Lister::     A very simple directory listing program.
* Random Access Directory::     Rereading part of the directory
                                 already read with the same stream.
* Scanning Directory Content::  Get entries for user selected subset of
                                 contents in given directory.
* Simple Directory Lister Mark II::  Revised version of the program.
* Low-level Directory Access::  AS-Safe functions for directory access.

File Attributes

* Attribute Meanings::          The names of the file attributes,
                                 and what their values mean.
* Reading Attributes::          How to read the attributes of a file.
* Testing File Type::           Distinguishing ordinary files,
                                 directories, links...
* File Owner::                  How ownership for new files is determined,
			         and how to change it.
* Permission Bits::             How information about a file’s access
                                 mode is stored.
* Access Permission::           How the system decides who can access a file.
* Setting Permissions::         How permissions for new files are assigned,
			         and how to change them.
* Testing File Access::         How to find out if your process can
                                 access a file.
* File Times::                  About the time attributes of a file.
* File Size::			Manually changing the size of a file.
* Storage Allocation::          Allocate backing storage for files.

Pipes and FIFOs

* Creating a Pipe::             Making a pipe with the ‘pipe’ function.
* Pipe to a Subprocess::        Using a pipe to communicate with a
				 child process.
* FIFO Special Files::          Making a FIFO special file.
* Pipe Atomicity::		When pipe (or FIFO) I/O is atomic.

Sockets

* Socket Concepts::	Basic concepts you need to know about.
* Communication Styles::Stream communication, datagrams and other styles.
* Socket Addresses::	How socket names (“addresses”) work.
* Interface Naming::	Identifying specific network interfaces.
* Local Namespace::	Details about the local namespace.
* Internet Namespace::	Details about the Internet namespace.
* Misc Namespaces::	Other namespaces not documented fully here.
* Open/Close Sockets::  Creating sockets and destroying them.
* Connections::		Operations on sockets with connection state.
* Datagrams::		Operations on datagram sockets.
* Inetd::		Inetd is a daemon that starts servers on request.
			   The most convenient way to write a server
			   is to make it work with Inetd.
* Socket Options::	Miscellaneous low-level socket options.
* Networks Database::   Accessing the database of network names.

Socket Addresses

* Address Formats::		About ‘struct sockaddr’.
* Setting Address::		Binding an address to a socket.
* Reading Address::		Reading the address of a socket.

Local Namespace

* Concepts: Local Namespace Concepts. What you need to understand.
* Details: Local Namespace Details.   Address format, symbolic names, etc.
* Example: Local Socket Example.      Example of creating a socket.

Internet Namespace

* Internet Address Formats::    How socket addresses are specified in the
                                 Internet namespace.
* Host Addresses::	        All about host addresses of Internet host.
* Ports::			Internet port numbers.
* Services Database::           Ports may have symbolic names.
* Byte Order::		        Different hosts may use different byte
                                 ordering conventions; you need to
                                 canonicalize host address and port number.
* Protocols Database::		Referring to protocols by name.
* Inet Example::	        Putting it all together.

Host Addresses

* Abstract Host Addresses::	What a host number consists of.
* Data type: Host Address Data Type.	Data type for a host number.
* Functions: Host Address Functions.	Functions to operate on them.
* Names: Host Names.		Translating host names to host numbers.

Open/Close Sockets

* Creating a Socket::           How to open a socket.
* Closing a Socket::            How to close a socket.
* Socket Pairs::                These are created like pipes.

Connections

* Connecting::    	     What the client program must do.
* Listening::		     How a server program waits for requests.
* Accepting Connections::    What the server does when it gets a request.
* Who is Connected::	     Getting the address of the
				other side of a connection.
* Transferring Data::        How to send and receive data.
* Byte Stream Example::	     An example program: a client for communicating
			      over a byte stream socket in the Internet namespace.
* Server Example::	     A corresponding server program.
* Out-of-Band Data::         This is an advanced feature.

Transferring Data

* Sending Data::		Sending data with ‘send’.
* Receiving Data::		Reading data with ‘recv’.
* Socket Data Options::		Using ‘send’ and ‘recv’.

Datagrams

* Sending Datagrams::    Sending packets on a datagram socket.
* Receiving Datagrams::  Receiving packets on a datagram socket.
* Datagram Example::     An example program: packets sent over a
                           datagram socket in the local namespace.
* Example Receiver::	 Another program, that receives those packets.

Inetd

* Inetd Servers::
* Configuring Inetd::

Socket Options

* Socket Option Functions::     The basic functions for setting and getting
                                 socket options.
* Socket-Level Options::        Details of the options at the socket level.

Low-Level Terminal Interface

* Is It a Terminal::            How to determine if a file is a terminal
			         device, and what its name is.
* I/O Queues::                  About flow control and typeahead.
* Canonical or Not::            Two basic styles of input processing.
* Terminal Modes::              How to examine and modify flags controlling
			         details of terminal I/O: echoing,
                                 signals, editing.  Posix.
* BSD Terminal Modes::          BSD compatible terminal mode setting
* Line Control::                Sending break sequences, clearing
                                 terminal buffers ...
* Noncanon Example::            How to read single characters without echo.
* getpass::                     Prompting the user for a passphrase.
* Pseudo-Terminals::            How to open a pseudo-terminal.

Terminal Modes

* Mode Data Types::             The data type ‘struct termios’ and
                                 related types.
* Mode Functions::              Functions to read and set the terminal
                                 attributes.
* Setting Modes::               The right way to set terminal attributes
                                 reliably.
* Input Modes::                 Flags controlling low-level input handling.
* Output Modes::                Flags controlling low-level output handling.
* Control Modes::               Flags controlling serial port behavior.
* Local Modes::                 Flags controlling high-level input handling.
* Line Speed::                  How to read and set the terminal line speed.
* Special Characters::          Characters that have special effects,
			         and how to change them.
* Noncanonical Input::          Controlling how long to wait for input.

Special Characters

* Editing Characters::          Special characters that terminate lines and
                                  delete text, and other editing functions.
* Signal Characters::           Special characters that send or raise signals
                                  to or for certain classes of processes.
* Start/Stop Characters::       Special characters that suspend or resume
                                  suspended output.
* Other Special::		Other special characters for BSD systems:
				  they can discard output, and print status.

Pseudo-Terminals

* Allocation::             Allocating a pseudo terminal.
* Pseudo-Terminal Pairs::  How to open both sides of a
                            pseudo-terminal in a single operation.

Syslog

* Overview of Syslog::           Overview of a system’s Syslog facility
* Submitting Syslog Messages::   Functions to submit messages to Syslog

Submitting Syslog Messages

* openlog::                      Open connection to Syslog
* syslog; vsyslog::              Submit message to Syslog
* closelog::                     Close connection to Syslog
* setlogmask::                   Cause certain messages to be ignored
* Syslog Example::               Example of all of the above

Mathematics

* Mathematical Constants::      Precise numeric values for often-used
                                 constants.
* Trig Functions::              Sine, cosine, tangent, and friends.
* Inverse Trig Functions::      Arcsine, arccosine, etc.
* Exponents and Logarithms::    Also pow and sqrt.
* Hyperbolic Functions::        sinh, cosh, tanh, etc.
* Special Functions::           Bessel, gamma, erf.
* Errors in Math Functions::    Known Maximum Errors in Math Functions.
* Pseudo-Random Numbers::       Functions for generating pseudo-random
				 numbers.
* FP Function Optimizations::   Fast code or small code.

Pseudo-Random Numbers

* ISO Random::                  ‘rand’ and friends.
* BSD Random::                  ‘random’ and friends.
* SVID Random::                 ‘drand48’ and friends.

Arithmetic

* Integers::                    Basic integer types and concepts
* Integer Division::            Integer division with guaranteed rounding.
* Floating Point Numbers::      Basic concepts.  IEEE 754.
* Floating Point Classes::      The five kinds of floating-point number.
* Floating Point Errors::       When something goes wrong in a calculation.
* Rounding::                    Controlling how results are rounded.
* Control Functions::           Saving and restoring the FPU’s state.
* Arithmetic Functions::        Fundamental operations provided by the library.
* Complex Numbers::             The types.  Writing complex constants.
* Operations on Complex::       Projection, conjugation, decomposition.
* Parsing of Numbers::          Converting strings to numbers.
* Printing of Floats::          Converting floating-point numbers to strings.
* System V Number Conversion::  An archaic way to convert numbers to strings.

Floating Point Errors

* FP Exceptions::               IEEE 754 math exceptions and how to detect them.
* Infinity and NaN::            Special values returned by calculations.
* Status bit operations::       Checking for exceptions after the fact.
* Math Error Reporting::        How the math functions report errors.

Arithmetic Functions

* Absolute Value::              Absolute values of integers and floats.
* Normalization Functions::     Extracting exponents and putting them back.
* Rounding Functions::          Rounding floats to integers.
* Remainder Functions::         Remainders on division, precisely defined.
* FP Bit Twiddling::            Sign bit adjustment.  Adding epsilon.
* FP Comparison Functions::     Comparisons without risk of exceptions.
* Misc FP Arithmetic::          Max, min, positive difference, multiply-add.

Parsing of Numbers

* Parsing of Integers::         Functions for conversion of integer values.
* Parsing of Floats::           Functions for conversion of floating-point
				 values.

Date and Time

* Time Basics::                 Concepts and definitions.
* Time Types::                  Data types to represent time.
* Calculating Elapsed Time::    How to calculate the length of an interval.
* Processor And CPU Time::      Time a program has spent executing.
* Calendar Time::               Manipulation of “real” dates and times.
* Setting an Alarm::            Sending a signal after a specified time.
* Sleeping::                    Waiting for a period of time.

Processor And CPU Time

* CPU Time::                    The ‘clock’ function.
* Processor Time::              The ‘times’ function.

Calendar Time

* Getting the Time::            Functions for finding out what time it is.
* Setting and Adjusting the Time::
                                Functions for setting and adjusting
                                  the system clock.
* Broken-down Time::            Facilities for manipulating local time.
* Formatting Calendar Time::    Converting times to strings.
* Parsing Date and Time::       Convert textual time and date information back
                                 into broken-down time values.
* TZ Variable::                 How users specify the time zone.
* Time Zone Functions::         Functions to examine or specify the time zone.
* Time Functions Example::      An example program showing use of some of
				 the time functions.

Parsing Date and Time

* Low-Level Time String Parsing::  Interpret string according to given format.
* General Time String Parsing::    User-friendly function to parse data and
                                    time strings.

Resource Usage And Limitation

* Resource Usage::		Measuring various resources used.
* Limits on Resources::		Specifying limits on resource usage.
* Priority::			Reading or setting process run priority.
* Memory Resources::            Querying memory available resources.
* Processor Resources::         Learn about the processors available.

Priority

* Absolute Priority::               The first tier of priority.  Posix
* Realtime Scheduling::             Scheduling among the process nobility
* Basic Scheduling Functions::      Get/set scheduling policy, priority
* Traditional Scheduling::          Scheduling among the vulgar masses
* CPU Affinity::                    Limiting execution to certain CPUs

Traditional Scheduling

* Traditional Scheduling Intro::
* Traditional Scheduling Functions::

Memory Resources

* Memory Subsystem::           Overview about traditional Unix memory handling.
* Query Memory Parameters::    How to get information about the memory
                                subsystem?

Non-Local Exits

* Intro: Non-Local Intro.        When and how to use these facilities.
* Details: Non-Local Details.    Functions for non-local exits.
* Non-Local Exits and Signals::  Portability issues.
* System V contexts::            Complete context control a la System V.

Signal Handling

* Concepts of Signals::         Introduction to the signal facilities.
* Standard Signals::            Particular kinds of signals with
                                 standard names and meanings.
* Signal Actions::              Specifying what happens when a
                                 particular signal is delivered.
* Defining Handlers::           How to write a signal handler function.
* Interrupted Primitives::	Signal handlers affect use of ‘open’,
				 ‘read’, ‘write’ and other functions.
* Generating Signals::          How to send a signal to a process.
* Blocking Signals::            Making the system hold signals temporarily.
* Waiting for a Signal::        Suspending your program until a signal
                                 arrives.
* Signal Stack::                Using a Separate Signal Stack.
* BSD Signal Handling::         Additional functions for backward
			         compatibility with BSD.

Concepts of Signals

* Kinds of Signals::            Some examples of what can cause a signal.
* Signal Generation::           Concepts of why and how signals occur.
* Delivery of Signal::          Concepts of what a signal does to the
                                 process.

Standard Signals

* Program Error Signals::       Used to report serious program errors.
* Termination Signals::         Used to interrupt and/or terminate the
                                 program.
* Alarm Signals::               Used to indicate expiration of timers.
* Asynchronous I/O Signals::    Used to indicate input is available.
* Job Control Signals::         Signals used to support job control.
* Operation Error Signals::     Used to report operational system errors.
* Miscellaneous Signals::       Miscellaneous Signals.
* Signal Messages::             Printing a message describing a signal.

Signal Actions

* Basic Signal Handling::       The simple ‘signal’ function.
* Advanced Signal Handling::    The more powerful ‘sigaction’ function.
* Signal and Sigaction::        How those two functions interact.
* Sigaction Function Example::  An example of using the sigaction function.
* Flags for Sigaction::         Specifying options for signal handling.
* Initial Signal Actions::      How programs inherit signal actions.

Defining Handlers

* Handler Returns::             Handlers that return normally, and what
                                 this means.
* Termination in Handler::      How handler functions terminate a program.
* Longjmp in Handler::          Nonlocal transfer of control out of a
                                 signal handler.
* Signals in Handler::          What happens when signals arrive while
                                 the handler is already occupied.
* Merged Signals::		When a second signal arrives before the
				 first is handled.
* Nonreentrancy::               Do not call any functions unless you know they
                                 are reentrant with respect to signals.
* Atomic Data Access::          A single handler can run in the middle of
                                 reading or writing a single object.

Atomic Data Access

* Non-atomic Example::		A program illustrating interrupted access.
* Types: Atomic Types.		Data types that guarantee no interruption.
* Usage: Atomic Usage.		Proving that interruption is harmless.

Generating Signals

* Signaling Yourself::          A process can send a signal to itself.
* Signaling Another Process::   Send a signal to another process.
* Permission for kill::         Permission for using ‘kill’.
* Kill Example::                Using ‘kill’ for Communication.

Blocking Signals

* Why Block::                           The purpose of blocking signals.
* Signal Sets::                         How to specify which signals to
                                         block.
* Process Signal Mask::                 Blocking delivery of signals to your
				         process during normal execution.
* Testing for Delivery::                Blocking to Test for Delivery of
                                         a Signal.
* Blocking for Handler::                Blocking additional signals while a
				         handler is being run.
* Checking for Pending Signals::        Checking for Pending Signals
* Remembering a Signal::                How you can get almost the same
                                         effect as blocking a signal, by
                                         handling it and setting a flag
                                         to be tested later.

Waiting for a Signal

* Using Pause::                 The simple way, using ‘pause’.
* Pause Problems::              Why the simple way is often not very good.
* Sigsuspend::                  Reliably waiting for a specific signal.

Program Basics

* Program Arguments::           Parsing your program’s command-line arguments
* Environment Variables::       Less direct parameters affecting your program
* Auxiliary Vector::            Least direct parameters affecting your program
* System Calls::                Requesting service from the system
* Program Termination::         Telling the system you’re done; return status

Program Arguments

* Argument Syntax::             By convention, options start with a hyphen.
* Parsing Program Arguments::   Ways to parse program options and arguments.

Parsing Program Arguments

* Getopt::                      Parsing program options using ‘getopt’.
* Argp::                        Parsing program options using ‘argp_parse’.
* Suboptions::                  Some programs need more detailed options.
* Suboptions Example::          This shows how it could be done for ‘mount’.

Environment Variables

* Environment Access::          How to get and set the values of
				 environment variables.
* Standard Environment::        These environment variables have
                		 standard interpretations.

Program Termination

* Normal Termination::          If a program calls ‘exit’, a
                                 process terminates normally.
* Exit Status::                 The ‘exit status’ provides information
                                 about why the process terminated.
* Cleanups on Exit::            A process can run its own cleanup
                                 functions upon normal termination.
* Aborting a Program::          The ‘abort’ function causes
                                 abnormal program termination.
* Termination Internals::       What happens when a process terminates.

Processes

* Running a Command::           The easy way to run another program.
* Process Creation Concepts::   An overview of the hard way to do it.
* Process Identification::      How to get the process ID of a process.
* Creating a Process::          How to fork a child process.
* Executing a File::            How to make a process execute another program.
* Process Completion::          How to tell when a child process has completed.
* Process Completion Status::   How to interpret the status value
                                 returned from a child process.
* BSD Wait Functions::  	More functions, for backward compatibility.
* Process Creation Example::    A complete example program.

Inter-Process Communication

* Semaphores::	Support for creating and managing semaphores

Job Control

* Concepts of Job Control::     Jobs can be controlled by a shell.
* Controlling Terminal::        How a process gets its controlling terminal.
* Access to the Terminal::      How processes share the controlling terminal.
* Orphaned Process Groups::     Jobs left after the user logs out.
* Implementing a Shell::        What a shell must do to implement job control.
* Functions for Job Control::   Functions to control process groups.

Implementing a Shell

* Data Structures::             Introduction to the sample shell.
* Initializing the Shell::      What the shell must do to take
				 responsibility for job control.
* Launching Jobs::              Creating jobs to execute commands.
* Foreground and Background::   Putting a job in foreground of background.
* Stopped and Terminated Jobs::  Reporting job status.
* Continuing Stopped Jobs::     How to continue a stopped job in
				 the foreground or background.
* Missing Pieces::              Other parts of the shell.

Functions for Job Control

* Identifying the Terminal::    Determining the controlling terminal’s name.
* Process Group Functions::     Functions for manipulating process groups.
* Terminal Access Functions::   Functions for controlling terminal access.

Name Service Switch

* NSS Basics::                  What is this NSS good for.
* NSS Configuration File::      Configuring NSS.
* NSS Module Internals::        How does it work internally.
* Extending NSS::               What to do to add services or databases.

NSS Configuration File

* Services in the NSS configuration::  Service names in the NSS configuration.
* Actions in the NSS configuration::  React appropriately to the lookup result.
* Notes on NSS Configuration File::  Things to take care about while
                                     configuring NSS.

NSS Module Internals

* NSS Module Names::            Construction of the interface function of
                                the NSS modules.
* NSS Modules Interface::       Programming interface in the NSS module
                                functions.

Extending NSS

* Adding another Service to NSS::  What is to do to add a new service.
* NSS Module Function Internals::  Guidelines for writing new NSS
                                        service functions.

Users and Groups

* User and Group IDs::          Each user has a unique numeric ID;
				 likewise for groups.
* Process Persona::             The user IDs and group IDs of a process.
* Why Change Persona::          Why a program might need to change
				 its user and/or group IDs.
* How Change Persona::          Changing the user and group IDs.
* Reading Persona::             How to examine the user and group IDs.

* Setting User ID::             Functions for setting the user ID.
* Setting Groups::              Functions for setting the group IDs.

* Enable/Disable Setuid::       Turning setuid access on and off.
* Setuid Program Example::      The pertinent parts of one sample program.
* Tips for Setuid::             How to avoid granting unlimited access.

* Who Logged In::               Getting the name of the user who logged in,
				 or of the real user ID of the current process.

* User Accounting Database::    Keeping information about users and various
                                 actions in databases.

* User Database::               Functions and data structures for
                        	 accessing the user database.
* Group Database::              Functions and data structures for
                        	 accessing the group database.
* Database Example::            Example program showing the use of database
				 inquiry functions.
* Netgroup Database::           Functions for accessing the netgroup database.

User Accounting Database

* Manipulating the Database::   Scanning and modifying the user
                                 accounting database.
* XPG Functions::               A standardized way for doing the same thing.
* Logging In and Out::          Functions from BSD that modify the user
                                 accounting database.

User Database

* User Data Structure::         What each user record contains.
* Lookup User::                 How to look for a particular user.
* Scanning All Users::          Scanning the list of all users, one by one.
* Writing a User Entry::        How a program can rewrite a user’s record.

Group Database

* Group Data Structure::        What each group record contains.
* Lookup Group::                How to look for a particular group.
* Scanning All Groups::         Scanning the list of all groups.

Netgroup Database

* Netgroup Data::                  Data in the Netgroup database and where
                                   it comes from.
* Lookup Netgroup::                How to look for a particular netgroup.
* Netgroup Membership::            How to test for netgroup membership.

System Management

* Host Identification::         Determining the name of the machine.
* Platform Type::               Determining operating system and basic
                                  machine type
* Filesystem Handling::         Controlling/querying mounts

Filesystem Handling

* Mount Information::           What is or could be mounted?
* Mount-Unmount-Remount::       Controlling what is mounted and how

Mount Information

* fstab::                       The ‘fstab’ file
* mtab::                        The ‘mtab’ file
* Other Mount Information::     Other (non-libc) sources of mount information

System Configuration

* General Limits::           Constants and functions that describe
				various process-related limits that have
				one uniform value for any given machine.
* System Options::           Optional POSIX features.
* Version Supported::        Version numbers of POSIX.1 and POSIX.2.
* Sysconf::                  Getting specific configuration values
                                of general limits and system options.
* Minimums::                 Minimum values for general limits.

* Limits for Files::         Size limitations that pertain to individual files.
                                These can vary between file systems
                                or even from file to file.
* Options for Files::        Optional features that some files may support.
* File Minimums::            Minimum values for file limits.
* Pathconf::                 Getting the limit values for a particular file.

* Utility Limits::           Capacity limits of some POSIX.2 utility programs.
* Utility Minimums::         Minimum allowable values of those limits.

* String Parameters::        Getting the default search path.

Sysconf

* Sysconf Definition::        Detailed specifications of ‘sysconf’.
* Constants for Sysconf::     The list of parameters ‘sysconf’ can read.
* Examples of Sysconf::       How to use ‘sysconf’ and the parameter
				 macros properly together.

Cryptographic Functions

* Passphrase Storage::          One-way hashing for passphrases.
* Unpredictable Bytes::         Randomness for cryptographic purposes.

Debugging Support

* Backtraces::                Obtaining and printing a back trace of the
                               current stack.

Threads

* ISO C Threads::	Threads based on the ISO C specification.
* POSIX Threads::	Threads based on the POSIX specification.

ISO C Threads

* ISO C Threads Return Values:: Symbolic constants that represent a
				function’s return value.
* ISO C Thread Management::	Support for basic threading.
* Call Once::			Single-call functions and macros.
* ISO C Mutexes::		A low-level mechanism for mutual exclusion.
* ISO C Condition Variables::	High-level objects for thread synchronization.
* ISO C Thread-local Storage::	Functions to support thread-local storage.

POSIX Threads

* Thread-specific Data::          Support for creating and
				  managing thread-specific data
* Non-POSIX Extensions::          Additional functions to extend
				  POSIX Thread functionality

Non-POSIX Extensions

* Default Thread Attributes::             Setting default attributes for
					  threads in a process.
* Initial Thread Signal Mask::            Setting the initial mask of threads.
* Waiting with Explicit Clocks::          Functions for waiting with an
                                          explicit clock specification.
* Single-Threaded::                       Detecting single-threaded execution.

Internal Probes

* Memory Allocation Probes::  Probes in the memory allocation subsystem
* Mathematical Function Probes::  Probes in mathematical functions
* Non-local Goto Probes::  Probes in setjmp and longjmp

Tunables

* Tunable names::  The structure of a tunable name
* Memory Allocation Tunables::  Tunables in the memory allocation subsystem
* Dynamic Linking Tunables:: Tunables in the dynamic linking subsystem
* Elision Tunables::  Tunables in elision subsystem
* POSIX Thread Tunables:: Tunables in the POSIX thread subsystem
* Hardware Capability Tunables::  Tunables that modify the hardware
				  capabilities seen by the GNU C Library
* Memory Related Tunables::  Tunables that control the use of memory by
			     the GNU C Library.

Language Features

* Consistency Checking::        Using ‘assert’ to abort if
				 something “impossible” happens.
* Variadic Functions::          Defining functions with varying numbers
                                 of args.
* Null Pointer Constant::       The macro ‘NULL’.
* Important Data Types::        Data types for object sizes.
* Data Type Measurements::      Parameters of data type representations.

Variadic Functions

* Why Variadic::                Reasons for making functions take
                                 variable arguments.
* How Variadic::                How to define and call variadic functions.
* Variadic Example::            A complete example.

How Variadic

* Variadic Prototypes::  How to make a prototype for a function
			  with variable arguments.
* Receiving Arguments::  Steps you must follow to access the
			  optional argument values.
* How Many Arguments::   How to decide whether there are more arguments.
* Calling Variadics::    Things you need to know about calling
			  variable arguments functions.
* Argument Macros::      Detailed specification of the macros
        		  for accessing variable arguments.

Data Type Measurements

* Width of Type::           How many bits does an integer type hold?
* Range of Type::           What are the largest and smallest values
			     that an integer type can hold?
* Floating Type Macros::    Parameters that measure the floating point types.
* Structure Measurement::   Getting measurements on structure types.

Floating Type Macros

* Floating Point Concepts::     Definitions of terminology.
* Floating Point Parameters::   Details of specific macros.
* IEEE Floating Point::         The measurements for one common
                                 representation.

Installation

* Configuring and compiling::   How to compile and test GNU libc.
* Running make install::        How to install it once you’ve got it
 compiled.
* Tools for Compilation::       You’ll need these first.
* Linux::                       Specific advice for GNU/Linux systems.
* Reporting Bugs::              So they’ll get fixed.

Maintenance

* Source Layout::         How to add new functions or header files
                             to the GNU C Library.
* Symbol handling::       How to handle symbols in the GNU C Library.
* Porting::               How to port the GNU C Library to
                             a new machine or operating system.

Source Layout

* Platform: Adding Platform-specific.             Adding platform-specific
                                         features.

Symbol handling

* 64-bit time symbol handling :: How to handle 64-bit time related
                                    symbols in the GNU C Library.

Porting

* Hierarchy Conventions::       The layout of the ‘sysdeps’ hierarchy.
* Porting to Unix::             Porting the library to an average
                                   Unix-like system.

Platform

* PowerPC::           Facilities Specific to the PowerPC Architecture
* RISC-V::            Facilities Specific to the RISC-V Architecture
* X86::               Facilities Specific to the X86 Architecture


File: libc.info,  Node: Introduction,  Next: Error Reporting,  Prev: Top,  Up: Top

1 Introduction
**************

The C language provides no built-in facilities for performing such
common operations as input/output, memory management, string
manipulation, and the like.  Instead, these facilities are defined in a
standard “library”, which you compile and link with your programs.

   The GNU C Library, described in this document, defines all of the
library functions that are specified by the ISO C standard, as well as
additional features specific to POSIX and other derivatives of the Unix
operating system, and extensions specific to GNU systems.

   The purpose of this manual is to tell you how to use the facilities
of the GNU C Library.  We have mentioned which features belong to which
standards to help you identify things that are potentially non-portable
to other systems.  But the emphasis in this manual is not on strict
portability.

* Menu:

* Getting Started::             What this manual is for and how to use it.
* Standards and Portability::   Standards and sources upon which the GNU
                                 C library is based.
* Using the Library::           Some practical uses for the library.
* Roadmap to the Manual::       Overview of the remaining chapters in
                                 this manual.


File: libc.info,  Node: Getting Started,  Next: Standards and Portability,  Up: Introduction

1.1 Getting Started
===================

This manual is written with the assumption that you are at least
somewhat familiar with the C programming language and basic programming
concepts.  Specifically, familiarity with ISO standard C (*note ISO
C::), rather than “traditional” pre-ISO C dialects, is assumed.

   The GNU C Library includes several “header files”, each of which
provides definitions and declarations for a group of related facilities;
this information is used by the C compiler when processing your program.
For example, the header file ‘stdio.h’ declares facilities for
performing input and output, and the header file ‘string.h’ declares
string processing utilities.  The organization of this manual generally
follows the same division as the header files.

   If you are reading this manual for the first time, you should read
all of the introductory material and skim the remaining chapters.  There
are a _lot_ of functions in the GNU C Library and it’s not realistic to
expect that you will be able to remember exactly _how_ to use each and
every one of them.  It’s more important to become generally familiar
with the kinds of facilities that the library provides, so that when you
are writing your programs you can recognize _when_ to make use of
library functions, and _where_ in this manual you can find more specific
information about them.


File: libc.info,  Node: Standards and Portability,  Next: Using the Library,  Prev: Getting Started,  Up: Introduction

1.2 Standards and Portability
=============================

This section discusses the various standards and other sources that the
GNU C Library is based upon.  These sources include the ISO C and POSIX
standards, and the System V and Berkeley Unix implementations.

   The primary focus of this manual is to tell you how to make effective
use of the GNU C Library facilities.  But if you are concerned about
making your programs compatible with these standards, or portable to
operating systems other than GNU, this can affect how you use the
library.  This section gives you an overview of these standards, so that
you will know what they are when they are mentioned in other parts of
the manual.

   *Note Library Summary::, for an alphabetical list of the functions
and other symbols provided by the library.  This list also states which
standards each function or symbol comes from.

* Menu:

* ISO C::                       The international standard for the C
                                 programming language.
* POSIX::                       The ISO/IEC 9945 (aka IEEE 1003) standards
                                 for operating systems.
* Berkeley Unix::               BSD and SunOS.
* SVID::                        The System V Interface Description.
* XPG::                         The X/Open Portability Guide.


File: libc.info,  Node: ISO C,  Next: POSIX,  Up: Standards and Portability

1.2.1 ISO C
-----------

The GNU C Library is compatible with the C standard adopted by the
American National Standards Institute (ANSI): ‘American National
Standard X3.159-1989—“ANSI C”’ and later by the International
Standardization Organization (ISO): ‘ISO/IEC 9899:1990, “Programming
languages—C”’.  We here refer to the standard as ISO C since this is the
more general standard in respect of ratification.  The header files and
library facilities that make up the GNU C Library are a superset of
those specified by the ISO C standard.

   If you are concerned about strict adherence to the ISO C standard,
you should use the ‘-ansi’ option when you compile your programs with
the GNU C compiler.  This tells the compiler to define _only_ ISO
standard features from the library header files, unless you explicitly
ask for additional features.  *Note Feature Test Macros::, for
information on how to do this.

   Being able to restrict the library to include only ISO C features is
important because ISO C puts limitations on what names can be defined by
the library implementation, and the GNU extensions don’t fit these
limitations.  *Note Reserved Names::, for more information about these
restrictions.

   This manual does not attempt to give you complete details on the
differences between ISO C and older dialects.  It gives advice on how to
write programs to work portably under multiple C dialects, but does not
aim for completeness.


File: libc.info,  Node: POSIX,  Next: Berkeley Unix,  Prev: ISO C,  Up: Standards and Portability

1.2.2 POSIX (The Portable Operating System Interface)
-----------------------------------------------------

The GNU C Library is also compatible with the ISO “POSIX” family of
standards, known more formally as the “Portable Operating System
Interface for Computer Environments” (ISO/IEC 9945).  They were also
published as ANSI/IEEE Std 1003.  POSIX is derived mostly from various
versions of the Unix operating system.

   The library facilities specified by the POSIX standards are a
superset of those required by ISO C; POSIX specifies additional features
for ISO C functions, as well as specifying new additional functions.  In
general, the additional requirements and functionality defined by the
POSIX standards are aimed at providing lower-level support for a
particular kind of operating system environment, rather than general
programming language support which can run in many diverse operating
system environments.

   The GNU C Library implements all of the functions specified in
‘ISO/IEC 9945-1:1996, the POSIX System Application Program Interface’,
commonly referred to as POSIX.1.  The primary extensions to the ISO C
facilities specified by this standard include file system interface
primitives (*note File System Interface::), device-specific terminal
control functions (*note Low-Level Terminal Interface::), and process
control functions (*note Processes::).

   Some facilities from ‘ISO/IEC 9945-2:1993, the POSIX Shell and
Utilities standard’ (POSIX.2) are also implemented in the GNU C Library.
These include utilities for dealing with regular expressions and other
pattern matching facilities (*note Pattern Matching::).

* Menu:

* POSIX Safety Concepts::       Safety concepts from POSIX.
* Unsafe Features::             Features that make functions unsafe.
* Conditionally Safe Features:: Features that make functions unsafe
                                 in the absence of workarounds.
* Other Safety Remarks::        Additional safety features and remarks.


File: libc.info,  Node: POSIX Safety Concepts,  Next: Unsafe Features,  Up: POSIX

1.2.2.1 POSIX Safety Concepts
.............................

This manual documents various safety properties of GNU C Library
functions, in lines that follow their prototypes and look like:

Preliminary: | MT-Safe | AS-Safe | AC-Safe |

   The properties are assessed according to the criteria set forth in
the POSIX standard for such safety contexts as Thread-, Async-Signal-
and Async-Cancel- -Safety.  Intuitive definitions of these properties,
attempting to capture the meaning of the standard definitions, follow.

   • ‘MT-Safe’ or Thread-Safe functions are safe to call in the presence
     of other threads.  MT, in MT-Safe, stands for Multi Thread.

     Being MT-Safe does not imply a function is atomic, nor that it uses
     any of the memory synchronization mechanisms POSIX exposes to
     users.  It is even possible that calling MT-Safe functions in
     sequence does not yield an MT-Safe combination.  For example,
     having a thread call two MT-Safe functions one right after the
     other does not guarantee behavior equivalent to atomic execution of
     a combination of both functions, since concurrent calls in other
     threads may interfere in a destructive way.

     Whole-program optimizations that could inline functions across
     library interfaces may expose unsafe reordering, and so performing
     inlining across the GNU C Library interface is not recommended.
     The documented MT-Safety status is not guaranteed under
     whole-program optimization.  However, functions defined in
     user-visible headers are designed to be safe for inlining.

   • ‘AS-Safe’ or Async-Signal-Safe functions are safe to call from
     asynchronous signal handlers.  AS, in AS-Safe, stands for
     Asynchronous Signal.

     Many functions that are AS-Safe may set ‘errno’, or modify the
     floating-point environment, because their doing so does not make
     them unsuitable for use in signal handlers.  However, programs
     could misbehave should asynchronous signal handlers modify this
     thread-local state, and the signal handling machinery cannot be
     counted on to preserve it.  Therefore, signal handlers that call
     functions that may set ‘errno’ or modify the floating-point
     environment _must_ save their original values, and restore them
     before returning.

   • ‘AC-Safe’ or Async-Cancel-Safe functions are safe to call when
     asynchronous cancellation is enabled.  AC in AC-Safe stands for
     Asynchronous Cancellation.

     The POSIX standard defines only three functions to be AC-Safe,
     namely ‘pthread_cancel’, ‘pthread_setcancelstate’, and
     ‘pthread_setcanceltype’.  At present the GNU C Library provides no
     guarantees beyond these three functions, but does document which
     functions are presently AC-Safe.  This documentation is provided
     for use by the GNU C Library developers.

     Just like signal handlers, cancellation cleanup routines must
     configure the floating point environment they require.  The
     routines cannot assume a floating point environment, particularly
     when asynchronous cancellation is enabled.  If the configuration of
     the floating point environment cannot be performed atomically then
     it is also possible that the environment encountered is internally
     inconsistent.

   • ‘MT-Unsafe’, ‘AS-Unsafe’, ‘AC-Unsafe’ functions are not safe to
     call within the safety contexts described above.  Calling them
     within such contexts invokes undefined behavior.

     Functions not explicitly documented as safe in a safety context
     should be regarded as Unsafe.

   • ‘Preliminary’ safety properties are documented, indicating these
     properties may _not_ be counted on in future releases of the GNU C
     Library.

     Such preliminary properties are the result of an assessment of the
     properties of our current implementation, rather than of what is
     mandated and permitted by current and future standards.

     Although we strive to abide by the standards, in some cases our
     implementation is safe even when the standard does not demand
     safety, and in other cases our implementation does not meet the
     standard safety requirements.  The latter are most likely bugs; the
     former, when marked as ‘Preliminary’, should not be counted on:
     future standards may require changes that are not compatible with
     the additional safety properties afforded by the current
     implementation.

     Furthermore, the POSIX standard does not offer a detailed
     definition of safety.  We assume that, by “safe to call”, POSIX
     means that, as long as the program does not invoke undefined
     behavior, the “safe to call” function behaves as specified, and
     does not cause other functions to deviate from their specified
     behavior.  We have chosen to use its loose definitions of safety,
     not because they are the best definitions to use, but because
     choosing them harmonizes this manual with POSIX.

     Please keep in mind that these are preliminary definitions and
     annotations, and certain aspects of the definitions are still under
     discussion and might be subject to clarification or change.

     Over time, we envision evolving the preliminary safety notes into
     stable commitments, as stable as those of our interfaces.  As we
     do, we will remove the ‘Preliminary’ keyword from safety notes.  As
     long as the keyword remains, however, they are not to be regarded
     as a promise of future behavior.

   Other keywords that appear in safety notes are defined in subsequent
sections.


File: libc.info,  Node: Unsafe Features,  Next: Conditionally Safe Features,  Prev: POSIX Safety Concepts,  Up: POSIX

1.2.2.2 Unsafe Features
.......................

Functions that are unsafe to call in certain contexts are annotated with
keywords that document their features that make them unsafe to call.
AS-Unsafe features in this section indicate the functions are never safe
to call when asynchronous signals are enabled.  AC-Unsafe features
indicate they are never safe to call when asynchronous cancellation is
enabled.  There are no MT-Unsafe marks in this section.

   • ‘lock’

     Functions marked with ‘lock’ as an AS-Unsafe feature may be
     interrupted by a signal while holding a non-recursive lock.  If the
     signal handler calls another such function that takes the same
     lock, the result is a deadlock.

     Functions annotated with ‘lock’ as an AC-Unsafe feature may, if
     cancelled asynchronously, fail to release a lock that would have
     been released if their execution had not been interrupted by
     asynchronous thread cancellation.  Once a lock is left taken,
     attempts to take that lock will block indefinitely.

   • ‘corrupt’

     Functions marked with ‘corrupt’ as an AS-Unsafe feature may corrupt
     data structures and misbehave when they interrupt, or are
     interrupted by, another such function.  Unlike functions marked
     with ‘lock’, these take recursive locks to avoid MT-Safety
     problems, but this is not enough to stop a signal handler from
     observing a partially-updated data structure.  Further corruption
     may arise from the interrupted function’s failure to notice updates
     made by signal handlers.

     Functions marked with ‘corrupt’ as an AC-Unsafe feature may leave
     data structures in a corrupt, partially updated state.  Subsequent
     uses of the data structure may misbehave.

   • ‘heap’

     Functions marked with ‘heap’ may call heap memory management
     functions from the ‘malloc’/‘free’ family of functions and are only
     as safe as those functions.  This note is thus equivalent to:

     | AS-Unsafe lock | AC-Unsafe lock fd mem |

   • ‘dlopen’

     Functions marked with ‘dlopen’ use the dynamic loader to load
     shared libraries into the current execution image.  This involves
     opening files, mapping them into memory, allocating additional
     memory, resolving symbols, applying relocations and more, all of
     this while holding internal dynamic loader locks.

     The locks are enough for these functions to be AS- and AC-Unsafe,
     but other issues may arise.  At present this is a placeholder for
     all potential safety issues raised by ‘dlopen’.

   • ‘plugin’

     Functions annotated with ‘plugin’ may run code from plugins that
     may be external to the GNU C Library.  Such plugin functions are
     assumed to be MT-Safe, AS-Unsafe and AC-Unsafe.  Examples of such
     plugins are stack unwinding libraries, name service switch (NSS)
     and character set conversion (iconv) back-ends.

     Although the plugins mentioned as examples are all brought in by
     means of dlopen, the ‘plugin’ keyword does not imply any direct
     involvement of the dynamic loader or the ‘libdl’ interfaces, those
     are covered by ‘dlopen’.  For example, if one function loads a
     module and finds the addresses of some of its functions, while
     another just calls those already-resolved functions, the former
     will be marked with ‘dlopen’, whereas the latter will get the
     ‘plugin’.  When a single function takes all of these actions, then
     it gets both marks.

   • ‘i18n’

     Functions marked with ‘i18n’ may call internationalization
     functions of the ‘gettext’ family and will be only as safe as those
     functions.  This note is thus equivalent to:

     | MT-Safe env | AS-Unsafe corrupt heap dlopen | AC-Unsafe corrupt |

   • ‘timer’

     Functions marked with ‘timer’ use the ‘alarm’ function or similar
     to set a time-out for a system call or a long-running operation.
     In a multi-threaded program, there is a risk that the time-out
     signal will be delivered to a different thread, thus failing to
     interrupt the intended thread.  Besides being MT-Unsafe, such
     functions are always AS-Unsafe, because calling them in signal
     handlers may interfere with timers set in the interrupted code, and
     AC-Unsafe, because there is no safe way to guarantee an earlier
     timer will be reset in case of asynchronous cancellation.


File: libc.info,  Node: Conditionally Safe Features,  Next: Other Safety Remarks,  Prev: Unsafe Features,  Up: POSIX

1.2.2.3 Conditionally Safe Features
...................................

For some features that make functions unsafe to call in certain
contexts, there are known ways to avoid the safety problem other than
refraining from calling the function altogether.  The keywords that
follow refer to such features, and each of their definitions indicate
how the whole program needs to be constrained in order to remove the
safety problem indicated by the keyword.  Only when all the reasons that
make a function unsafe are observed and addressed, by applying the
documented constraints, does the function become safe to call in a
context.

   • ‘init’

     Functions marked with ‘init’ as an MT-Unsafe feature perform
     MT-Unsafe initialization when they are first called.

     Calling such a function at least once in single-threaded mode
     removes this specific cause for the function to be regarded as
     MT-Unsafe.  If no other cause for that remains, the function can
     then be safely called after other threads are started.

     Functions marked with ‘init’ as an AS- or AC-Unsafe feature use the
     internal ‘libc_once’ machinery or similar to initialize internal
     data structures.

     If a signal handler interrupts such an initializer, and calls any
     function that also performs ‘libc_once’ initialization, it will
     deadlock if the thread library has been loaded.

     Furthermore, if an initializer is partially complete before it is
     canceled or interrupted by a signal whose handler requires the same
     initialization, some or all of the initialization may be performed
     more than once, leaking resources or even resulting in corrupt
     internal data.

     Applications that need to call functions marked with ‘init’ as an
     AS- or AC-Unsafe feature should ensure the initialization is
     performed before configuring signal handlers or enabling
     cancellation, so that the AS- and AC-Safety issues related with
     ‘libc_once’ do not arise.

   • ‘race’

     Functions annotated with ‘race’ as an MT-Safety issue operate on
     objects in ways that may cause data races or similar forms of
     destructive interference out of concurrent execution.  In some
     cases, the objects are passed to the functions by users; in others,
     they are used by the functions to return values to users; in
     others, they are not even exposed to users.

     We consider access to objects passed as (indirect) arguments to
     functions to be data race free.  The assurance of data race free
     objects is the caller’s responsibility.  We will not mark a
     function as MT-Unsafe or AS-Unsafe if it misbehaves when users fail
     to take the measures required by POSIX to avoid data races when
     dealing with such objects.  As a general rule, if a function is
     documented as reading from an object passed (by reference) to it,
     or modifying it, users ought to use memory synchronization
     primitives to avoid data races just as they would should they
     perform the accesses themselves rather than by calling the library
     function.  ‘FILE’ streams are the exception to the general rule, in
     that POSIX mandates the library to guard against data races in many
     functions that manipulate objects of this specific opaque type.  We
     regard this as a convenience provided to users, rather than as a
     general requirement whose expectations should extend to other
     types.

     In order to remind users that guarding certain arguments is their
     responsibility, we will annotate functions that take objects of
     certain types as arguments.  We draw the line for objects passed by
     users as follows: objects whose types are exposed to users, and
     that users are expected to access directly, such as memory buffers,
     strings, and various user-visible ‘struct’ types, do _not_ give
     reason for functions to be annotated with ‘race’.  It would be
     noisy and redundant with the general requirement, and not many
     would be surprised by the library’s lack of internal guards when
     accessing objects that can be accessed directly by users.

     As for objects that are opaque or opaque-like, in that they are to
     be manipulated only by passing them to library functions (e.g.,
     ‘FILE’, ‘DIR’, ‘obstack’, ‘iconv_t’), there might be additional
     expectations as to internal coordination of access by the library.
     We will annotate, with ‘race’ followed by a colon and the argument
     name, functions that take such objects but that do not take care of
     synchronizing access to them by default.  For example, ‘FILE’
     stream ‘unlocked’ functions will be annotated, but those that
     perform implicit locking on ‘FILE’ streams by default will not,
     even though the implicit locking may be disabled on a per-stream
     basis.

     In either case, we will not regard as MT-Unsafe functions that may
     access user-supplied objects in unsafe ways should users fail to
     ensure the accesses are well defined.  The notion prevails that
     users are expected to safeguard against data races any
     user-supplied objects that the library accesses on their behalf.

     This user responsibility does not apply, however, to objects
     controlled by the library itself, such as internal objects and
     static buffers used to return values from certain calls.  When the
     library doesn’t guard them against concurrent uses, these cases are
     regarded as MT-Unsafe and AS-Unsafe (although the ‘race’ mark under
     AS-Unsafe will be omitted as redundant with the one under
     MT-Unsafe).  As in the case of user-exposed objects, the mark may
     be followed by a colon and an identifier.  The identifier groups
     all functions that operate on a certain unguarded object; users may
     avoid the MT-Safety issues related with unguarded concurrent access
     to such internal objects by creating a non-recursive mutex related
     with the identifier, and always holding the mutex when calling any
     function marked as racy on that identifier, as they would have to
     should the identifier be an object under user control.  The
     non-recursive mutex avoids the MT-Safety issue, but it trades one
     AS-Safety issue for another, so use in asynchronous signals remains
     undefined.

     When the identifier relates to a static buffer used to hold return
     values, the mutex must be held for as long as the buffer remains in
     use by the caller.  Many functions that return pointers to static
     buffers offer reentrant variants that store return values in
     caller-supplied buffers instead.  In some cases, such as ‘tmpname’,
     the variant is chosen not by calling an alternate entry point, but
     by passing a non-‘NULL’ pointer to the buffer in which the returned
     values are to be stored.  These variants are generally preferable
     in multi-threaded programs, although some of them are not MT-Safe
     because of other internal buffers, also documented with ‘race’
     notes.

   • ‘const’

     Functions marked with ‘const’ as an MT-Safety issue non-atomically
     modify internal objects that are better regarded as constant,
     because a substantial portion of the GNU C Library accesses them
     without synchronization.  Unlike ‘race’, that causes both readers
     and writers of internal objects to be regarded as MT-Unsafe and
     AS-Unsafe, this mark is applied to writers only.  Writers remain
     equally MT- and AS-Unsafe to call, but the then-mandatory constness
     of objects they modify enables readers to be regarded as MT-Safe
     and AS-Safe (as long as no other reasons for them to be unsafe
     remain), since the lack of synchronization is not a problem when
     the objects are effectively constant.

     The identifier that follows the ‘const’ mark will appear by itself
     as a safety note in readers.  Programs that wish to work around
     this safety issue, so as to call writers, may use a non-recursve
     ‘rwlock’ associated with the identifier, and guard _all_ calls to
     functions marked with ‘const’ followed by the identifier with a
     write lock, and _all_ calls to functions marked with the identifier
     by itself with a read lock.  The non-recursive locking removes the
     MT-Safety problem, but it trades one AS-Safety problem for another,
     so use in asynchronous signals remains undefined.

   • ‘sig’

     Functions marked with ‘sig’ as a MT-Safety issue (that implies an
     identical AS-Safety issue, omitted for brevity) may temporarily
     install a signal handler for internal purposes, which may interfere
     with other uses of the signal, identified after a colon.

     This safety problem can be worked around by ensuring that no other
     uses of the signal will take place for the duration of the call.
     Holding a non-recursive mutex while calling all functions that use
     the same temporary signal; blocking that signal before the call and
     resetting its handler afterwards is recommended.

     There is no safe way to guarantee the original signal handler is
     restored in case of asynchronous cancellation, therefore so-marked
     functions are also AC-Unsafe.

     Besides the measures recommended to work around the MT- and
     AS-Safety problem, in order to avert the cancellation problem,
     disabling asynchronous cancellation _and_ installing a cleanup
     handler to restore the signal to the desired state and to release
     the mutex are recommended.

   • ‘term’

     Functions marked with ‘term’ as an MT-Safety issue may change the
     terminal settings in the recommended way, namely: call ‘tcgetattr’,
     modify some flags, and then call ‘tcsetattr’; this creates a window
     in which changes made by other threads are lost.  Thus, functions
     marked with ‘term’ are MT-Unsafe.  The same window enables changes
     made by asynchronous signals to be lost.  These functions are also
     AS-Unsafe, but the corresponding mark is omitted as redundant.

     It is thus advisable for applications using the terminal to avoid
     concurrent and reentrant interactions with it, by not using it in
     signal handlers or blocking signals that might use it, and holding
     a lock while calling these functions and interacting with the
     terminal.  This lock should also be used for mutual exclusion with
     functions marked with ‘race:tcattr(fd)’, where FD is a file
     descriptor for the controlling terminal.  The caller may use a
     single mutex for simplicity, or use one mutex per terminal, even if
     referenced by different file descriptors.

     Functions marked with ‘term’ as an AC-Safety issue are supposed to
     restore terminal settings to their original state, after
     temporarily changing them, but they may fail to do so if cancelled.

     Besides the measures recommended to work around the MT- and
     AS-Safety problem, in order to avert the cancellation problem,
     disabling asynchronous cancellation _and_ installing a cleanup
     handler to restore the terminal settings to the original state and
     to release the mutex are recommended.


File: libc.info,  Node: Other Safety Remarks,  Prev: Conditionally Safe Features,  Up: POSIX

******* Other Safety Remarks
............................

Additional keywords may be attached to functions, indicating features
that do not make a function unsafe to call, but that may need to be
taken into account in certain classes of programs:

   • ‘locale’

     Functions annotated with ‘locale’ as an MT-Safety issue read from
     the locale object without any form of synchronization.  Functions
     annotated with ‘locale’ called concurrently with locale changes may
     behave in ways that do not correspond to any of the locales active
     during their execution, but an unpredictable mix thereof.

     We do not mark these functions as MT- or AS-Unsafe, however,
     because functions that modify the locale object are marked with
     ‘const:locale’ and regarded as unsafe.  Being unsafe, the latter
     are not to be called when multiple threads are running or
     asynchronous signals are enabled, and so the locale can be
     considered effectively constant in these contexts, which makes the
     former safe.

   • ‘env’

     Functions marked with ‘env’ as an MT-Safety issue access the
     environment with ‘getenv’ or similar, without any guards to ensure
     safety in the presence of concurrent modifications.

     We do not mark these functions as MT- or AS-Unsafe, however,
     because functions that modify the environment are all marked with
     ‘const:env’ and regarded as unsafe.  Being unsafe, the latter are
     not to be called when multiple threads are running or asynchronous
     signals are enabled, and so the environment can be considered
     effectively constant in these contexts, which makes the former
     safe.

   • ‘hostid’

     The function marked with ‘hostid’ as an MT-Safety issue reads from
     the system-wide data structures that hold the “host ID” of the
     machine.  These data structures cannot generally be modified
     atomically.  Since it is expected that the “host ID” will not
     normally change, the function that reads from it (‘gethostid’) is
     regarded as safe, whereas the function that modifies it
     (‘sethostid’) is marked with ‘const:hostid’, indicating it may
     require special care if it is to be called.  In this specific case,
     the special care amounts to system-wide (not merely intra-process)
     coordination.

   • ‘sigintr’

     Functions marked with ‘sigintr’ as an MT-Safety issue access the
     ‘_sigintr’ internal data structure without any guards to ensure
     safety in the presence of concurrent modifications.

     We do not mark these functions as MT- or AS-Unsafe, however,
     because functions that modify the this data structure are all
     marked with ‘const:sigintr’ and regarded as unsafe.  Being unsafe,
     the latter are not to be called when multiple threads are running
     or asynchronous signals are enabled, and so the data structure can
     be considered effectively constant in these contexts, which makes
     the former safe.

   • ‘fd’

     Functions annotated with ‘fd’ as an AC-Safety issue may leak file
     descriptors if asynchronous thread cancellation interrupts their
     execution.

     Functions that allocate or deallocate file descriptors will
     generally be marked as such.  Even if they attempted to protect the
     file descriptor allocation and deallocation with cleanup regions,
     allocating a new descriptor and storing its number where the
     cleanup region could release it cannot be performed as a single
     atomic operation.  Similarly, releasing the descriptor and taking
     it out of the data structure normally responsible for releasing it
     cannot be performed atomically.  There will always be a window in
     which the descriptor cannot be released because it was not stored
     in the cleanup handler argument yet, or it was already taken out
     before releasing it.  It cannot be taken out after release: an open
     descriptor could mean either that the descriptor still has to be
     closed, or that it already did so but the descriptor was
     reallocated by another thread or signal handler.

     Such leaks could be internally avoided, with some performance
     penalty, by temporarily disabling asynchronous thread cancellation.
     However, since callers of allocation or deallocation functions
     would have to do this themselves, to avoid the same sort of leak in
     their own layer, it makes more sense for the library to assume they
     are taking care of it than to impose a performance penalty that is
     redundant when the problem is solved in upper layers, and
     insufficient when it is not.

     This remark by itself does not cause a function to be regarded as
     AC-Unsafe.  However, cumulative effects of such leaks may pose a
     problem for some programs.  If this is the case, suspending
     asynchronous cancellation for the duration of calls to such
     functions is recommended.

   • ‘mem’

     Functions annotated with ‘mem’ as an AC-Safety issue may leak
     memory if asynchronous thread cancellation interrupts their
     execution.

     The problem is similar to that of file descriptors: there is no
     atomic interface to allocate memory and store its address in the
     argument to a cleanup handler, or to release it and remove its
     address from that argument, without at least temporarily disabling
     asynchronous cancellation, which these functions do not do.

     This remark does not by itself cause a function to be regarded as
     generally AC-Unsafe.  However, cumulative effects of such leaks may
     be severe enough for some programs that disabling asynchronous
     cancellation for the duration of calls to such functions may be
     required.

   • ‘cwd’

     Functions marked with ‘cwd’ as an MT-Safety issue may temporarily
     change the current working directory during their execution, which
     may cause relative pathnames to be resolved in unexpected ways in
     other threads or within asynchronous signal or cancellation
     handlers.

     This is not enough of a reason to mark so-marked functions as MT-
     or AS-Unsafe, but when this behavior is optional (e.g., ‘nftw’ with
     ‘FTW_CHDIR’), avoiding the option may be a good alternative to
     using full pathnames or file descriptor-relative (e.g.  ‘openat’)
     system calls.

   • ‘!posix’

     This remark, as an MT-, AS- or AC-Safety note to a function,
     indicates the safety status of the function is known to differ from
     the specified status in the POSIX standard.  For example, POSIX
     does not require a function to be Safe, but our implementation is,
     or vice-versa.

     For the time being, the absence of this remark does not imply the
     safety properties we documented are identical to those mandated by
     POSIX for the corresponding functions.

   • ‘:identifier’

     Annotations may sometimes be followed by identifiers, intended to
     group several functions that e.g.  access the data structures in an
     unsafe way, as in ‘race’ and ‘const’, or to provide more specific
     information, such as naming a signal in a function marked with
     ‘sig’.  It is envisioned that it may be applied to ‘lock’ and
     ‘corrupt’ as well in the future.

     In most cases, the identifier will name a set of functions, but it
     may name global objects or function arguments, or identifiable
     properties or logical components associated with them, with a
     notation such as e.g.  ‘:buf(arg)’ to denote a buffer associated
     with the argument ARG, or ‘:tcattr(fd)’ to denote the terminal
     attributes of a file descriptor FD.

     The most common use for identifiers is to provide logical groups of
     functions and arguments that need to be protected by the same
     synchronization primitive in order to ensure safe operation in a
     given context.

   • ‘/condition’

     Some safety annotations may be conditional, in that they only apply
     if a boolean expression involving arguments, global variables or
     even the underlying kernel evaluates to true.  Such conditions as
     ‘/hurd’ or ‘/!linux!bsd’ indicate the preceding marker only applies
     when the underlying kernel is the HURD, or when it is neither Linux
     nor a BSD kernel, respectively.  ‘/!ps’ and ‘/one_per_line’
     indicate the preceding marker only applies when argument PS is
     NULL, or global variable ONE_PER_LINE is nonzero.

     When all marks that render a function unsafe are adorned with such
     conditions, and none of the named conditions hold, then the
     function can be regarded as safe.


File: libc.info,  Node: Berkeley Unix,  Next: SVID,  Prev: POSIX,  Up: Standards and Portability

1.2.3 Berkeley Unix
-------------------

The GNU C Library defines facilities from some versions of Unix which
are not formally standardized, specifically from the 4.2 BSD, 4.3 BSD,
and 4.4 BSD Unix systems (also known as “Berkeley Unix”) and from
“SunOS” (a popular 4.2 BSD derivative that includes some Unix System V
functionality).  These systems support most of the ISO C and POSIX
facilities, and 4.4 BSD and newer releases of SunOS in fact support them
all.

   The BSD facilities include symbolic links (*note Symbolic Links::),
the ‘select’ function (*note Waiting for I/O::), the BSD signal
functions (*note BSD Signal Handling::), and sockets (*note Sockets::).


File: libc.info,  Node: SVID,  Next: XPG,  Prev: Berkeley Unix,  Up: Standards and Portability

1.2.4 SVID (The System V Interface Description)
-----------------------------------------------

The “System V Interface Description” (SVID) is a document describing the
AT&T Unix System V operating system.  It is to some extent a superset of
the POSIX standard (*note POSIX::).

   The GNU C Library defines most of the facilities required by the SVID
that are not also required by the ISO C or POSIX standards, for
compatibility with System V Unix and other Unix systems (such as SunOS)
which include these facilities.  However, many of the more obscure and
less generally useful facilities required by the SVID are not included.
(In fact, Unix System V itself does not provide them all.)

   The supported facilities from System V include the methods for
inter-process communication and shared memory, the ‘hsearch’ and
‘drand48’ families of functions, ‘fmtmsg’ and several of the
mathematical functions.


File: libc.info,  Node: XPG,  Prev: SVID,  Up: Standards and Portability

1.2.5 XPG (The X/Open Portability Guide)
----------------------------------------

The X/Open Portability Guide, published by the X/Open Company, Ltd., is
a more general standard than POSIX. X/Open owns the Unix copyright and
the XPG specifies the requirements for systems which are intended to be
a Unix system.

   The GNU C Library complies to the X/Open Portability Guide, Issue
4.2, with all extensions common to XSI (X/Open System Interface)
compliant systems and also all X/Open UNIX extensions.

   The additions on top of POSIX are mainly derived from functionality
available in System V and BSD systems.  Some of the really bad mistakes
in System V systems were corrected, though.  Since fulfilling the XPG
standard with the Unix extensions is a precondition for getting the Unix
brand chances are good that the functionality is available on commercial
systems.


File: libc.info,  Node: Using the Library,  Next: Roadmap to the Manual,  Prev: Standards and Portability,  Up: Introduction

1.3 Using the Library
=====================

This section describes some of the practical issues involved in using
the GNU C Library.

* Menu:

* Header Files::                How to include the header files in your
                                 programs.
* Macro Definitions::           Some functions in the library may really
                                 be implemented as macros.
* Reserved Names::              The C standard reserves some names for
                                 the library, and some for users.
* Feature Test Macros::         How to control what names are defined.


File: libc.info,  Node: Header Files,  Next: Macro Definitions,  Up: Using the Library

1.3.1 Header Files
------------------

Libraries for use by C programs really consist of two parts: “header
files” that define types and macros and declare variables and functions;
and the actual library or “archive” that contains the definitions of the
variables and functions.

   (Recall that in C, a “declaration” merely provides information that a
function or variable exists and gives its type.  For a function
declaration, information about the types of its arguments might be
provided as well.  The purpose of declarations is to allow the compiler
to correctly process references to the declared variables and functions.
A “definition”, on the other hand, actually allocates storage for a
variable or says what a function does.)

   In order to use the facilities in the GNU C Library, you should be
sure that your program source files include the appropriate header
files.  This is so that the compiler has declarations of these
facilities available and can correctly process references to them.  Once
your program has been compiled, the linker resolves these references to
the actual definitions provided in the archive file.

   Header files are included into a program source file by the
‘#include’ preprocessor directive.  The C language supports two forms of
this directive; the first,

     #include "HEADER"

is typically used to include a header file HEADER that you write
yourself; this would contain definitions and declarations describing the
interfaces between the different parts of your particular application.
By contrast,

     #include <file.h>

is typically used to include a header file ‘file.h’ that contains
definitions and declarations for a standard library.  This file would
normally be installed in a standard place by your system administrator.
You should use this second form for the C library header files.

   Typically, ‘#include’ directives are placed at the top of the C
source file, before any other code.  If you begin your source files with
some comments explaining what the code in the file does (a good idea),
put the ‘#include’ directives immediately afterwards, following the
feature test macro definition (*note Feature Test Macros::).

   For more information about the use of header files and ‘#include’
directives, *note (cpp.info)Header Files::.

   The GNU C Library provides several header files, each of which
contains the type and macro definitions and variable and function
declarations for a group of related facilities.  This means that your
programs may need to include several header files, depending on exactly
which facilities you are using.

   Some library header files include other library header files
automatically.  However, as a matter of programming style, you should
not rely on this; it is better to explicitly include all the header
files required for the library facilities you are using.  The GNU C
Library header files have been written in such a way that it doesn’t
matter if a header file is accidentally included more than once;
including a header file a second time has no effect.  Likewise, if your
program needs to include multiple header files, the order in which they
are included doesn’t matter.

   *Compatibility Note:* Inclusion of standard header files in any order
and any number of times works in any ISO C implementation.  However,
this has traditionally not been the case in many older C
implementations.

   Strictly speaking, you don’t _have to_ include a header file to use a
function it declares; you could declare the function explicitly
yourself, according to the specifications in this manual.  But it is
usually better to include the header file because it may define types
and macros that are not otherwise available and because it may define
more efficient macro replacements for some functions.  It is also a sure
way to have the correct declaration.


File: libc.info,  Node: Macro Definitions,  Next: Reserved Names,  Prev: Header Files,  Up: Using the Library

1.3.2 Macro Definitions of Functions
------------------------------------

If we describe something as a function in this manual, it may have a
macro definition as well.  This normally has no effect on how your
program runs—the macro definition does the same thing as the function
would.  In particular, macro equivalents for library functions evaluate
arguments exactly once, in the same way that a function call would.  The
main reason for these macro definitions is that sometimes they can
produce an inline expansion that is considerably faster than an actual
function call.

   Taking the address of a library function works even if it is also
defined as a macro.  This is because, in this context, the name of the
function isn’t followed by the left parenthesis that is syntactically
necessary to recognize a macro call.

   You might occasionally want to avoid using the macro definition of a
function—perhaps to make your program easier to debug.  There are two
ways you can do this:

   • You can avoid a macro definition in a specific use by enclosing the
     name of the function in parentheses.  This works because the name
     of the function doesn’t appear in a syntactic context where it is
     recognizable as a macro call.

   • You can suppress any macro definition for a whole source file by
     using the ‘#undef’ preprocessor directive, unless otherwise stated
     explicitly in the description of that facility.

   For example, suppose the header file ‘stdlib.h’ declares a function
named ‘abs’ with

     extern int abs (int);

and also provides a macro definition for ‘abs’.  Then, in:

     #include <stdlib.h>
     int f (int *i) { return abs (++*i); }

the reference to ‘abs’ might refer to either a macro or a function.  On
the other hand, in each of the following examples the reference is to a
function and not a macro.

     #include <stdlib.h>
     int g (int *i) { return (abs) (++*i); }

     #undef abs
     int h (int *i) { return abs (++*i); }

   Since macro definitions that double for a function behave in exactly
the same way as the actual function version, there is usually no need
for any of these methods.  In fact, removing macro definitions usually
just makes your program slower.


File: libc.info,  Node: Reserved Names,  Next: Feature Test Macros,  Prev: Macro Definitions,  Up: Using the Library

1.3.3 Reserved Names
--------------------

The names of all library types, macros, variables and functions that
come from the ISO C standard are reserved unconditionally; your program
*may not* redefine these names.  All other library names are reserved if
your program explicitly includes the header file that defines or
declares them.  There are several reasons for these restrictions:

   • Other people reading your code could get very confused if you were
     using a function named ‘exit’ to do something completely different
     from what the standard ‘exit’ function does, for example.
     Preventing this situation helps to make your programs easier to
     understand and contributes to modularity and maintainability.

   • It avoids the possibility of a user accidentally redefining a
     library function that is called by other library functions.  If
     redefinition were allowed, those other functions would not work
     properly.

   • It allows the compiler to do whatever special optimizations it
     pleases on calls to these functions, without the possibility that
     they may have been redefined by the user.  Some library facilities,
     such as those for dealing with variadic arguments (*note Variadic
     Functions::) and non-local exits (*note Non-Local Exits::),
     actually require a considerable amount of cooperation on the part
     of the C compiler, and with respect to the implementation, it might
     be easier for the compiler to treat these as built-in parts of the
     language.

   In addition to the names documented in this manual, reserved names
include all external identifiers (global functions and variables) that
begin with an underscore (‘_’) and all identifiers regardless of use
that begin with either two underscores or an underscore followed by a
capital letter are reserved names.  This is so that the library and
header files can define functions, variables, and macros for internal
purposes without risk of conflict with names in user programs.

   Some additional classes of identifier names are reserved for future
extensions to the C language or the POSIX.1 environment.  While using
these names for your own purposes right now might not cause a problem,
they do raise the possibility of conflict with future versions of the C
or POSIX standards, so you should avoid these names.

   • Names beginning with a capital ‘E’ followed a digit or uppercase
     letter may be used for additional error code names.  *Note Error
     Reporting::.

   • Names that begin with either ‘is’ or ‘to’ followed by a lowercase
     letter may be used for additional character testing and conversion
     functions.  *Note Character Handling::.

   • Names that begin with ‘LC_’ followed by an uppercase letter may be
     used for additional macros specifying locale attributes.  *Note
     Locales::.

   • Names of all existing mathematics functions (*note Mathematics::)
     suffixed with ‘f’ or ‘l’ are reserved for corresponding functions
     that operate on ‘float’ and ‘long double’ arguments, respectively.

   • Names that begin with ‘SIG’ followed by an uppercase letter are
     reserved for additional signal names.  *Note Standard Signals::.

   • Names that begin with ‘SIG_’ followed by an uppercase letter are
     reserved for additional signal actions.  *Note Basic Signal
     Handling::.

   • Names beginning with ‘str’, ‘mem’, or ‘wcs’ followed by a lowercase
     letter are reserved for additional string and array functions.
     *Note String and Array Utilities::.

   • Names that end with ‘_t’ are reserved for additional type names.

   In addition, some individual header files reserve names beyond those
that they actually define.  You only need to worry about these
restrictions if your program includes that particular header file.

   • The header file ‘dirent.h’ reserves names prefixed with ‘d_’.

   • The header file ‘fcntl.h’ reserves names prefixed with ‘l_’, ‘F_’,
     ‘O_’, and ‘S_’.

   • The header file ‘grp.h’ reserves names prefixed with ‘gr_’.

   • The header file ‘limits.h’ reserves names suffixed with ‘_MAX’.

   • The header file ‘pwd.h’ reserves names prefixed with ‘pw_’.

   • The header file ‘signal.h’ reserves names prefixed with ‘sa_’ and
     ‘SA_’.

   • The header file ‘sys/stat.h’ reserves names prefixed with ‘st_’ and
     ‘S_’.

   • The header file ‘sys/times.h’ reserves names prefixed with ‘tms_’.

   • The header file ‘termios.h’ reserves names prefixed with ‘c_’, ‘V’,
     ‘I’, ‘O’, and ‘TC’; and names prefixed with ‘B’ followed by a
     digit.


File: libc.info,  Node: Feature Test Macros,  Prev: Reserved Names,  Up: Using the Library

1.3.4 Feature Test Macros
-------------------------

The exact set of features available when you compile a source file is
controlled by which “feature test macros” you define.

   If you compile your programs using ‘gcc -ansi’, you get only the
ISO C library features, unless you explicitly request additional
features by defining one or more of the feature macros.  *Note GNU CC
Command Options: (gcc)Invoking GCC, for more information about GCC
options.

   You should define these macros by using ‘#define’ preprocessor
directives at the top of your source code files.  These directives
_must_ come before any ‘#include’ of a system header file.  It is best
to make them the very first thing in the file, preceded only by
comments.  You could also use the ‘-D’ option to GCC, but it’s better if
you make the source files indicate their own meaning in a self-contained
way.

   This system exists to allow the library to conform to multiple
standards.  Although the different standards are often described as
supersets of each other, they are usually incompatible because larger
standards require functions with names that smaller ones reserve to the
user program.  This is not mere pedantry — it has been a problem in
practice.  For instance, some non-GNU programs define functions named
‘getline’ that have nothing to do with this library’s ‘getline’.  They
would not be compilable if all features were enabled indiscriminately.

   This should not be used to verify that a program conforms to a
limited standard.  It is insufficient for this purpose, as it will not
protect you from including header files outside the standard, or relying
on semantics undefined within the standard.

 -- Macro: _POSIX_SOURCE

     If you define this macro, then the functionality from the POSIX.1
     standard (IEEE Standard 1003.1) is available, as well as all of the
     ISO C facilities.

     The state of ‘_POSIX_SOURCE’ is irrelevant if you define the macro
     ‘_POSIX_C_SOURCE’ to a positive integer.

 -- Macro: _POSIX_C_SOURCE

     Define this macro to a positive integer to control which POSIX
     functionality is made available.  The greater the value of this
     macro, the more functionality is made available.

     If you define this macro to a value greater than or equal to ‘1’,
     then the functionality from the 1990 edition of the POSIX.1
     standard (IEEE Standard 1003.1-1990) is made available.

     If you define this macro to a value greater than or equal to ‘2’,
     then the functionality from the 1992 edition of the POSIX.2
     standard (IEEE Standard 1003.2-1992) is made available.

     If you define this macro to a value greater than or equal to
     ‘199309L’, then the functionality from the 1993 edition of the
     POSIX.1b standard (IEEE Standard 1003.1b-1993) is made available.

     If you define this macro to a value greater than or equal to
     ‘199506L’, then the functionality from the 1995 edition of the
     POSIX.1c standard (IEEE Standard 1003.1c-1995) is made available.

     If you define this macro to a value greater than or equal to
     ‘200112L’, then the functionality from the 2001 edition of the
     POSIX standard (IEEE Standard 1003.1-2001) is made available.

     If you define this macro to a value greater than or equal to
     ‘200809L’, then the functionality from the 2008 edition of the
     POSIX standard (IEEE Standard 1003.1-2008) is made available.

     Greater values for ‘_POSIX_C_SOURCE’ will enable future extensions.
     The POSIX standards process will define these values as necessary,
     and the GNU C Library should support them some time after they
     become standardized.  The 1996 edition of POSIX.1 (ISO/IEC 9945-1:
     1996) states that if you define ‘_POSIX_C_SOURCE’ to a value
     greater than or equal to ‘199506L’, then the functionality from the
     1996 edition is made available.  In general, in the GNU C Library,
     bugfixes to the standards are included when specifying the base
     version; e.g., POSIX.1-2004 will always be included with a value of
     ‘200112L’.

 -- Macro: _XOPEN_SOURCE
 -- Macro: _XOPEN_SOURCE_EXTENDED

     If you define this macro, functionality described in the X/Open
     Portability Guide is included.  This is a superset of the POSIX.1
     and POSIX.2 functionality and in fact ‘_POSIX_SOURCE’ and
     ‘_POSIX_C_SOURCE’ are automatically defined.

     As the unification of all Unices, functionality only available in
     BSD and SVID is also included.

     If the macro ‘_XOPEN_SOURCE_EXTENDED’ is also defined, even more
     functionality is available.  The extra functions will make all
     functions available which are necessary for the X/Open Unix brand.

     If the macro ‘_XOPEN_SOURCE’ has the value 500 this includes all
     functionality described so far plus some new definitions from the
     Single Unix Specification, version 2.  The value 600 (corresponding
     to the sixth revision) includes definitions from SUSv3, and using
     700 (the seventh revision) includes definitions from SUSv4.

 -- Macro: _LARGEFILE_SOURCE

     If this macro is defined some extra functions are available which
     rectify a few shortcomings in all previous standards.
     Specifically, the functions ‘fseeko’ and ‘ftello’ are available.
     Without these functions the difference between the ISO C interface
     (‘fseek’, ‘ftell’) and the low-level POSIX interface (‘lseek’)
     would lead to problems.

     This macro was introduced as part of the Large File Support
     extension (LFS).

 -- Macro: _LARGEFILE64_SOURCE

     If you define this macro an additional set of functions is made
     available which enables 32 bit systems to use files of sizes beyond
     the usual limit of 2GB. This interface is not available if the
     system does not support files that large.  On systems where the
     natural file size limit is greater than 2GB (i.e., on 64 bit
     systems) the new functions are identical to the replaced functions.

     The new functionality is made available by a new set of types and
     functions which replace the existing ones.  The names of these new
     objects contain ‘64’ to indicate the intention, e.g., ‘off_t’ vs.
     ‘off64_t’ and ‘fseeko’ vs.  ‘fseeko64’.

     This macro was introduced as part of the Large File Support
     extension (LFS). It is a transition interface for the period when
     64 bit offsets are not generally used (see ‘_FILE_OFFSET_BITS’).

 -- Macro: _FILE_OFFSET_BITS

     This macro determines which file system interface shall be used,
     one replacing the other.  Whereas ‘_LARGEFILE64_SOURCE’ makes the
     64 bit interface available as an additional interface,
     ‘_FILE_OFFSET_BITS’ allows the 64 bit interface to replace the old
     interface.

     If ‘_FILE_OFFSET_BITS’ is undefined, or if it is defined to the
     value ‘32’, nothing changes.  The 32 bit interface is used and
     types like ‘off_t’ have a size of 32 bits on 32 bit systems.

     If the macro is defined to the value ‘64’, the large file interface
     replaces the old interface.  I.e., the functions are not made
     available under different names (as they are with
     ‘_LARGEFILE64_SOURCE’).  Instead the old function names now
     reference the new functions, e.g., a call to ‘fseeko’ now indeed
     calls ‘fseeko64’.

     This macro should only be selected if the system provides
     mechanisms for handling large files.  On 64 bit systems this macro
     has no effect since the ‘*64’ functions are identical to the normal
     functions.

     This macro was introduced as part of the Large File Support
     extension (LFS).

 -- Macro: _ISOC99_SOURCE

     If this macro is defined, features from ISO C99 are included.
     Since these features are included by default, this macro is mostly
     relevant when the compiler uses an earlier language version.

 -- Macro: _ISOC11_SOURCE

     If this macro is defined, ISO C11 extensions to ISO C99 are
     included.

 -- Macro: _ISOC2X_SOURCE

     If this macro is defined, ISO C2X extensions to ISO C11 are
     included.  Only some features from this draft standard are
     supported by the GNU C Library.

 -- Macro: __STDC_WANT_LIB_EXT2__

     If you define this macro to the value ‘1’, features from ISO/IEC TR
     24731-2:2010 (Dynamic Allocation Functions) are enabled.  Only some
     of the features from this TR are supported by the GNU C Library.

 -- Macro: __STDC_WANT_IEC_60559_BFP_EXT__

     If you define this macro, features from ISO/IEC TS 18661-1:2014
     (Floating-point extensions for C: Binary floating-point arithmetic)
     are enabled.  Only some of the features from this TS are supported
     by the GNU C Library.

 -- Macro: __STDC_WANT_IEC_60559_FUNCS_EXT__

     If you define this macro, features from ISO/IEC TS 18661-4:2015
     (Floating-point extensions for C: Supplementary functions) are
     enabled.  Only some of the features from this TS are supported by
     the GNU C Library.

 -- Macro: __STDC_WANT_IEC_60559_TYPES_EXT__

     If you define this macro, features from ISO/IEC TS 18661-3:2015
     (Floating-point extensions for C: Interchange and extended types)
     are enabled.  Only some of the features from this TS are supported
     by the GNU C Library.

 -- Macro: _GNU_SOURCE

     If you define this macro, everything is included: ISO C89, ISO C99,
     POSIX.1, POSIX.2, BSD, SVID, X/Open, LFS, and GNU extensions.  In
     the cases where POSIX.1 conflicts with BSD, the POSIX definitions
     take precedence.

 -- Macro: _DEFAULT_SOURCE

     If you define this macro, most features are included apart from
     X/Open, LFS and GNU extensions: the effect is to enable features
     from the 2008 edition of POSIX, as well as certain BSD and SVID
     features without a separate feature test macro to control them.

     Be aware that compiler options also affect included features:

        • If you use a strict conformance option, features beyond those
          from the compiler’s language version will be disabled, though
          feature test macros may be used to enable them.

        • Features enabled by compiler options are not overridden by
          feature test macros.

 -- Macro: _ATFILE_SOURCE

     If this macro is defined, additional ‘*at’ interfaces are included.

 -- Macro: _FORTIFY_SOURCE

     If this macro is defined to 1, security hardening is added to
     various library functions.  If defined to 2, even stricter checks
     are applied.  If defined to 3, the GNU C Library may also use
     checks that may have an additional performance overhead.

 -- Macro: _REENTRANT
 -- Macro: _THREAD_SAFE

     These macros are obsolete.  They have the same effect as defining
     ‘_POSIX_C_SOURCE’ with the value ‘199506L’.

     Some very old C libraries required one of these macros to be
     defined for basic functionality (e.g. ‘getchar’) to be thread-safe.

   We recommend you use ‘_GNU_SOURCE’ in new programs.  If you don’t
specify the ‘-ansi’ option to GCC, or other conformance options such as
‘-std=c99’, and don’t define any of these macros explicitly, the effect
is the same as defining ‘_DEFAULT_SOURCE’ to 1.

   When you define a feature test macro to request a larger class of
features, it is harmless to define in addition a feature test macro for
a subset of those features.  For example, if you define
‘_POSIX_C_SOURCE’, then defining ‘_POSIX_SOURCE’ as well has no effect.
Likewise, if you define ‘_GNU_SOURCE’, then defining either
‘_POSIX_SOURCE’ or ‘_POSIX_C_SOURCE’ as well has no effect.


File: libc.info,  Node: Roadmap to the Manual,  Prev: Using the Library,  Up: Introduction

1.4 Roadmap to the Manual
=========================

Here is an overview of the contents of the remaining chapters of this
manual.

   • *note Error Reporting::, describes how errors detected by the
     library are reported.

   • *note Memory::, describes the GNU C Library’s facilities for
     managing and using virtual and real memory, including dynamic
     allocation of virtual memory.  If you do not know in advance how
     much memory your program needs, you can allocate it dynamically
     instead, and manipulate it via pointers.

   • *note Character Handling::, contains information about character
     classification functions (such as ‘isspace’) and functions for
     performing case conversion.

   • *note String and Array Utilities::, has descriptions of functions
     for manipulating strings (null-terminated character arrays) and
     general byte arrays, including operations such as copying and
     comparison.

   • *note Character Set Handling::, contains information about
     manipulating characters and strings using character sets larger
     than will fit in the usual ‘char’ data type.

   • *note Locales::, describes how selecting a particular country or
     language affects the behavior of the library.  For example, the
     locale affects collation sequences for strings and how monetary
     values are formatted.

   • *note Searching and Sorting::, contains information about functions
     for searching and sorting arrays.  You can use these functions on
     any kind of array by providing an appropriate comparison function.

   • *note Pattern Matching::, presents functions for matching regular
     expressions and shell file name patterns, and for expanding words
     as the shell does.

   • *note I/O Overview::, gives an overall look at the input and output
     facilities in the library, and contains information about basic
     concepts such as file names.

   • *note I/O on Streams::, describes I/O operations involving streams
     (or ‘FILE *’ objects).  These are the normal C library functions
     from ‘stdio.h’.

   • *note Low-Level I/O::, contains information about I/O operations on
     file descriptors.  File descriptors are a lower-level mechanism
     specific to the Unix family of operating systems.

   • *note File System Interface::, has descriptions of operations on
     entire files, such as functions for deleting and renaming them and
     for creating new directories.  This chapter also contains
     information about how you can access the attributes of a file, such
     as its owner and file protection modes.

   • *note Pipes and FIFOs::, contains information about simple
     interprocess communication mechanisms.  Pipes allow communication
     between two related processes (such as between a parent and child),
     while FIFOs allow communication between processes sharing a common
     file system on the same machine.

   • *note Sockets::, describes a more complicated interprocess
     communication mechanism that allows processes running on different
     machines to communicate over a network.  This chapter also contains
     information about Internet host addressing and how to use the
     system network databases.

   • *note Low-Level Terminal Interface::, describes how you can change
     the attributes of a terminal device.  If you want to disable echo
     of characters typed by the user, for example, read this chapter.

   • *note Mathematics::, contains information about the math library
     functions.  These include things like random-number generators and
     remainder functions on integers as well as the usual trigonometric
     and exponential functions on floating-point numbers.

   • *note Low-Level Arithmetic Functions: Arithmetic, describes
     functions for simple arithmetic, analysis of floating-point values,
     and reading numbers from strings.

   • *note Date and Time::, describes functions for measuring both
     calendar time and CPU time, as well as functions for setting alarms
     and timers.

   • *note Non-Local Exits::, contains descriptions of the ‘setjmp’ and
     ‘longjmp’ functions.  These functions provide a facility for
     ‘goto’-like jumps which can jump from one function to another.

   • *note Signal Handling::, tells you all about signals—what they are,
     how to establish a handler that is called when a particular kind of
     signal is delivered, and how to prevent signals from arriving
     during critical sections of your program.

   • *note Program Basics::, tells how your programs can access their
     command-line arguments and environment variables.

   • *note Processes::, contains information about how to start new
     processes and run programs.

   • *note Job Control::, describes functions for manipulating process
     groups and the controlling terminal.  This material is probably
     only of interest if you are writing a shell or other program which
     handles job control specially.

   • *note Name Service Switch::, describes the services which are
     available for looking up names in the system databases, how to
     determine which service is used for which database, and how these
     services are implemented so that contributors can design their own
     services.

   • *note User Database::, and *note Group Database::, tell you how to
     access the system user and group databases.

   • *note System Management::, describes functions for controlling and
     getting information about the hardware and software configuration
     your program is executing under.

   • *note System Configuration::, tells you how you can get information
     about various operating system limits.  Most of these parameters
     are provided for compatibility with POSIX.

   • *note Language Features::, contains information about library
     support for standard parts of the C language, including things like
     the ‘sizeof’ operator and the symbolic constant ‘NULL’, how to
     write functions accepting variable numbers of arguments, and
     constants describing the ranges and other properties of the
     numerical types.  There is also a simple debugging mechanism which
     allows you to put assertions in your code, and have diagnostic
     messages printed if the tests fail.

   • *note Library Summary::, gives a summary of all the functions,
     variables, and macros in the library, with complete data types and
     function prototypes, and says what standard or system each is
     derived from.

   • *note Installation::, explains how to build and install the GNU C
     Library on your system, and how to report any bugs you might find.

   • *note Maintenance::, explains how to add new functions or port the
     library to a new system.

   If you already know the name of the facility you are interested in,
you can look it up in *note Library Summary::.  This gives you a summary
of its syntax and a pointer to where you can find a more detailed
description.  This appendix is particularly useful if you just want to
verify the order and type of arguments to a function, for example.  It
also tells you what standard or system each function, variable, or macro
is derived from.


File: libc.info,  Node: Error Reporting,  Next: Memory,  Prev: Introduction,  Up: Top

2 Error Reporting
*****************

Many functions in the GNU C Library detect and report error conditions,
and sometimes your programs need to check for these error conditions.
For example, when you open an input file, you should verify that the
file was actually opened correctly, and print an error message or take
other appropriate action if the call to the library function failed.

   This chapter describes how the error reporting facility works.  Your
program should include the header file ‘errno.h’ to use this facility.

* Menu:

* Checking for Errors::         How errors are reported by library functions.
* Error Codes::                 Error code macros; all of these expand
                                 into integer constant values.
* Error Messages::              Mapping error codes onto error messages.


File: libc.info,  Node: Checking for Errors,  Next: Error Codes,  Up: Error Reporting

2.1 Checking for Errors
=======================

Most library functions return a special value to indicate that they have
failed.  The special value is typically ‘-1’, a null pointer, or a
constant such as ‘EOF’ that is defined for that purpose.  But this
return value tells you only that an error has occurred.  To find out
what kind of error it was, you need to look at the error code stored in
the variable ‘errno’.  This variable is declared in the header file
‘errno.h’.

 -- Variable: volatile int errno

     The variable ‘errno’ contains the system error number.  You can
     change the value of ‘errno’.

     Since ‘errno’ is declared ‘volatile’, it might be changed
     asynchronously by a signal handler; see *note Defining Handlers::.
     However, a properly written signal handler saves and restores the
     value of ‘errno’, so you generally do not need to worry about this
     possibility except when writing signal handlers.

     The initial value of ‘errno’ at program startup is zero.  In many
     cases, when a library function encounters an error, it will set
     ‘errno’ to a non-zero value to indicate what specific error
     condition occurred.  The documentation for each function lists the
     error conditions that are possible for that function.  Not all
     library functions use this mechanism; some return an error code
     directly, instead.

     *Warning:* Many library functions may set ‘errno’ to some
     meaningless non-zero value even if they did not encounter any
     errors, and even if they return error codes directly.  Therefore,
     it is usually incorrect to check _whether_ an error occurred by
     inspecting the value of ‘errno’.  The proper way to check for error
     is documented for each function.

     *Portability Note:* ISO C specifies ‘errno’ as a “modifiable
     lvalue” rather than as a variable, permitting it to be implemented
     as a macro.  For example, its expansion might involve a function
     call, like ‘*__errno_location ()’.  In fact, that is what it is on
     GNU/Linux and GNU/Hurd systems.  The GNU C Library, on each system,
     does whatever is right for the particular system.

     There are a few library functions, like ‘sqrt’ and ‘atan’, that
     return a perfectly legitimate value in case of an error, but also
     set ‘errno’.  For these functions, if you want to check to see
     whether an error occurred, the recommended method is to set ‘errno’
     to zero before calling the function, and then check its value
     afterward.

   All the error codes have symbolic names; they are macros defined in
‘errno.h’.  The names start with ‘E’ and an upper-case letter or digit;
you should consider names of this form to be reserved names.  *Note
Reserved Names::.

   The error code values are all positive integers and are all distinct,
with one exception: ‘EWOULDBLOCK’ and ‘EAGAIN’ are the same.  Since the
values are distinct, you can use them as labels in a ‘switch’ statement;
just don’t use both ‘EWOULDBLOCK’ and ‘EAGAIN’.  Your program should not
make any other assumptions about the specific values of these symbolic
constants.

   The value of ‘errno’ doesn’t necessarily have to correspond to any of
these macros, since some library functions might return other error
codes of their own for other situations.  The only values that are
guaranteed to be meaningful for a particular library function are the
ones that this manual lists for that function.

   Except on GNU/Hurd systems, almost any system call can return
‘EFAULT’ if it is given an invalid pointer as an argument.  Since this
could only happen as a result of a bug in your program, and since it
will not happen on GNU/Hurd systems, we have saved space by not
mentioning ‘EFAULT’ in the descriptions of individual functions.

   In some Unix systems, many system calls can also return ‘EFAULT’ if
given as an argument a pointer into the stack, and the kernel for some
obscure reason fails in its attempt to extend the stack.  If this ever
happens, you should probably try using statically or dynamically
allocated memory instead of stack memory on that system.


File: libc.info,  Node: Error Codes,  Next: Error Messages,  Prev: Checking for Errors,  Up: Error Reporting

2.2 Error Codes
===============

The error code macros are defined in the header file ‘errno.h’.  All of
them expand into integer constant values.  Some of these error codes
can’t occur on GNU systems, but they can occur using the GNU C Library
on other systems.

 -- Macro: int EPERM

     “Operation not permitted.” Only the owner of the file (or other
     resource) or processes with special privileges can perform the
     operation.

 -- Macro: int ENOENT

     “No such file or directory.” This is a “file doesn’t exist” error
     for ordinary files that are referenced in contexts where they are
     expected to already exist.

 -- Macro: int ESRCH

     “No such process.” No process matches the specified process ID.

 -- Macro: int EINTR

     “Interrupted system call.” An asynchronous signal occurred and
     prevented completion of the call.  When this happens, you should
     try the call again.

     You can choose to have functions resume after a signal that is
     handled, rather than failing with ‘EINTR’; see *note Interrupted
     Primitives::.

 -- Macro: int EIO

     “Input/output error.” Usually used for physical read or write
     errors.

 -- Macro: int ENXIO

     “No such device or address.” The system tried to use the device
     represented by a file you specified, and it couldn’t find the
     device.  This can mean that the device file was installed
     incorrectly, or that the physical device is missing or not
     correctly attached to the computer.

 -- Macro: int E2BIG

     “Argument list too long.” Used when the arguments passed to a new
     program being executed with one of the ‘exec’ functions (*note
     Executing a File::) occupy too much memory space.  This condition
     never arises on GNU/Hurd systems.

 -- Macro: int ENOEXEC

     “Exec format error.” Invalid executable file format.  This
     condition is detected by the ‘exec’ functions; see *note Executing
     a File::.

 -- Macro: int EBADF

     “Bad file descriptor.” For example, I/O on a descriptor that has
     been closed or reading from a descriptor open only for writing (or
     vice versa).

 -- Macro: int ECHILD

     “No child processes.” This error happens on operations that are
     supposed to manipulate child processes, when there aren’t any
     processes to manipulate.

 -- Macro: int EDEADLK

     “Resource deadlock avoided.” Allocating a system resource would
     have resulted in a deadlock situation.  The system does not
     guarantee that it will notice all such situations.  This error
     means you got lucky and the system noticed; it might just hang.
     *Note File Locks::, for an example.

 -- Macro: int ENOMEM

     “Cannot allocate memory.” The system cannot allocate more virtual
     memory because its capacity is full.

 -- Macro: int EACCES

     “Permission denied.” The file permissions do not allow the
     attempted operation.

 -- Macro: int EFAULT

     “Bad address.” An invalid pointer was detected.  On GNU/Hurd
     systems, this error never happens; you get a signal instead.

 -- Macro: int ENOTBLK

     “Block device required.” A file that isn’t a block special file was
     given in a situation that requires one.  For example, trying to
     mount an ordinary file as a file system in Unix gives this error.

 -- Macro: int EBUSY

     “Device or resource busy.” A system resource that can’t be shared
     is already in use.  For example, if you try to delete a file that
     is the root of a currently mounted filesystem, you get this error.

 -- Macro: int EEXIST

     “File exists.” An existing file was specified in a context where it
     only makes sense to specify a new file.

 -- Macro: int EXDEV

     “Invalid cross-device link.” An attempt to make an improper link
     across file systems was detected.  This happens not only when you
     use ‘link’ (*note Hard Links::) but also when you rename a file
     with ‘rename’ (*note Renaming Files::).

 -- Macro: int ENODEV

     “No such device.” The wrong type of device was given to a function
     that expects a particular sort of device.

 -- Macro: int ENOTDIR

     “Not a directory.” A file that isn’t a directory was specified when
     a directory is required.

 -- Macro: int EISDIR

     “Is a directory.” You cannot open a directory for writing, or
     create or remove hard links to it.

 -- Macro: int EINVAL

     “Invalid argument.” This is used to indicate various kinds of
     problems with passing the wrong argument to a library function.

 -- Macro: int EMFILE

     “Too many open files.” The current process has too many files open
     and can’t open any more.  Duplicate descriptors do count toward
     this limit.

     In BSD and GNU, the number of open files is controlled by a
     resource limit that can usually be increased.  If you get this
     error, you might want to increase the ‘RLIMIT_NOFILE’ limit or make
     it unlimited; *note Limits on Resources::.

 -- Macro: int ENFILE

     “Too many open files in system.” There are too many distinct file
     openings in the entire system.  Note that any number of linked
     channels count as just one file opening; see *note Linked
     Channels::.  This error never occurs on GNU/Hurd systems.

 -- Macro: int ENOTTY

     “Inappropriate ioctl for device.” Inappropriate I/O control
     operation, such as trying to set terminal modes on an ordinary
     file.

 -- Macro: int ETXTBSY

     “Text file busy.” An attempt to execute a file that is currently
     open for writing, or write to a file that is currently being
     executed.  Often using a debugger to run a program is considered
     having it open for writing and will cause this error.  (The name
     stands for “text file busy”.)  This is not an error on GNU/Hurd
     systems; the text is copied as necessary.

 -- Macro: int EFBIG

     “File too large.” The size of a file would be larger than allowed
     by the system.

 -- Macro: int ENOSPC

     “No space left on device.” Write operation on a file failed because
     the disk is full.

 -- Macro: int ESPIPE

     “Illegal seek.” Invalid seek operation (such as on a pipe).

 -- Macro: int EROFS

     “Read-only file system.” An attempt was made to modify something on
     a read-only file system.

 -- Macro: int EMLINK

     “Too many links.” The link count of a single file would become too
     large.  ‘rename’ can cause this error if the file being renamed
     already has as many links as it can take (*note Renaming Files::).

 -- Macro: int EPIPE

     “Broken pipe.” There is no process reading from the other end of a
     pipe.  Every library function that returns this error code also
     generates a ‘SIGPIPE’ signal; this signal terminates the program if
     not handled or blocked.  Thus, your program will never actually see
     ‘EPIPE’ unless it has handled or blocked ‘SIGPIPE’.

 -- Macro: int EDOM

     “Numerical argument out of domain.” Used by mathematical functions
     when an argument value does not fall into the domain over which the
     function is defined.

 -- Macro: int ERANGE

     “Numerical result out of range.” Used by mathematical functions
     when the result value is not representable because of overflow or
     underflow.

 -- Macro: int EAGAIN

     “Resource temporarily unavailable.” The call might work if you try
     again later.  The macro ‘EWOULDBLOCK’ is another name for ‘EAGAIN’;
     they are always the same in the GNU C Library.

     This error can happen in a few different situations:

        • An operation that would block was attempted on an object that
          has non-blocking mode selected.  Trying the same operation
          again will block until some external condition makes it
          possible to read, write, or connect (whatever the operation).
          You can use ‘select’ to find out when the operation will be
          possible; *note Waiting for I/O::.

          *Portability Note:* In many older Unix systems, this condition
          was indicated by ‘EWOULDBLOCK’, which was a distinct error
          code different from ‘EAGAIN’.  To make your program portable,
          you should check for both codes and treat them the same.

        • A temporary resource shortage made an operation impossible.
          ‘fork’ can return this error.  It indicates that the shortage
          is expected to pass, so your program can try the call again
          later and it may succeed.  It is probably a good idea to delay
          for a few seconds before trying it again, to allow time for
          other processes to release scarce resources.  Such shortages
          are usually fairly serious and affect the whole system, so
          usually an interactive program should report the error to the
          user and return to its command loop.

 -- Macro: int EWOULDBLOCK

     “Operation would block.” In the GNU C Library, this is another name
     for ‘EAGAIN’ (above).  The values are always the same, on every
     operating system.

     C libraries in many older Unix systems have ‘EWOULDBLOCK’ as a
     separate error code.

 -- Macro: int EINPROGRESS

     “Operation now in progress.” An operation that cannot complete
     immediately was initiated on an object that has non-blocking mode
     selected.  Some functions that must always block (such as
     ‘connect’; *note Connecting::) never return ‘EAGAIN’.  Instead,
     they return ‘EINPROGRESS’ to indicate that the operation has begun
     and will take some time.  Attempts to manipulate the object before
     the call completes return ‘EALREADY’.  You can use the ‘select’
     function to find out when the pending operation has completed;
     *note Waiting for I/O::.

 -- Macro: int EALREADY

     “Operation already in progress.” An operation is already in
     progress on an object that has non-blocking mode selected.

 -- Macro: int ENOTSOCK

     “Socket operation on non-socket.” A file that isn’t a socket was
     specified when a socket is required.

 -- Macro: int EMSGSIZE

     “Message too long.” The size of a message sent on a socket was
     larger than the supported maximum size.

 -- Macro: int EPROTOTYPE

     “Protocol wrong type for socket.” The socket type does not support
     the requested communications protocol.

 -- Macro: int ENOPROTOOPT

     “Protocol not available.” You specified a socket option that
     doesn’t make sense for the particular protocol being used by the
     socket.  *Note Socket Options::.

 -- Macro: int EPROTONOSUPPORT

     “Protocol not supported.” The socket domain does not support the
     requested communications protocol (perhaps because the requested
     protocol is completely invalid).  *Note Creating a Socket::.

 -- Macro: int ESOCKTNOSUPPORT

     “Socket type not supported.” The socket type is not supported.

 -- Macro: int EOPNOTSUPP

     “Operation not supported.” The operation you requested is not
     supported.  Some socket functions don’t make sense for all types of
     sockets, and others may not be implemented for all communications
     protocols.  On GNU/Hurd systems, this error can happen for many
     calls when the object does not support the particular operation; it
     is a generic indication that the server knows nothing to do for
     that call.

 -- Macro: int EPFNOSUPPORT

     “Protocol family not supported.” The socket communications protocol
     family you requested is not supported.

 -- Macro: int EAFNOSUPPORT

     “Address family not supported by protocol.” The address family
     specified for a socket is not supported; it is inconsistent with
     the protocol being used on the socket.  *Note Sockets::.

 -- Macro: int EADDRINUSE

     “Address already in use.” The requested socket address is already
     in use.  *Note Socket Addresses::.

 -- Macro: int EADDRNOTAVAIL

     “Cannot assign requested address.” The requested socket address is
     not available; for example, you tried to give a socket a name that
     doesn’t match the local host name.  *Note Socket Addresses::.

 -- Macro: int ENETDOWN

     “Network is down.” A socket operation failed because the network
     was down.

 -- Macro: int ENETUNREACH

     “Network is unreachable.” A socket operation failed because the
     subnet containing the remote host was unreachable.

 -- Macro: int ENETRESET

     “Network dropped connection on reset.” A network connection was
     reset because the remote host crashed.

 -- Macro: int ECONNABORTED

     “Software caused connection abort.” A network connection was
     aborted locally.

 -- Macro: int ECONNRESET

     “Connection reset by peer.” A network connection was closed for
     reasons outside the control of the local host, such as by the
     remote machine rebooting or an unrecoverable protocol violation.

 -- Macro: int ENOBUFS

     “No buffer space available.” The kernel’s buffers for I/O
     operations are all in use.  In GNU, this error is always synonymous
     with ‘ENOMEM’; you may get one or the other from network
     operations.

 -- Macro: int EISCONN

     “Transport endpoint is already connected.” You tried to connect a
     socket that is already connected.  *Note Connecting::.

 -- Macro: int ENOTCONN

     “Transport endpoint is not connected.” The socket is not connected
     to anything.  You get this error when you try to transmit data over
     a socket, without first specifying a destination for the data.  For
     a connectionless socket (for datagram protocols, such as UDP), you
     get ‘EDESTADDRREQ’ instead.

 -- Macro: int EDESTADDRREQ

     “Destination address required.” No default destination address was
     set for the socket.  You get this error when you try to transmit
     data over a connectionless socket, without first specifying a
     destination for the data with ‘connect’.

 -- Macro: int ESHUTDOWN

     “Cannot send after transport endpoint shutdown.” The socket has
     already been shut down.

 -- Macro: int ETOOMANYREFS

     “Too many references: cannot splice.”

 -- Macro: int ETIMEDOUT

     “Connection timed out.” A socket operation with a specified timeout
     received no response during the timeout period.

 -- Macro: int ECONNREFUSED

     “Connection refused.” A remote host refused to allow the network
     connection (typically because it is not running the requested
     service).

 -- Macro: int ELOOP

     “Too many levels of symbolic links.” Too many levels of symbolic
     links were encountered in looking up a file name.  This often
     indicates a cycle of symbolic links.

 -- Macro: int ENAMETOOLONG

     “File name too long.” Filename too long (longer than ‘PATH_MAX’;
     *note Limits for Files::) or host name too long (in ‘gethostname’
     or ‘sethostname’; *note Host Identification::).

 -- Macro: int EHOSTDOWN

     “Host is down.” The remote host for a requested network connection
     is down.

 -- Macro: int EHOSTUNREACH

     “No route to host.” The remote host for a requested network
     connection is not reachable.

 -- Macro: int ENOTEMPTY

     “Directory not empty.” Directory not empty, where an empty
     directory was expected.  Typically, this error occurs when you are
     trying to delete a directory.

 -- Macro: int EPROCLIM

     “Too many processes.” This means that the per-user limit on new
     process would be exceeded by an attempted ‘fork’.  *Note Limits on
     Resources::, for details on the ‘RLIMIT_NPROC’ limit.

 -- Macro: int EUSERS

     “Too many users.” The file quota system is confused because there
     are too many users.

 -- Macro: int EDQUOT

     “Disk quota exceeded.” The user’s disk quota was exceeded.

 -- Macro: int ESTALE

     “Stale file handle.” This indicates an internal confusion in the
     file system which is due to file system rearrangements on the
     server host for NFS file systems or corruption in other file
     systems.  Repairing this condition usually requires unmounting,
     possibly repairing and remounting the file system.

 -- Macro: int EREMOTE

     “Object is remote.” An attempt was made to NFS-mount a remote file
     system with a file name that already specifies an NFS-mounted file.
     (This is an error on some operating systems, but we expect it to
     work properly on GNU/Hurd systems, making this error code
     impossible.)

 -- Macro: int EBADRPC

     “RPC struct is bad.”

 -- Macro: int ERPCMISMATCH

     “RPC version wrong.”

 -- Macro: int EPROGUNAVAIL

     “RPC program not available.”

 -- Macro: int EPROGMISMATCH

     “RPC program version wrong.”

 -- Macro: int EPROCUNAVAIL

     “RPC bad procedure for program.”

 -- Macro: int ENOLCK

     “No locks available.” This is used by the file locking facilities;
     see *note File Locks::.  This error is never generated by GNU/Hurd
     systems, but it can result from an operation to an NFS server
     running another operating system.

 -- Macro: int EFTYPE

     “Inappropriate file type or format.” The file was the wrong type
     for the operation, or a data file had the wrong format.

     On some systems ‘chmod’ returns this error if you try to set the
     sticky bit on a non-directory file; *note Setting Permissions::.

 -- Macro: int EAUTH

     “Authentication error.”

 -- Macro: int ENEEDAUTH

     “Need authenticator.”

 -- Macro: int ENOSYS

     “Function not implemented.” This indicates that the function called
     is not implemented at all, either in the C library itself or in the
     operating system.  When you get this error, you can be sure that
     this particular function will always fail with ‘ENOSYS’ unless you
     install a new version of the C library or the operating system.

 -- Macro: int ENOTSUP

     “Not supported.” A function returns this error when certain
     parameter values are valid, but the functionality they request is
     not available.  This can mean that the function does not implement
     a particular command or option value or flag bit at all.  For
     functions that operate on some object given in a parameter, such as
     a file descriptor or a port, it might instead mean that only _that
     specific object_ (file descriptor, port, etc.)  is unable to
     support the other parameters given; different file descriptors
     might support different ranges of parameter values.

     If the entire function is not available at all in the
     implementation, it returns ‘ENOSYS’ instead.

 -- Macro: int EILSEQ

     “Invalid or incomplete multibyte or wide character.” While decoding
     a multibyte character the function came along an invalid or an
     incomplete sequence of bytes or the given wide character is
     invalid.

 -- Macro: int EBACKGROUND

     “Inappropriate operation for background process.” On GNU/Hurd
     systems, servers supporting the ‘term’ protocol return this error
     for certain operations when the caller is not in the foreground
     process group of the terminal.  Users do not usually see this error
     because functions such as ‘read’ and ‘write’ translate it into a
     ‘SIGTTIN’ or ‘SIGTTOU’ signal.  *Note Job Control::, for
     information on process groups and these signals.

 -- Macro: int EDIED

     “Translator died.” On GNU/Hurd systems, opening a file returns this
     error when the file is translated by a program and the translator
     program dies while starting up, before it has connected to the
     file.

 -- Macro: int ED

     “?.” The experienced user will know what is wrong.

 -- Macro: int EGREGIOUS

     “You really blew it this time.” You did *what*?

 -- Macro: int EIEIO

     “Computer bought the farm.” Go home and have a glass of warm,
     dairy-fresh milk.

 -- Macro: int EGRATUITOUS

     “Gratuitous error.” This error code has no purpose.

 -- Macro: int EBADMSG

     “Bad message.”

 -- Macro: int EIDRM

     “Identifier removed.”

 -- Macro: int EMULTIHOP

     “Multihop attempted.”

 -- Macro: int ENODATA

     “No data available.”

 -- Macro: int ENOLINK

     “Link has been severed.”

 -- Macro: int ENOMSG

     “No message of desired type.”

 -- Macro: int ENOSR

     “Out of streams resources.”

 -- Macro: int ENOSTR

     “Device not a stream.”

 -- Macro: int EOVERFLOW

     “Value too large for defined data type.”

 -- Macro: int EPROTO

     “Protocol error.”

 -- Macro: int ETIME

     “Timer expired.”

 -- Macro: int ECANCELED

     “Operation canceled.” An asynchronous operation was canceled before
     it completed.  *Note Asynchronous I/O::.  When you call
     ‘aio_cancel’, the normal result is for the operations affected to
     complete with this error; *note Cancel AIO Operations::.

 -- Macro: int EOWNERDEAD

     “Owner died.”

 -- Macro: int ENOTRECOVERABLE

     “State not recoverable.”

   _The following error codes are defined by the Linux/i386 kernel.
They are not yet documented._

 -- Macro: int ERESTART

     “Interrupted system call should be restarted.”

 -- Macro: int ECHRNG

     “Channel number out of range.”

 -- Macro: int EL2NSYNC

     “Level 2 not synchronized.”

 -- Macro: int EL3HLT

     “Level 3 halted.”

 -- Macro: int EL3RST

     “Level 3 reset.”

 -- Macro: int ELNRNG

     “Link number out of range.”

 -- Macro: int EUNATCH

     “Protocol driver not attached.”

 -- Macro: int ENOCSI

     “No CSI structure available.”

 -- Macro: int EL2HLT

     “Level 2 halted.”

 -- Macro: int EBADE

     “Invalid exchange.”

 -- Macro: int EBADR

     “Invalid request descriptor.”

 -- Macro: int EXFULL

     “Exchange full.”

 -- Macro: int ENOANO

     “No anode.”

 -- Macro: int EBADRQC

     “Invalid request code.”

 -- Macro: int EBADSLT

     “Invalid slot.”

 -- Macro: int EDEADLOCK

     “File locking deadlock error.”

 -- Macro: int EBFONT

     “Bad font file format.”

 -- Macro: int ENONET

     “Machine is not on the network.”

 -- Macro: int ENOPKG

     “Package not installed.”

 -- Macro: int EADV

     “Advertise error.”

 -- Macro: int ESRMNT

     “Srmount error.”

 -- Macro: int ECOMM

     “Communication error on send.”

 -- Macro: int EDOTDOT

     “RFS specific error.”

 -- Macro: int ENOTUNIQ

     “Name not unique on network.”

 -- Macro: int EBADFD

     “File descriptor in bad state.”

 -- Macro: int EREMCHG

     “Remote address changed.”

 -- Macro: int ELIBACC

     “Can not access a needed shared library.”

 -- Macro: int ELIBBAD

     “Accessing a corrupted shared library.”

 -- Macro: int ELIBSCN

     “.lib section in a.out corrupted.”

 -- Macro: int ELIBMAX

     “Attempting to link in too many shared libraries.”

 -- Macro: int ELIBEXEC

     “Cannot exec a shared library directly.”

 -- Macro: int ESTRPIPE

     “Streams pipe error.”

 -- Macro: int EUCLEAN

     “Structure needs cleaning.”

 -- Macro: int ENOTNAM

     “Not a XENIX named type file.”

 -- Macro: int ENAVAIL

     “No XENIX semaphores available.”

 -- Macro: int EISNAM

     “Is a named type file.”

 -- Macro: int EREMOTEIO

     “Remote I/O error.”

 -- Macro: int ENOMEDIUM

     “No medium found.”

 -- Macro: int EMEDIUMTYPE

     “Wrong medium type.”

 -- Macro: int ENOKEY

     “Required key not available.”

 -- Macro: int EKEYEXPIRED

     “Key has expired.”

 -- Macro: int EKEYREVOKED

     “Key has been revoked.”

 -- Macro: int EKEYREJECTED

     “Key was rejected by service.”

 -- Macro: int ERFKILL

     “Operation not possible due to RF-kill.”

 -- Macro: int EHWPOISON

     “Memory page has hardware error.”


File: libc.info,  Node: Error Messages,  Prev: Error Codes,  Up: Error Reporting

2.3 Error Messages
==================

The library has functions and variables designed to make it easy for
your program to report informative error messages in the customary
format about the failure of a library call.  The functions ‘strerror’
and ‘perror’ give you the standard error message for a given error code;
the variable ‘program_invocation_short_name’ gives you convenient access
to the name of the program that encountered the error.

 -- Function: char * strerror (int ERRNUM)

     Preliminary: | MT-Unsafe race:strerror | AS-Unsafe heap i18n |
     AC-Unsafe mem | *Note POSIX Safety Concepts::.

     The ‘strerror’ function maps the error code (*note Checking for
     Errors::) specified by the ERRNUM argument to a descriptive error
     message string.  The return value is a pointer to this string.

     The value ERRNUM normally comes from the variable ‘errno’.

     You should not modify the string returned by ‘strerror’.  Also, if
     you make subsequent calls to ‘strerror’, the string might be
     overwritten.  (But it’s guaranteed that no library function ever
     calls ‘strerror’ behind your back.)

     The function ‘strerror’ is declared in ‘string.h’.

 -- Function: char * strerror_r (int ERRNUM, char *BUF, size_t N)

     Preliminary: | MT-Safe | AS-Unsafe i18n | AC-Unsafe | *Note POSIX
     Safety Concepts::.

     The ‘strerror_r’ function works like ‘strerror’ but instead of
     returning the error message in a statically allocated buffer shared
     by all threads in the process, it returns a private copy for the
     thread.  This might be either some permanent global data or a
     message string in the user supplied buffer starting at BUF with the
     length of N bytes.

     At most N characters are written (including the NUL byte) so it is
     up to the user to select a buffer large enough.

     This function should always be used in multi-threaded programs
     since there is no way to guarantee the string returned by
     ‘strerror’ really belongs to the last call of the current thread.

     The function ‘strerror_r’ is a GNU extension and it is declared in
     ‘string.h’.

 -- Function: void perror (const char *MESSAGE)

     Preliminary: | MT-Safe race:stderr | AS-Unsafe corrupt i18n heap
     lock | AC-Unsafe corrupt lock mem fd | *Note POSIX Safety
     Concepts::.

     This function prints an error message to the stream ‘stderr’; see
     *note Standard Streams::.  The orientation of ‘stderr’ is not
     changed.

     If you call ‘perror’ with a MESSAGE that is either a null pointer
     or an empty string, ‘perror’ just prints the error message
     corresponding to ‘errno’, adding a trailing newline.

     If you supply a non-null MESSAGE argument, then ‘perror’ prefixes
     its output with this string.  It adds a colon and a space character
     to separate the MESSAGE from the error string corresponding to
     ‘errno’.

     The function ‘perror’ is declared in ‘stdio.h’.

 -- Function: const char * strerrorname_np (int ERRNUM)

     | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety Concepts::.

     This function returns the name describing the error ERRNUM or
     ‘NULL’ if there is no known constant with this value (e.g "EINVAL"
     for ‘EINVAL’).

     This function is a GNU extension, declared in the header file
     ‘string.h’.

 -- Function: const char * strerrordesc_np (int ERRNUM)

     | MT-Safe | AS-Safe | AC-Safe | *Note POSIX Safety Concepts::.

     This function returns the message describing the error ERRNUM or
     ‘NULL’ if there is no known constant with this value (e.g "Invalid
     argument" for ‘EINVAL’).  Different than ‘strerror’ the returned
     description is not translated.

     This function is a GNU extension, declared in the header file
     ‘string.h’.

   ‘strerror’ and ‘perror’ produce the exact same message for any given
error code; the precise text varies from system to system.  With the GNU
C Library, the messages are fairly short; there are no multi-line
messages or embedded newlines.  Each error message begins with a capital
letter and does not include any terminating punctuation.

   Many programs that don’t read input from the terminal are designed to
exit if any system call fails.  By convention, the error message from
such a program should start with the program’s name, sans directories.
You can find that name in the variable ‘program_invocation_short_name’;
the full file name is stored the variable ‘program_invocation_name’.

 -- Variable: char * program_invocation_name

     This variable’s value is the name that was used to invoke the
     program running in the current process.  It is the same as
     ‘argv[0]’.  Note that this is not necessarily a useful file name;
     often it contains no directory names.  *Note Program Arguments::.

     This variable is a GNU extension and is declared in ‘errno.h’.

 -- Variable: char * program_invocation_short_name

     This variable’s value is the name that was used to invoke the
     program running in the current process, with directory names
     removed.  (That is to say, it is the same as
     ‘program_invocation_name’ minus everything up to the last slash, if
     any.)

     This variable is a GNU extension and is declared in ‘errno.h’.

   The library initialization code sets up both of these variables
before calling ‘main’.

   *Portability Note:* If you want your program to work with non-GNU
libraries, you must save the value of ‘argv[0]’ in ‘main’, and then
strip off the directory names yourself.  We added these extensions to
make it possible to write self-contained error-reporting subroutines
that require no explicit cooperation from ‘main’.

   Here is an example showing how to handle failure to open a file
correctly.  The function ‘open_sesame’ tries to open the named file for
reading and returns a stream if successful.  The ‘fopen’ library
function returns a null pointer if it couldn’t open the file for some
reason.  In that situation, ‘open_sesame’ constructs an appropriate
error message using the ‘strerror’ function, and terminates the program.
If we were going to make some other library calls before passing the
error code to ‘strerror’, we’d have to save it in a local variable
instead, because those other library functions might overwrite ‘errno’
in the meantime.

     #define _GNU_SOURCE

     #include <errno.h>
     #include <stdio.h>
     #include <stdlib.h>
     #include <string.h>

     FILE *
     open_sesame (char *name)
     {
       FILE *stream;

       errno = 0;
       stream = fopen (name, "r");
       if (stream == NULL)
         {
           fprintf (stderr, "%s: Couldn't open file %s; %s\n",
                    program_invocation_short_name, name, strerror (errno));
           exit (EXIT_FAILURE);
         }
       else
         return stream;
     }

   Using ‘perror’ has the advantage that the function is portable and
available on all systems implementing ISO C. But often the text ‘perror’
generates is not what is wanted and there is no way to extend or change
what ‘perror’ does.  The GNU coding standard, for instance, requires
error messages to be preceded by the program name and programs which
read some input files should provide information about the input file
name and the line number in case an error is encountered while reading
the file.  For these occasions there are two functions available which
are widely used throughout the GNU project.  These functions are
declared in ‘error.h’.

 -- Function: void error (int STATUS, int ERRNUM, const char *FORMAT,
          ...)

     Preliminary: | MT-Safe locale | AS-Unsafe corrupt heap i18n |
     AC-Safe | *Note POSIX Safety Concepts::.

     The ‘error’ function can be used to report general problems during
     program execution.  The FORMAT argument is a format string just
     like those given to the ‘printf’ family of functions.  The
     arguments required for the format can follow the FORMAT parameter.
     Just like ‘perror’, ‘error’ also can report an error code in
     textual form.  But unlike ‘perror’ the error value is explicitly
     passed to the function in the ERRNUM parameter.  This eliminates
     the problem mentioned above that the error reporting function must
     be called immediately after the function causing the error since
     otherwise ‘errno’ might have a different value.

     ‘error’ prints first the program name.  If the application defined
     a global variable ‘error_print_progname’ and points it to a
     function this function will be called to print the program name.
     Otherwise the string from the global variable ‘program_name’ is
     used.  The program name is followed by a colon and a space which in
     turn is followed by the output produced by the format string.  If
     the ERRNUM parameter is non-zero the format string output is
     followed by a colon and a space, followed by the error message for
     the error code ERRNUM.  In any case is the output terminated with a
     newline.

     The output is directed to the ‘stderr’ stream.  If the ‘stderr’
     wasn’t oriented before the call it will be narrow-oriented
     afterwards.

     The function will return unless the STATUS parameter has a non-zero
     value.  In this case the function will call ‘exit’ with the STATUS
     value for its parameter and therefore never return.  If ‘error’
     returns, the global variable ‘error_message_count’ is incremented
     by one to keep track of the number of errors reported.

 -- Function: void error_at_line (int STATUS, int ERRNUM, const char
          *FNAME, unsigned int LINENO, const char *FORMAT, ...)

     Preliminary: | MT-Unsafe race:error_at_line/error_one_per_line
     locale | AS-Unsafe corrupt heap i18n | AC-Unsafe
     corrupt/error_one_per_line | *Note POSIX Safety Concepts::.

     The ‘error_at_line’ function is very similar to the ‘error’
     function.  The only differences are the additional parameters FNAME
     and LINENO.  The handling of the other parameters is identical to
     that of ‘error’ except that between the program name and the string
     generated by the format string additional text is inserted.

     Directly following the program name a colon, followed by the file
     name pointed to by FNAME, another colon, and the value of LINENO is
     printed.

     This additional output of course is meant to be used to locate an
     error in an input file (like a programming language source code
     file etc).

     If the global variable ‘error_one_per_line’ is set to a non-zero
     value ‘error_at_line’ will avoid printing consecutive messages for
     the same file and line.  Repetition which are not directly
     following each other are not caught.

     Just like ‘error’ this function only returns if STATUS is zero.
     Otherwise ‘exit’ is called with the non-zero value.  If ‘error’
     returns, the global variable ‘error_message_count’ is incremented
     by one to keep track of the number of errors reported.

   As mentioned above, the ‘error’ and ‘error_at_line’ functions can be
customized by defining a variable named ‘error_print_progname’.

 -- Variable: void (*error_print_progname) (void)

     If the ‘error_print_progname’ variable is defined to a non-zero
     value the function pointed to is called by ‘error’ or
     ‘error_at_line’.  It is expected to print the program name or do
     something similarly useful.

     The function is expected to print to the ‘stderr’ stream and must
     be able to handle whatever orientation the stream has.

     The variable is global and shared by all threads.

 -- Variable: unsigned int error_message_count

     The ‘error_message_count’ variable is incremented whenever one of
     the functions ‘error’ or ‘error_at_line’ returns.  The variable is
     global and shared by all threads.

 -- Variable: int error_one_per_line

     The ‘error_one_per_line’ variable influences only ‘error_at_line’.
     Normally the ‘error_at_line’ function creates output for every
     invocation.  If ‘error_one_per_line’ is set to a non-zero value
     ‘error_at_line’ keeps track of the last file name and line number
     for which an error was reported and avoids directly following
     messages for the same file and line.  This variable is global and
     shared by all threads.

A program which read some input file and reports errors in it could look
like this:

     {
       char *line = NULL;
       size_t len = 0;
       unsigned int lineno = 0;

       error_message_count = 0;
       while (! feof_unlocked (fp))
         {
           ssize_t n = getline (&line, &len, fp);
           if (n <= 0)
             /* End of file or error.  */
             break;
           ++lineno;

           /* Process the line.  */
           ...

           if (Detect error in line)
             error_at_line (0, errval, filename, lineno,
                            "some error text %s", some_variable);
         }

       if (error_message_count != 0)
         error (EXIT_FAILURE, 0, "%u errors found", error_message_count);
     }

   ‘error’ and ‘error_at_line’ are clearly the functions of choice and
enable the programmer to write applications which follow the GNU coding
standard.  The GNU C Library additionally contains functions which are
used in BSD for the same purpose.  These functions are declared in
‘err.h’.  It is generally advised to not use these functions.  They are
included only for compatibility.

 -- Function: void warn (const char *FORMAT, ...)

     Preliminary: | MT-Safe locale | AS-Unsafe corrupt heap i18n |
     AC-Unsafe corrupt lock mem | *Note POSIX Safety Concepts::.

     The ‘warn’ function is roughly equivalent to a call like
            error (0, errno, format, the parameters)
     except that the global variables ‘error’ respects and modifies are
     not used.

 -- Function: void vwarn (const char *FORMAT, va_list AP)

     Preliminary: | MT-Safe locale | AS-Unsafe corrupt heap i18n |
     AC-Unsafe corrupt lock mem | *Note POSIX Safety Concepts::.

     The ‘vwarn’ function is just like ‘warn’ except that the parameters
     for the handling of the format string FORMAT are passed in as a
     value of type ‘va_list’.

 -- Function: void warnx (const char *FORMAT, ...)

     Preliminary: | MT-Safe locale | AS-Unsafe corrupt heap | AC-Unsafe
     corrupt lock mem | *Note POSIX Safety Concepts::.

     The ‘warnx’ function is roughly equivalent to a call like
            error (0, 0, format, the parameters)
     except that the global variables ‘error’ respects and modifies are
     not used.  The difference to ‘warn’ is that no error number string
     is printed.

 -- Function: void vwarnx (const char *FORMAT, va_list AP)

     Preliminary: | MT-Safe locale | AS-Unsafe corrupt heap | AC-Unsafe
     corrupt lock mem | *Note POSIX Safety Concepts::.

     The ‘vwarnx’ function is just like ‘warnx’ except that the
     parameters for the handling of the format string FORMAT are passed
     in as a value of type ‘va_list’.

 -- Function: void err (int STATUS, const char *FORMAT, ...)

     Preliminary: | MT-Safe locale | AS-Unsafe corrupt heap i18n |
     AC-Unsafe corrupt lock mem | *Note POSIX Safety Concepts::.

     The ‘err’ function is roughly equivalent to a call like
            error (status, errno, format, the parameters)
     except that the global variables ‘error’ respects and modifies are
     not used and that the program is exited even if STATUS is zero.

 -- Function: void verr (int STATUS, const char *FORMAT, va_list AP)

     Preliminary: | MT-Safe locale | AS-Unsafe corrupt heap i18n |
     AC-Unsafe corrupt lock mem | *Note POSIX Safety Concepts::.

     The ‘verr’ function is just like ‘err’ except that the parameters
     for the handling of the format string FORMAT are passed in as a
     value of type ‘va_list’.

 -- Function: void errx (int STATUS, const char *FORMAT, ...)

     Preliminary: | MT-Safe locale | AS-Unsafe corrupt heap | AC-Unsafe
     corrupt lock mem | *Note POSIX Safety Concepts::.

     The ‘errx’ function is roughly equivalent to a call like
            error (status, 0, format, the parameters)
     except that the global variables ‘error’ respects and modifies are
     not used and that the program is exited even if STATUS is zero.
     The difference to ‘err’ is that no error number string is printed.

 -- Function: void verrx (int STATUS, const char *FORMAT, va_list AP)

     Preliminary: | MT-Safe locale | AS-Unsafe corrupt heap | AC-Unsafe
     corrupt lock mem | *Note POSIX Safety Concepts::.

     The ‘verrx’ function is just like ‘errx’ except that the parameters
     for the handling of the format string FORMAT are passed in as a
     value of type ‘va_list’.


File: libc.info,  Node: Memory,  Next: Character Handling,  Prev: Error Reporting,  Up: Top

3 Virtual Memory Allocation And Paging
**************************************

This chapter describes how processes manage and use memory in a system
that uses the GNU C Library.

   The GNU C Library has several functions for dynamically allocating
virtual memory in various ways.  They vary in generality and in
efficiency.  The library also provides functions for controlling paging
and allocation of real memory.

* Menu:

* Memory Concepts::             An introduction to concepts and terminology.
* Memory Allocation::           Allocating storage for your program data
* Resizing the Data Segment::   ‘brk’, ‘sbrk’
* Memory Protection::           Controlling access to memory regions.
* Locking Pages::               Preventing page faults

   Memory mapped I/O is not discussed in this chapter.  *Note
Memory-mapped I/O::.


File: libc.info,  Node: Memory Concepts,  Next: Memory Allocation,  Up: Memory

3.1 Process Memory Concepts
===========================

One of the most basic resources a process has available to it is memory.
There are a lot of different ways systems organize memory, but in a
typical one, each process has one linear virtual address space, with
addresses running from zero to some huge maximum.  It need not be
contiguous; i.e., not all of these addresses actually can be used to
store data.

   The virtual memory is divided into pages (4 kilobytes is typical).
Backing each page of virtual memory is a page of real memory (called a
“frame”) or some secondary storage, usually disk space.  The disk space
might be swap space or just some ordinary disk file.  Actually, a page
of all zeroes sometimes has nothing at all backing it – there’s just a
flag saying it is all zeroes.

   The same frame of real memory or backing store can back multiple
virtual pages belonging to multiple processes.  This is normally the
case, for example, with virtual memory occupied by GNU C Library code.
The same real memory frame containing the ‘printf’ function backs a
virtual memory page in each of the existing processes that has a
‘printf’ call in its program.

   In order for a program to access any part of a virtual page, the page
must at that moment be backed by (“connected to”) a real frame.  But
because there is usually a lot more virtual memory than real memory, the
pages must move back and forth between real memory and backing store
regularly, coming into real memory when a process needs to access them
and then retreating to backing store when not needed anymore.  This
movement is called “paging”.

   When a program attempts to access a page which is not at that moment
backed by real memory, this is known as a “page fault”.  When a page
fault occurs, the kernel suspends the process, places the page into a
real page frame (this is called “paging in” or “faulting in”), then
resumes the process so that from the process’ point of view, the page
was in real memory all along.  In fact, to the process, all pages always
seem to be in real memory.  Except for one thing: the elapsed execution
time of an instruction that would normally be a few nanoseconds is
suddenly much, much, longer (because the kernel normally has to do I/O
to complete the page-in).  For programs sensitive to that, the functions
described in *note Locking Pages:: can control it.

   Within each virtual address space, a process has to keep track of
what is at which addresses, and that process is called memory
allocation.  Allocation usually brings to mind meting out scarce
resources, but in the case of virtual memory, that’s not a major goal,
because there is generally much more of it than anyone needs.  Memory
allocation within a process is mainly just a matter of making sure that
the same byte of memory isn’t used to store two different things.

   Processes allocate memory in two major ways: by exec and
programmatically.  Actually, forking is a third way, but it’s not very
interesting.  *Note Creating a Process::.

   Exec is the operation of creating a virtual address space for a
process, loading its basic program into it, and executing the program.
It is done by the “exec” family of functions (e.g.  ‘execl’).  The
operation takes a program file (an executable), it allocates space to
load all the data in the executable, loads it, and transfers control to
it.  That data is most notably the instructions of the program (the
“text”), but also literals and constants in the program and even some
variables: C variables with the static storage class (*note Memory
Allocation and C::).

   Once that program begins to execute, it uses programmatic allocation
to gain additional memory.  In a C program with the GNU C Library, there
are two kinds of programmatic allocation: automatic and dynamic.  *Note
Memory Allocation and C::.

   Memory-mapped I/O is another form of dynamic virtual memory
allocation.  Mapping memory to a file means declaring that the contents
of certain range of a process’ addresses shall be identical to the
contents of a specified regular file.  The system makes the virtual
memory initially contain the contents of the file, and if you modify the
memory, the system writes the same modification to the file.  Note that
due to the magic of virtual memory and page faults, there is no reason
for the system to do I/O to read the file, or allocate real memory for
its contents, until the program accesses the virtual memory.  *Note
Memory-mapped I/O::.

   Just as it programmatically allocates memory, the program can
programmatically deallocate (“free”) it.  You can’t free the memory that
was allocated by exec.  When the program exits or execs, you might say
that all its memory gets freed, but since in both cases the address
space ceases to exist, the point is really moot.  *Note Program
Termination::.

   A process’ virtual address space is divided into segments.  A segment
is a contiguous range of virtual addresses.  Three important segments
are:

   • 
     The “text segment” contains a program’s instructions and literals
     and static constants.  It is allocated by exec and stays the same
     size for the life of the virtual address space.

   • The “data segment” is working storage for the program.  It can be
     preallocated and preloaded by exec and the process can extend or
     shrink it by calling functions as described in *Note Resizing the
     Data Segment::.  Its lower end is fixed.

   • The “stack segment” contains a program stack.  It grows as the
     stack grows, but doesn’t shrink when the stack shrinks.


File: libc.info,  Node: Memory Allocation,  Next: Resizing the Data Segment,  Prev: Memory Concepts,  Up: Memory

3.2 Allocating Storage For Program Data
=======================================

This section covers how ordinary programs manage storage for their data,
including the famous ‘malloc’ function and some fancier facilities
special to the GNU C Library and GNU Compiler.

* Menu:

* Memory Allocation and C::     How to get different kinds of allocation in C.
* The GNU Allocator::		An overview of the GNU ‘malloc’
				implementation.
* Unconstrained Allocation::    The ‘malloc’ facility allows fully general
		 		 dynamic allocation.
* Allocation Debugging::        Finding memory leaks and not freed memory.
* Replacing malloc::            Using your own ‘malloc’-style allocator.
* Obstacks::                    Obstacks are less general than malloc
				 but more efficient and convenient.
* Variable Size Automatic::     Allocation of variable-sized blocks
				 of automatic storage that are freed when the
				 calling function returns.


File: libc.info,  Node: Memory Allocation and C,  Next: The GNU Allocator,  Up: Memory Allocation

3.2.1 Memory Allocation in C Programs
-------------------------------------

The C language supports two kinds of memory allocation through the
variables in C programs:

   • “Static allocation” is what happens when you declare a static or
     global variable.  Each static or global variable defines one block
     of space, of a fixed size.  The space is allocated once, when your
     program is started (part of the exec operation), and is never
     freed.

   • “Automatic allocation” happens when you declare an automatic
     variable, such as a function argument or a local variable.  The
     space for an automatic variable is allocated when the compound
     statement containing the declaration is entered, and is freed when
     that compound statement is exited.

     In GNU C, the size of the automatic storage can be an expression
     that varies.  In other C implementations, it must be a constant.

   A third important kind of memory allocation, “dynamic allocation”, is
not supported by C variables but is available via GNU C Library
functions.

3.2.1.1 Dynamic Memory Allocation
.................................

“Dynamic memory allocation” is a technique in which programs determine
as they are running where to store some information.  You need dynamic
allocation when the amount of memory you need, or how long you continue
to need it, depends on factors that are not known before the program
runs.

   For example, you may need a block to store a line read from an input
file; since there is no limit to how long a line can be, you must
allocate the memory dynamically and make it dynamically larger as you
read more of the line.

   Or, you may need a block for each record or each definition in the
input data; since you can’t know in advance how many there will be, you
must allocate a new block for each record or definition as you read it.

   When you use dynamic allocation, the allocation of a block of memory
is an action that the program requests explicitly.  You call a function
or macro when you want to allocate space, and specify the size with an
argument.  If you want to free the space, you do so by calling another
function or macro.  You can do these things whenever you want, as often
as you want.

   Dynamic allocation is not supported by C variables; there is no
storage class “dynamic”, and there can never be a C variable whose value
is stored in dynamically allocated space.  The only way to get
dynamically allocated memory is via a system call (which is generally
via a GNU C Library function call), and the only way to refer to
dynamically allocated space is through a pointer.  Because it is less
convenient, and because the actual process of dynamic allocation
requires more computation time, programmers generally use dynamic
allocation only when neither static nor automatic allocation will serve.

   For example, if you want to allocate dynamically some space to hold a
‘struct foobar’, you cannot declare a variable of type ‘struct foobar’
whose contents are the dynamically allocated space.  But you can declare
a variable of pointer type ‘struct foobar *’ and assign it the address
of the space.  Then you can use the operators ‘*’ and ‘->’ on this
pointer variable to refer to the contents of the space:

     {
       struct foobar *ptr
          = (struct foobar *) malloc (sizeof (struct foobar));
       ptr->name = x;
       ptr->next = current_foobar;
       current_foobar = ptr;
     }


File: libc.info,  Node: The GNU Allocator,  Next: Unconstrained Allocation,  Prev: Memory Allocation and C,  Up: Memory Allocation

3.2.2 The GNU Allocator
-----------------------

The ‘malloc’ implementation in the GNU C Library is derived from
ptmalloc (pthreads malloc), which in turn is derived from dlmalloc (Doug
Lea malloc).  This malloc may allocate memory in two different ways
depending on their size and certain parameters that may be controlled by
users.  The most common way is to allocate portions of memory (called
chunks) from a large contiguous area of memory and manage these areas to
optimize their use and reduce wastage in the form of unusable chunks.
Traditionally the system heap was set up to be the one large memory area
but the GNU C Library ‘malloc’ implementation maintains multiple such
areas to optimize their use in multi-threaded applications.  Each such
area is internally referred to as an “arena”.

   As opposed to other versions, the ‘malloc’ in the GNU C Library does
not round up chunk sizes to powers of two, neither for large nor for
small sizes.  Neighboring chunks can be coalesced on a ‘free’ no matter
what their size is.  This makes the implementation suitable for all
kinds of allocation patterns without generally incurring high memory
waste through fragmentation.  The presence of multiple arenas allows
multiple threads to allocate memory simultaneously in separate arenas,
thus improving performance.

   The other way of memory allocation is for very large blocks, i.e.
much larger than a page.  These requests are allocated with ‘mmap’
(anonymous or via ‘/dev/zero’; *note Memory-mapped I/O::)).  This has
the great advantage that these chunks are returned to the system
immediately when they are freed.  Therefore, it cannot happen that a
large chunk becomes “locked” in between smaller ones and even after
calling ‘free’ wastes memory.  The size threshold for ‘mmap’ to be used
is dynamic and gets adjusted according to allocation patterns of the
program.  ‘mallopt’ can be used to statically adjust the threshold using
‘M_MMAP_THRESHOLD’ and the use of ‘mmap’ can be disabled completely with
‘M_MMAP_MAX’; *note Malloc Tunable Parameters::.

   A more detailed technical description of the GNU Allocator is
maintained in the GNU C Library wiki.  See
<https://sourceware.org/glibc/wiki/MallocInternals>.

   It is possible to use your own custom ‘malloc’ instead of the
built-in allocator provided by the GNU C Library.  *Note Replacing
malloc::.


File: libc.info,  Node: Unconstrained Allocation,  Next: Allocation Debugging,  Prev: The GNU Allocator,  Up: Memory Allocation

3.2.3 Unconstrained Allocation
------------------------------

The most general dynamic allocation facility is ‘malloc’.  It allows you
to allocate blocks of memory of any size at any time, make them bigger
or smaller at any time, and free the blocks individually at any time (or
never).

* Menu:

* Basic Allocation::            Simple use of ‘malloc’.
* Malloc Examples::             Examples of ‘malloc’.  ‘xmalloc’.
* Freeing after Malloc::        Use ‘free’ to free a block you
				 got with ‘malloc’.
* Changing Block Size::         Use ‘realloc’ to make a block
				 bigger or smaller.
* Allocating Cleared Space::    Use ‘calloc’ to allocate a
				 block and clear it.
* Aligned Memory Blocks::       Allocating specially aligned memory.
* Malloc Tunable Parameters::   Use ‘mallopt’ to adjust allocation
                                 parameters.
* Heap Consistency Checking::   Automatic checking for errors.
* Hooks for Malloc::            You can use these hooks for debugging
				 programs that use ‘malloc’.
* Statistics of Malloc::        Getting information about how much
				 memory your program is using.
* Summary of Malloc::           Summary of ‘malloc’ and related functions.


File: libc.info,  Node: Basic Allocation,  Next: Malloc Examples,  Up: Unconstrained Allocation

3.2.3.1 Basic Memory Allocation
...............................

To allocate a block of memory, call ‘malloc’.  The prototype for this
function is in ‘stdlib.h’.

 -- Function: void * malloc (size_t SIZE)

     Preliminary: | MT-Safe | AS-Unsafe lock | AC-Unsafe lock fd mem |
     *Note POSIX Safety Concepts::.

     This function returns a pointer to a newly allocated block SIZE
     bytes long, or a null pointer if the block could not be allocated.

   The contents of the block are undefined; you must initialize it
yourself (or use ‘calloc’ instead; *note Allocating Cleared Space::).
Normally you would cast the value as a pointer to the kind of object
that you want to store in the block.  Here we show an example of doing
so, and of initializing the space with zeros using the library function
‘memset’ (*note Copying Strings and Arrays::):

     struct foo *ptr;
     ...
     ptr = (struct foo *) malloc (sizeof (struct foo));
     if (ptr == 0) abort ();
     memset (ptr, 0, sizeof (struct foo));

   You can store the result of ‘malloc’ into any pointer variable
without a cast, because ISO C automatically converts the type ‘void *’
to another type of pointer when necessary.  But the cast is necessary in
contexts other than assignment operators or if you might want your code
to run in traditional C.

   Remember that when allocating space for a string, the argument to
‘malloc’ must be one plus the length of the string.  This is because a
string is terminated with a null character that doesn’t count in the
“length” of the string but does need space.  For example:

     char *ptr;
     ...
     ptr = (char *) malloc (length + 1);

*Note Representation of Strings::, for more information about this.


File: libc.info,  Node: Malloc Examples,  Next: Freeing after Malloc,  Prev: Basic Allocation,  Up: Unconstrained Allocation

3.2.3.2 Examples of ‘malloc’
............................

If no more space is available, ‘malloc’ returns a null pointer.  You
should check the value of _every_ call to ‘malloc’.  It is useful to
write a subroutine that calls ‘malloc’ and reports an error if the value
is a null pointer, returning only if the value is nonzero.  This
function is conventionally called ‘xmalloc’.  Here it is:

     void *
     xmalloc (size_t size)
     {
       void *value = malloc (size);
       if (value == 0)
         fatal ("virtual memory exhausted");
       return value;
     }

   Here is a real example of using ‘malloc’ (by way of ‘xmalloc’).  The
function ‘savestring’ will copy a sequence of characters into a newly
allocated null-terminated string:

     char *
     savestring (const char *ptr, size_t len)
     {
       char *value = (char *) xmalloc (len + 1);
       value[len] = '\0';
       return (char *) memcpy (value, ptr, len);
     }

   The block that ‘malloc’ gives you is guaranteed to be aligned so that
it can hold any type of data.  On GNU systems, the address is always a
multiple of eight on 32-bit systems, and a multiple of 16 on 64-bit
systems.  Only rarely is any higher boundary (such as a page boundary)
necessary; for those cases, use ‘aligned_alloc’ or ‘posix_memalign’
(*note Aligned Memory Blocks::).

   Note that the memory located after the end of the block is likely to
be in use for something else; perhaps a block already allocated by
another call to ‘malloc’.  If you attempt to treat the block as longer
than you asked for it to be, you are liable to destroy the data that
‘malloc’ uses to keep track of its blocks, or you may destroy the
contents of another block.  If you have already allocated a block and
discover you want it to be bigger, use ‘realloc’ (*note Changing Block
Size::).


File: libc.info,  Node: Freeing after Malloc,  Next: Changing Block Size,  Prev: Malloc Examples,  Up: Unconstrained Allocation

******* Freeing Memory Allocated with ‘malloc’
..............................................

When you no longer need a block that you got with ‘malloc’, use the
function ‘free’ to make the block available to be allocated again.  The
prototype for this function is in ‘stdlib.h’.

 -- Function: void free (void *PTR)

     Preliminary: | MT-Safe | AS-Unsafe lock | AC-Unsafe lock fd mem |
     *Note POSIX Safety Concepts::.

     The ‘free’ function deallocates the block of memory pointed at by
     PTR.

   Freeing a block alters the contents of the block.  *Do not expect to
find any data (such as a pointer to the next block in a chain of blocks)
in the block after freeing it.*  Copy whatever you need out of the block
before freeing it!  Here is an example of the proper way to free all the
blocks in a chain, and the strings that they point to:

     struct chain
       {
         struct chain *next;
         char *name;
       }

     void
     free_chain (struct chain *chain)
     {
       while (chain != 0)
         {
           struct chain *next = chain->next;
           free (chain->name);
           free (chain);
           chain = next;
         }
     }

   Occasionally, ‘free’ can actually return memory to the operating
system and make the process smaller.  Usually, all it can do is allow a
later call to ‘malloc’ to reuse the space.  In the meantime, the space
remains in your program as part of a free-list used internally by
‘malloc’.

   The ‘free’ function preserves the value of ‘errno’, so that cleanup
code need not worry about saving and restoring ‘errno’ around a call to
‘free’.  Although neither ISO C nor POSIX.1-2017 requires ‘free’ to
preserve ‘errno’, a future version of POSIX is planned to require it.

   There is no point in freeing blocks at the end of a program, because
all of the program’s space is given back to the system when the process
terminates.


File: libc.info,  Node: Changing Block Size,  Next: Allocating Cleared Space,  Prev: Freeing after Malloc,  Up: Unconstrained Allocation

3.2.3.4 Changing the Size of a Block
....................................

Often you do not know for certain how big a block you will ultimately
need at the time you must begin to use the block.  For example, the
block might be a buffer that you use to hold a line being read from a
file; no matter how long you make the buffer initially, you may
encounter a line that is longer.

   You can make the block longer by calling ‘realloc’ or ‘reallocarray’.
These functions are declared in ‘stdlib.h’.

 -- Function: void * realloc (void *PTR, size_t NEWSIZE)

     Preliminary: | MT-Safe | AS-Unsafe lock | AC-Unsafe lock fd mem |
     *Note POSIX Safety Concepts::.

     The ‘realloc’ function changes the size of the block whose address
     is PTR to be NEWSIZE.

     Since the space after the end of the block may be in use, ‘realloc’
     may find it necessary to copy the block to a new address where more
     free space is available.  The value of ‘realloc’ is the new address
     of the block.  If the block needs to be moved, ‘realloc’ copies the
     old contents.

     If you pass a null pointer for PTR, ‘realloc’ behaves just like
     ‘malloc (NEWSIZE)’.  This can be convenient, but beware that older
     implementations (before ISO C) may not support this behavior, and
     will probably crash when ‘realloc’ is passed a null pointer.

 -- Function: void * reallocarray (void *PTR, size_t NMEMB, size_t SIZE)

     Preliminary: | MT-Safe | AS-Unsafe lock | AC-Unsafe lock fd mem |
     *Note POSIX Safety Concepts::.

     The ‘reallocarray’ function changes the size of the block whose
     address is PTR to be long enough to contain a vector of NMEMB
     elements, each of size SIZE.  It is equivalent to ‘realloc (PTR,
     NMEMB * SIZE)’, except that ‘reallocarray’ fails safely if the
     multiplication overflows, by setting ‘errno’ to ‘ENOMEM’, returning
     a null pointer, and leaving the original block unchanged.

     ‘reallocarray’ should be used instead of ‘realloc’ when the new
     size of the allocated block is the result of a multiplication that
     might overflow.

     *Portability Note:* This function is not part of any standard.  It
     was first introduced in OpenBSD 5.6.

   Like ‘malloc’, ‘realloc’ and ‘reallocarray’ may return a null pointer
if no memory space is available to make the block bigger.  When this
happens, the original block is untouched; it has not been modified or
relocated.

   In most cases it makes no difference what happens to the original
block when ‘realloc’ fails, because the application program cannot
continue when it is out of memory, and the only thing to do is to give a
fatal error message.  Often it is convenient to write and use a
subroutine, conventionally called ‘xrealloc’, that takes care of the
error message as ‘xmalloc’ does for ‘malloc’:

     void *
     xrealloc (void *ptr, size_t size)
     {
       void *value = realloc (ptr, size);
       if (value == 0)
         fatal ("Virtual memory exhausted");
       return value;
     }

   You can also use ‘realloc’ or ‘reallocarray’ to make a block smaller.
The reason you would do this is to avoid tying up a lot of memory space
when only a little is needed.  In several allocation implementations,
making a block smaller sometimes necessitates copying it, so it can fail
if no other space is available.

   If the new size you specify is the same as the old size, ‘realloc’
and ‘reallocarray’ are guaranteed to change nothing and return the same
address that you gave.


File: libc.info,  Node: Allocating Cleared Space,  Next: Aligned Memory Blocks,  Prev: Changing Block Size,  Up: Unconstrained Allocation

******* Allocating Cleared Space
................................

The function ‘calloc’ allocates memory and clears it to zero.  It is
declared in ‘stdlib.h’.

 -- Function: void * calloc (size_t COUNT, size_t ELTSIZE)

     Preliminary: | MT-Safe | AS-Unsafe lock | AC-Unsafe lock fd mem |
     *Note POSIX Safety Concepts::.

     This function allocates a block long enough to contain a vector of
     COUNT elements, each of size ELTSIZE.  Its contents are cleared to
     zero before ‘calloc’ returns.

   You could define ‘calloc’ as follows:

     void *
     calloc (size_t count, size_t eltsize)
     {
       size_t size = count * eltsize;
       void *value = malloc (size);
       if (value != 0)
         memset (value, 0, size);
       return value;
     }

   But in general, it is not guaranteed that ‘calloc’ calls ‘malloc’
internally.  Therefore, if an application provides its own
‘malloc’/‘realloc’/‘free’ outside the C library, it should always define
‘calloc’, too.


File: libc.info,  Node: Aligned Memory Blocks,  Next: Malloc Tunable Parameters,  Prev: Allocating Cleared Space,  Up: Unconstrained Allocation

******* Allocating Aligned Memory Blocks
........................................

The address of a block returned by ‘malloc’ or ‘realloc’ in GNU systems
is always a multiple of eight (or sixteen on 64-bit systems).  If you
need a block whose address is a multiple of a higher power of two than
that, use ‘aligned_alloc’ or ‘posix_memalign’.  ‘aligned_alloc’ and
‘posix_memalign’ are declared in ‘stdlib.h’.

 -- Function: void * aligned_alloc (size_t ALIGNMENT, size_t SIZE)

     Preliminary: | MT-Safe | AS-Unsafe lock | AC-Unsafe lock fd mem |
     *Note POSIX Safety Concepts::.

     The ‘aligned_alloc’ function allocates a block of SIZE bytes whose
     address is a multiple of ALIGNMENT.  The ALIGNMENT must be a power
     of two and SIZE must be a multiple of ALIGNMENT.

     The ‘aligned_alloc’ function returns a null pointer on error and
     sets ‘errno’ to one of the following values:

     ‘ENOMEM’
          There was insufficient memory available to satisfy the
          request.

     ‘EINVAL’
          ALIGNMENT is not a power of two.

          This function was introduced in ISO C11 and hence may have
          better portability to modern non-POSIX systems than
          ‘posix_memalign’.

 -- Function: void * memalign (size_t BOUNDARY, size_t SIZE)

     Preliminary: | MT-Safe | AS-Unsafe lock | AC-Unsafe lock fd mem |
     *Note POSIX Safety Concepts::.

     The ‘memalign’ function allocates a block of SIZE bytes whose
     address is a multiple of BOUNDARY.  The BOUNDARY must be a power of
     two!  The function ‘memalign’ works by allocating a somewhat larger
     block, and then returning an address within the block that is on
     the specified boundary.

     The ‘memalign’ function returns a null pointer on error and sets
     ‘errno’ to one of the following values:

     ‘ENOMEM’
          There was insufficient memory available to satisfy the
          request.

     ‘EINVAL’
          BOUNDARY is not a power of two.

     The ‘memalign’ function is obsolete and ‘aligned_alloc’ or
     ‘posix_memalign’ should be used instead.

 -- Function: int posix_memalign (void **MEMPTR, size_t ALIGNMENT,
          size_t SIZE)

     Preliminary: | MT-Safe | AS-Unsafe lock | AC-Unsafe lock fd mem |
     *Note POSIX Safety Concepts::.

     The ‘posix_memalign’ function is similar to the ‘memalign’ function
     in that it returns a buffer of SIZE bytes aligned to a multiple of
     ALIGNMENT.  But it adds one requirement to the parameter ALIGNMENT:
     the value must be a power of two multiple of ‘sizeof (void *)’.

     If the function succeeds in allocation memory a pointer to the
     allocated memory is returned in ‘*MEMPTR’ and the return value is
     zero.  Otherwise the function returns an error value indicating the
     problem.  The possible error values returned are:

     ‘ENOMEM’
          There was insufficient memory available to satisfy the
          request.

     ‘EINVAL’
          ALIGNMENT is not a power of two multiple of ‘sizeof (void *)’.

     This function was introduced in POSIX 1003.1d.  Although this
     function is superseded by ‘aligned_alloc’, it is more portable to
     older POSIX systems that do not support ISO C11.

 -- Function: void * valloc (size_t SIZE)

     Preliminary: | MT-Unsafe init | AS-Unsafe init lock | AC-Unsafe
     init lock fd mem | *Note POSIX Safety Concepts::.

     Using ‘valloc’ is like using ‘memalign’ and passing the page size
     as the value of the first argument.  It is implemented like this:

          void *
          valloc (size_t size)
          {
            return memalign (getpagesize (), size);
          }

     *note Query Memory Parameters:: for more information about the
     memory subsystem.

     The ‘valloc’ function is obsolete and ‘aligned_alloc’ or
     ‘posix_memalign’ should be used instead.


File: libc.info,  Node: Malloc Tunable Parameters,  Next: Heap Consistency Checking,  Prev: Aligned Memory Blocks,  Up: Unconstrained Allocation

3.2.3.7 Malloc Tunable Parameters
.................................

You can adjust some parameters for dynamic memory allocation with the
‘mallopt’ function.  This function is the general SVID/XPG interface,
defined in ‘malloc.h’.

 -- Function: int mallopt (int PARAM, int VALUE)
     Preliminary: | MT-Unsafe init const:mallopt | AS-Unsafe init lock |
     AC-Unsafe init lock | *Note POSIX Safety Concepts::.

     When calling ‘mallopt’, the PARAM argument specifies the parameter
     to be set, and VALUE the new value to be set.  Possible choices for
     PARAM, as defined in ‘malloc.h’, are:

     ‘M_MMAP_MAX’
          The maximum number of chunks to allocate with ‘mmap’.  Setting
          this to zero disables all use of ‘mmap’.

          The default value of this parameter is ‘65536’.

          This parameter can also be set for the process at startup by
          setting the environment variable ‘MALLOC_MMAP_MAX_’ to the
          desired value.

     ‘M_MMAP_THRESHOLD’
          All chunks larger than this value are allocated outside the
          normal heap, using the ‘mmap’ system call.  This way it is
          guaranteed that the memory for these chunks can be returned to
          the system on ‘free’.  Note that requests smaller than this
          threshold might still be allocated via ‘mmap’.

          If this parameter is not set, the default value is set as 128
          KiB and the threshold is adjusted dynamically to suit the
          allocation patterns of the program.  If the parameter is set,
          the dynamic adjustment is disabled and the value is set
          statically to the input value.

          This parameter can also be set for the process at startup by
          setting the environment variable ‘MALLOC_MMAP_THRESHOLD_’ to
          the desired value.

     ‘M_PERTURB’
          If non-zero, memory blocks are filled with values depending on
          some low order bits of this parameter when they are allocated
          (except when allocated by ‘calloc’) and freed.  This can be
          used to debug the use of uninitialized or freed heap memory.
          Note that this option does not guarantee that the freed block
          will have any specific values.  It only guarantees that the
          content the block had before it was freed will be overwritten.

          The default value of this parameter is ‘0’.

          This parameter can also be set for the process at startup by
          setting the environment variable ‘MALLOC_PERTURB_’ to the
          desired value.

     ‘M_TOP_PAD’
          This parameter determines the amount of extra memory to obtain
          from the system when an arena needs to be extended.  It also
          specifies the number of bytes to retain when shrinking an
          arena.  This provides the necessary hysteresis in heap size
          such that excessive amounts of system calls can be avoided.

          The default value of this parameter is ‘0’.

          This parameter can also be set for the process at startup by
          setting the environment variable ‘MALLOC_TOP_PAD_’ to the
          desired value.

     ‘M_TRIM_THRESHOLD’
          This is the minimum size (in bytes) of the top-most,
          releasable chunk that will trigger a system call in order to
          return memory to the system.

          If this parameter is not set, the default value is set as 128
          KiB and the threshold is adjusted dynamically to suit the
          allocation patterns of the program.  If the parameter is set,
          the dynamic adjustment is disabled and the value is set
          statically to the provided input.

          This parameter can also be set for the process at startup by
          setting the environment variable ‘MALLOC_TRIM_THRESHOLD_’ to
          the desired value.

     ‘M_ARENA_TEST’
          This parameter specifies the number of arenas that can be
          created before the test on the limit to the number of arenas
          is conducted.  The value is ignored if ‘M_ARENA_MAX’ is set.

          The default value of this parameter is 2 on 32-bit systems and
          8 on 64-bit systems.

          This parameter can also be set for the process at startup by
          setting the environment variable ‘MALLOC_ARENA_TEST’ to the
          desired value.

     ‘M_ARENA_MAX’
          This parameter sets the number of arenas to use regardless of
          the number of cores in the system.

          The default value of this tunable is ‘0’, meaning that the
          limit on the number of arenas is determined by the number of
          CPU cores online.  For 32-bit systems the limit is twice the
          number of cores online and on 64-bit systems, it is eight
          times the number of cores online.  Note that the default value
          is not derived from the default value of M_ARENA_TEST and is
          computed independently.

          This parameter can also be set for the process at startup by
          setting the environment variable ‘MALLOC_ARENA_MAX’ to the
          desired value.


File: libc.info,  Node: Heap Consistency Checking,  Next: Hooks for Malloc,  Prev: Malloc Tunable Parameters,  Up: Unconstrained Allocation

3.2.3.8 Heap Consistency Checking
.................................

You can ask ‘malloc’ to check the consistency of dynamic memory by using
the ‘mcheck’ function.  This function is a GNU extension, declared in
‘mcheck.h’.

 -- Function: int mcheck (void (*ABORTFN) (enum mcheck_status STATUS))

     Preliminary: | MT-Unsafe race:mcheck const:malloc_hooks | AS-Unsafe
     corrupt | AC-Unsafe corrupt | *Note POSIX Safety Concepts::.

     Calling ‘mcheck’ tells ‘malloc’ to perform occasional consistency
     checks.  These will catch things such as writing past the end of a
     block that was allocated with ‘malloc’.

     The ABORTFN argument is the function to call when an inconsistency
     is found.  If you supply a null pointer, then ‘mcheck’ uses a
     default function which prints a message and calls ‘abort’ (*note
     Aborting a Program::).  The function you supply is called with one
     argument, which says what sort of inconsistency was detected; its
     type is described below.

     It is too late to begin allocation checking once you have allocated
     anything with ‘malloc’.  So ‘mcheck’ does nothing in that case.
     The function returns ‘-1’ if you call it too late, and ‘0’
     otherwise (when it is successful).

     The easiest way to arrange to call ‘mcheck’ early enough is to use
     the option ‘-lmcheck’ when you link your program; then you don’t
     need to modify your program source at all.  Alternatively you might
     use a debugger to insert a call to ‘mcheck’ whenever the program is
     started, for example these gdb commands will automatically call
     ‘mcheck’ whenever the program starts:

          (gdb) break main
          Breakpoint 1, main (argc=2, argv=0xbffff964) at whatever.c:10
          (gdb) command 1
          Type commands for when breakpoint 1 is hit, one per line.
          End with a line saying just "end".
          >call mcheck(0)
          >continue
          >end
          (gdb) ...

     This will however only work if no initialization function of any
     object involved calls any of the ‘malloc’ functions since ‘mcheck’
     must be called before the first such function.

 -- Function: enum mcheck_status mprobe (void *POINTER)
     Preliminary: | MT-Unsafe race:mcheck const:malloc_hooks | AS-Unsafe
     corrupt | AC-Unsafe corrupt | *Note POSIX Safety Concepts::.

     The ‘mprobe’ function lets you explicitly check for inconsistencies
     in a particular allocated block.  You must have already called
     ‘mcheck’ at the beginning of the program, to do its occasional
     checks; calling ‘mprobe’ requests an additional consistency check
     to be done at the time of the call.

     The argument POINTER must be a pointer returned by ‘malloc’ or
     ‘realloc’.  ‘mprobe’ returns a value that says what inconsistency,
     if any, was found.  The values are described below.

 -- Data Type: enum mcheck_status
     This enumerated type describes what kind of inconsistency was
     detected in an allocated block, if any.  Here are the possible
     values:

     ‘MCHECK_DISABLED’
          ‘mcheck’ was not called before the first allocation.  No
          consistency checking can be done.
     ‘MCHECK_OK’
          No inconsistency detected.
     ‘MCHECK_HEAD’
          The data immediately before the block was modified.  This
          commonly happens when an array index or pointer is decremented
          too far.
     ‘MCHECK_TAIL’
          The data immediately after the block was modified.  This
          commonly happens when an array index or pointer is incremented
          too far.
     ‘MCHECK_FREE’
          The block was already freed.

   Another possibility to check for and guard against bugs in the use of
‘malloc’, ‘realloc’ and ‘free’ is to set the environment variable
‘MALLOC_CHECK_’.  When ‘MALLOC_CHECK_’ is set to a non-zero value, a
special (less efficient) implementation is used which is designed to be
tolerant against simple errors, such as double calls of ‘free’ with the
same argument, or overruns of a single byte (off-by-one bugs).  Not all
such errors can be protected against, however, and memory leaks can
result.

   Any detected heap corruption results in immediate termination of the
process.

   There is one problem with ‘MALLOC_CHECK_’: in SUID or SGID binaries
it could possibly be exploited since diverging from the normal programs
behavior it now writes something to the standard error descriptor.
Therefore the use of ‘MALLOC_CHECK_’ is disabled by default for SUID and
SGID binaries.  It can be enabled again by the system administrator by
adding a file ‘/etc/suid-debug’ (the content is not important it could
be empty).

   So, what’s the difference between using ‘MALLOC_CHECK_’ and linking
with ‘-lmcheck’?  ‘MALLOC_CHECK_’ is orthogonal with respect to
‘-lmcheck’.  ‘-lmcheck’ has been added for backward compatibility.  Both
‘MALLOC_CHECK_’ and ‘-lmcheck’ should uncover the same bugs - but using
‘MALLOC_CHECK_’ you don’t need to recompile your application.

